import Icon from '@antscorp/icons';
import { Dropdown } from 'antd';
import produce from 'immer';
import React, { FC } from 'react';
import { updateObjAccessInfo } from '../../actions';
import { GENERAL_ACCESS, PUBLIC_ROLE, ROLE_ACCESS_LABEL } from '../../constants';
import { useShareAccess } from '../../hooks';
import { ShareAccessProps } from '../../types';
import { StyledGeneralAccessRoot } from './styled';

type GeneralAccessProps = Pick<ShareAccessProps, 'generalAccessSettings'>;

export const GeneralAccess: FC<GeneralAccessProps> = ({ generalAccessSettings }) => {
  const { state, dispatch } = useShareAccess();

  const { accessInfo, allowEdit } = state;

  if (!accessInfo) return null;

  const { isPublic, publicRole } = accessInfo;
  const isShowOnlyText = !!generalAccessSettings?.publicOnlyWith;

  const handleChangeGeneralAccess = (value: number) => {
    if (!accessInfo || !dispatch || !allowEdit) return;

    const updated = produce(accessInfo, draft => {
      draft.isPublic = value;

      if (!isPublic && value === GENERAL_ACCESS.PUBLIC) {
        draft.publicRole = PUBLIC_ROLE.ONLY_VIEWER;
      }
    });

    dispatch(updateObjAccessInfo({ updated }));
  };

  const handleChangePublicRole = (value: number) => {
    if (!accessInfo || !dispatch || !allowEdit) return;

    const updated = produce(accessInfo, draft => {
      draft.publicRole = value;
    });

    dispatch(updateObjAccessInfo({ updated }));
  };

  const renderDescription = () => {
    let description = 'Only people with access can open this item';

    if (isPublic && publicRole === PUBLIC_ROLE.ONLY_VIEWER) {
      description = 'Anyone on the portal can view this item';
    }

    if (isPublic && publicRole === PUBLIC_ROLE.ONLY_EDITOR) {
      description = 'Anyone on the portal can edit this item';
    }

    return description;
  };

  return (
    <StyledGeneralAccessRoot>
      <div className="title">General access</div>

      <div className="setting">
        <Icon
          className="accessable-icon"
          type={isPublic ? 'icon-ants-mui-lock-open' : 'icon-ants-mui-lock'}
        />

        <div className="general-info">
          <Dropdown
            placement="bottomLeft"
            trigger={['click']}
            overlayStyle={{ zIndex: 3002 }}
            destroyPopupOnHide
            disabled={!allowEdit}
            menu={{
              items: [
                isPublic
                  ? { key: GENERAL_ACCESS.RESTRICTED, label: <div>Restricted </div> }
                  : { key: GENERAL_ACCESS.PUBLIC, label: <div>Public</div> },
              ],
              onClick: v => handleChangeGeneralAccess(+v.key),
            }}
          >
            <div className="general-dropdown-wrapper">
              <div className="accessable-title">{isPublic ? 'Public' : 'Restricted'}</div>

              {allowEdit && (
                <Icon
                  className="icon-angle"
                  type="icon-ants-expand-more"
                  style={{ fontSize: 20 }}
                />
              )}
            </div>
          </Dropdown>

          <div className="description">{renderDescription()}</div>
        </div>

        {!!isPublic && publicRole && (
          <Dropdown
            placement="bottomRight"
            trigger={['click']}
            destroyPopupOnHide
            disabled={!allowEdit || isShowOnlyText}
            overlayStyle={{ zIndex: 3002 }}
            menu={{
              items: [
                publicRole === PUBLIC_ROLE.ONLY_VIEWER
                  ? {
                      key: PUBLIC_ROLE.ONLY_EDITOR,
                      label: ROLE_ACCESS_LABEL[PUBLIC_ROLE.ONLY_EDITOR],
                    }
                  : {
                      key: PUBLIC_ROLE.ONLY_VIEWER,
                      label: ROLE_ACCESS_LABEL[PUBLIC_ROLE.ONLY_VIEWER],
                    },
              ],
              onClick: v => handleChangePublicRole(+v.key),
            }}
          >
            <div className="public-role-dropdown-wrapper">
              <div className="for-title">{ROLE_ACCESS_LABEL[publicRole]}</div>

              {allowEdit && !isShowOnlyText && (
                <Icon
                  className="icon-angle"
                  type="icon-ants-expand-more"
                  style={{ fontSize: 20 }}
                />
              )}
            </div>
          </Dropdown>
        )}
      </div>
    </StyledGeneralAccessRoot>
  );
};
