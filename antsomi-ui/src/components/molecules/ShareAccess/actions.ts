import { ObjectAccessInfo, ShareAccessState } from './types';

export const TYPES = {
  UPDATE_OBJECT_ACCESS_INFO: 'UPDATE_OBJECT_ACCESS_INFO',
  UPDATE_MODAL_TRANSTER_OWNERSHIP: 'UPDATE_MODAL_TRANSTER_OWNERSHIP',
} as const;

export const updateObjAccessInfo = (payload: {
  updated: ObjectAccessInfo;
  triggerOut?: boolean;
}) => ({
  type: TYPES.UPDATE_OBJECT_ACCESS_INFO,
  payload,
});

export const updateModalTransferOwnership = (
  modalState: Partial<ShareAccessState['modalTransferOwnership']>,
) => ({
  type: TYPES.UPDATE_MODAL_TRANSTER_OWNERSHIP,
  payload: modalState,
});
