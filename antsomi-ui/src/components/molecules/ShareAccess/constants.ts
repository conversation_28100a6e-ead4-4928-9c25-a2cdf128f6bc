import { AccessRole, UserAccessInfo } from './types';

export const REGEX_REMOVE_NAME = /\.remove#1$/i;

export const PUBLIC_ROLE = {
  ONLY_EDITOR: 2,
  ONLY_VIEWER: 3,
};

export const GENERAL_ACCESS = {
  PUBLIC: 1,
  RESTRICTED: 0,
} as const;

export const ROLE_MAPPING = {
  OWNER: 1,
  EDITOR: 2,
  VIEWER: 3,
} as const;

export const ROLE_ACCESS_LABEL = {
  [ROLE_MAPPING.OWNER]: 'Owner',
  [ROLE_MAPPING.EDITOR]: 'Editor',
  [ROLE_MAPPING.VIEWER]: 'Viewer',
};

export const ACCESS_PROPERTIES_BY_ROLE: Record<
  AccessRole,
  Pick<UserAccessInfo, 'role' | 'allowView' | 'allowEdit' | 'allowComment'>
> = {
  [ROLE_MAPPING.OWNER]: {
    role: ROLE_MAPPING.OWNER,
    allowView: 1,
    allowEdit: 1,
    allowComment: 1,
  },
  [ROLE_MAPPING.EDITOR]: {
    role: ROLE_MAPPING.EDITOR,
    allowView: 1,
    allowEdit: 1,
    allowComment: 1,
  },
  [ROLE_MAPPING.VIEWER]: {
    role: ROLE_MAPPING.VIEWER,
    allowView: 1,
    allowEdit: 0,
    allowComment: 0,
  },
} as const;

export const PEOPLE_ACTION_KEYS = {
  TO_VIEWER: 'to_viewer',
  TO_EDITOR: 'to_editor',
  TO_OWNER: 'to_owner',
  REMOVE_ACCESS: 'remove_acess',
} as const;

export const PEOPLE_ACTIONS = {
  [PEOPLE_ACTION_KEYS.TO_VIEWER]: {
    label: 'Viewer',
    key: PEOPLE_ACTION_KEYS.TO_VIEWER,
  },
  [PEOPLE_ACTION_KEYS.TO_EDITOR]: {
    label: 'Editor',
    key: PEOPLE_ACTION_KEYS.TO_EDITOR,
  },
  [PEOPLE_ACTION_KEYS.TO_OWNER]: {
    label: 'Transfer ownership',
    key: PEOPLE_ACTION_KEYS.TO_OWNER,
  },
  [PEOPLE_ACTION_KEYS.REMOVE_ACCESS]: {
    label: 'Remove access',
    key: PEOPLE_ACTION_KEYS.REMOVE_ACCESS,
  },
} as const;

export const PEOPLE_ACTIONS_BY_ROLE = {
  [ROLE_MAPPING.OWNER]: [PEOPLE_ACTION_KEYS.REMOVE_ACCESS],
} as const;

export const GENERAL_ACTION_KEYS = {
  RESTRICTED: 'restricted',
  PUBLIC: 'public',
} as const;

export const GENERAL_ACTIONS = {
  [GENERAL_ACTION_KEYS.PUBLIC]: {
    key: GENERAL_ACTION_KEYS.PUBLIC,
    label: 'public',
  },
  [GENERAL_ACTION_KEYS.RESTRICTED]: {
    key: GENERAL_ACTION_KEYS.RESTRICTED,
    label: 'Restricted',
  },
} as const;

export const MENU_PERMISSION = {
  NONE: 1,
  CREATED_BY_USER: 2,
  MANAGEMENT_BY_USER: 3,
  EVERYTHING: 4,
} as const;
