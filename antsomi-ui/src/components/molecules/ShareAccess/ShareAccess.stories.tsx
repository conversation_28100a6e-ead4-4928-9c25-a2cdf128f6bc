import axios from 'axios';
import React, { useState } from 'react';
import { Meta } from '@storybook/react';
import { ShareAccess } from './ShareAccess';
import { ObjectAccessInfo, UserInfo } from './types';
import { get } from 'lodash';
import { ACCESS_PROPERTIES_BY_ROLE, MENU_PERMISSION, ROLE_MAPPING } from './constants';

export default {
  title: 'Molecules/ShareAccess',
} as Meta<typeof ShareAccess>;

const userId = **********;
const network_id = 33167;
const token = '5474r2x214z254a4u2a4y4k5a4q5e4q594a4g4t474x5';

const getUserInfos = async (email: string) => {
  const type = 'search-user-info';

  const res = await axios.get(
    `https://permission.antsomi.com/api/account/info?userId=${userId}&network_id=${network_id}&_token=${token}&_user_id=${userId}&_account_id=${userId}&_lang=en&type=${type}&email=${email}`,
    {
      method: 'GET',
    },
  );

  const data = get(res, 'data.data');

  return data as UserInfo[];
};

const DEFAULT_VALUE: ObjectAccessInfo = {
  ownerId: **********,
  isPublic: 1,
  listAccess: [
    {
      userId: **********,
      email: '<EMAIL>',
      avatar: '//c0-platform.ants.tech/avatar/2023/05/04/b3rg0ye8oa.png',
      fullName: 'Truong Vi',
      ...ACCESS_PROPERTIES_BY_ROLE[ROLE_MAPPING.OWNER],
    },
    {
      userId: **********,
      email: '<EMAIL>',
      avatar: '//c0-platform.ants.tech/avatar/2023/05/04/b3rg0ye8oa.png',
      fullName: 'Truong Vi 2',
      ...ACCESS_PROPERTIES_BY_ROLE[ROLE_MAPPING.EDITOR],
    },
  ],
  publicRole: 3,
};

export const Default = () => {
  // const [open, setOpen] = useState<boolean>(false);
  const [accessInfo, setAccessInfo] = useState<ObjectAccessInfo>(DEFAULT_VALUE);

  return (
    <>
      {/* <Button onClick={() => setOpen(true)}>Click</Button> */}

      {/* <Modal open={open} style={{ maxWidth: 400 }} onCancel={() => setOpen(false)}> */}
      <ShareAccess
        userId={**********}
        userPermission={{
          edit: MENU_PERMISSION.CREATED_BY_USER,
          view: MENU_PERMISSION.CREATED_BY_USER,
        }}
        accessInfo={accessInfo}
        generalAccessSettings={{
          publicOnlyWith: true,
        }}
        getUserInfo={getUserInfos}
        onChange={value => {
          console.log({ value });

          setAccessInfo(value);
        }}
      />
      {/* </Modal> */}
    </>
  );
};

export const NotAllowAddUserAndChangeOwnership = () => (
  <ShareAccess
    userId={**********}
    userPermission={{
      edit: MENU_PERMISSION.CREATED_BY_USER,
      view: MENU_PERMISSION.NONE,
    }}
    accessInfo={DEFAULT_VALUE}
    getUserInfo={getUserInfos}
    allowAddUser={false}
    excludeUserAccess={['to_owner']}
  />
);
