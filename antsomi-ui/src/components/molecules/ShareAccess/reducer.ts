import produce from 'immer';
import { TYPES } from './actions';
import { ShareAccessAction, ShareAccessProps, ShareAccessState } from './types';
import { mappingOutData } from './utils';

export const shareAccessReducer =
  (onChange: ShareAccessProps['onChange']) =>
  (state: ShareAccessState, action: ShareAccessAction): ShareAccessState =>
    produce(state, draft => {
      switch (action.type) {
        case TYPES.UPDATE_OBJECT_ACCESS_INFO: {
          const { updated, triggerOut = true } = action.payload;

          draft.accessInfo = updated;

          if (onChange && triggerOut) {
            onChange(mappingOutData(action.payload.updated));
          }
          break;
        }
        case TYPES.UPDATE_MODAL_TRANSTER_OWNERSHIP: {
          draft.modalTransferOwnership = {
            ...state.modalTransferOwnership,
            ...action.payload,
          };
          break;
        }
        default:
          break;
      }
    });
