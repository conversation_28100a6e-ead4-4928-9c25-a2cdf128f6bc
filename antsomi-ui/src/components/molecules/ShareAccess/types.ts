import { updateModalTransferOwnership, updateObjAccessInfo } from './actions';
import { PEOPLE_ACTION_KEYS } from './constants';

export type UserInfo = {
  user_id: number;
  avatar?: string;
  full_name?: string;
  email?: string;
  user_name?: string;
  user_type?: number;
  ctime?: Date;
  utime?: Date;

  [key: string]: any;
};

export type UserAccessInfo = {
  userId: number;
  role: number;
  allowView: number;
  allowEdit: number;
  allowComment: number;

  fullName?: string;
  email?: string;
  avatar?: string;

  [key: string]: any;
};

export type ObjectAccessInfo = {
  isPublic: number;
  publicRole: number | null;
  ownerId: number;
  listAccess: UserAccessInfo[];
};

export type ActionKey = (typeof PEOPLE_ACTION_KEYS)[keyof typeof PEOPLE_ACTION_KEYS];
export type ExcludeUserAccess = Array<ActionKey>;
export type GeneralAccessSettings = {
  publicOnlyWith?: boolean;
};

export type ShareAccessProps = {
  userId?: number;
  userPermission?: {
    edit: number;
    view: number;
  };
  accessInfo?: ObjectAccessInfo;
  allowAddUser?: boolean;
  placeholder?: string;
  excludeUserAccess?: ExcludeUserAccess;
  getUserInfo?(search: string, signal: AbortSignal): Promise<UserInfo[]> | UserInfo[];
  generalAccessSettings?: GeneralAccessSettings;
  onChange?: (accessInfo: ObjectAccessInfo) => void;
  hideTransferOwnership?: boolean;
};

export type ShareAccessState = {
  accessInfo?: ObjectAccessInfo;
  accessUserId?: number;
  accessUserPermission?: {
    view: number;
    edit: number;
  };
  excludeUserAccess?: ExcludeUserAccess;
  modalTransferOwnership: {
    open: boolean;
    userToOwnerShipId: number;
  };
};

export type ShareAccessAction =
  | ReturnType<typeof updateObjAccessInfo>
  | ReturnType<typeof updateModalTransferOwnership>;

export type AccessRole = 1 | 2 | 3;

export type GeneralAccess = 1 | 0;
