import { Meta, StoryObj } from '@storybook/react/*';
import { EditingList } from './EditingList';
import { useState } from 'react';
import { CurvedConnectorIcon } from '../../icons';
import CITIES from '../../../__mocks__/1000_city.json';
import { Flex, Form, Input } from 'antd';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

const meta = {
  title: 'Molecules/EditingList',
  component: EditingList,
} satisfies Meta<typeof EditingList>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default = {
  args: {
    title: 'Selected Fields',
    options: [
      {
        key: 'account_id',
        label: 'Account ID',
      },
      {
        key: 'ticket_id',
        label: 'Ticket ID',
      },
      {
        key: 'sia_id',
        label: 'Sia ID',
      },
      {
        key: 'created_at',
        label: 'Created At',
      },
      {
        key: 'updated_at',
        label: (
          <Flex align="center">
            <CurvedConnectorIcon />
            Updated At
          </Flex>
        ),
      },
      {
        key: 'long_attr',
        label:
          'Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr',
      },
      {
        key: '124',
        label: 'Test search',
        search: '124 Test search',
      },
    ],
  },
  render: args => {
    const { options, ...restArgs } = args;
    const [selected, setSelected] = useState(['account_id', 'ticket_id', '124']);

    const removable = selected.length > 1;

    return (
      <div style={{ height: 400, width: 300 }}>
        <EditingList
          {...restArgs}
          removable={removable}
          selected={selected}
          options={options}
          onChange={v => setSelected(v)}
        />
      </div>
    );
  },
} satisfies Story;

export const LargeList = {
  args: {
    options: CITIES.map(c => ({ key: c.value.toString(), label: c.label })),
    popoverProps: {
      inputSearchProps: {
        searchConfig: {
          searchWithKey: true,
        },
      },
    },
  },
  render: args => {
    const [selected, setSelected] = useState<string[]>([]);
    const [width, setWidth] = useState(200);
    const [height, setHeight] = useState(300);

    return (
      <Flex vertical gap={8}>
        <div style={{ width: 300 }}>
          <Form.Item label="Width">
            <Input
              step={10}
              type="number"
              value={width}
              onChange={e => setWidth(+e.target.value)}
            />
          </Form.Item>

          <Form.Item label="Height">
            <Input
              step={10}
              type="number"
              value={height}
              onChange={e => setHeight(+e.target.value)}
            />
          </Form.Item>
        </div>

        <div
          style={{ width, height, padding: 10, border: `1px solid ${globalToken?.colorBorder}` }}
        >
          <EditingList {...args} selected={selected} onChange={setSelected} />
        </div>
      </Flex>
    );
  },
} satisfies Story;
