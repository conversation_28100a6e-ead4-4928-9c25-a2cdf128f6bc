import React, { memo, useLayoutEffect, useMemo, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

// Hooks

// Styled
import { HeaderV2Styled, LogoWrapper } from './styled';

// Components
import { AccountSelection, AccountSelectionProps } from '../AccountSelection';
import { Flex } from '@antscorp/antsomi-ui/es/components/atoms/Flex';
import { AccountSharing } from '@antscorp/antsomi-ui/es/components/organism/AccountSharing';
import type { AccountSharingProps } from '@antscorp/antsomi-ui/es/components/organism/AccountSharing';
import { Help } from '@antscorp/antsomi-ui/es/components/organism/Help';
import { Notification } from '@antscorp/antsomi-ui/es/components/organism/Notification';
import type { NotificationProps } from '@antscorp/antsomi-ui/es/components/organism/Notification';

import { IHelpProps } from '../../organism/Help/types';

// Constants
import {
  APP_CODES,
  APP_PERMISSION_DOMAIN,
  CDP_API,
  IAM_API,
  ISSUE_API,
  PERMISSION_API,
  PLATFORM_API,
  PORTALS_IDS,
  SOCKET_API,
  MEDIA_TEMPLATE_API,
} from '@antscorp/antsomi-ui/es/constants';
import { useContextSelector } from 'use-context-selector';

import { AppConfigContext } from '@antscorp/antsomi-ui/es/providers/AppConfigProvider';

// Types
import { TEnv } from '@antscorp/antsomi-ui/es/types/config';
import { isNil } from 'lodash';
import { useCustomRouter } from '@antscorp/antsomi-ui/es/hooks';

const SubLogoAntsomi = 'https://st-media-template.antsomi.com/icons/antsomi/antsomi.png';
const { DATAFLOWS, APP_ANTALYSER } = APP_CODES;

interface Breadcrumb {
  title: string;
  link?: string;
  useExternalLink?: boolean;
}

export interface HeaderV2Props {
  show?: boolean;
  className?: string;
  style?: React.CSSProperties;
  pageTitle?: string | React.ReactNode;
  breadcrumbs?: Breadcrumb[];
  showLogo?: boolean;
  accountSelection?: Omit<AccountSelectionProps, 'onChange' | 'apiConfig'> & {
    onSelectAccount?: (userId: string) => void;
  };
  helpConfig: Omit<IHelpProps, 'configs'> & {
    configs: Omit<
      IHelpProps['configs'],
      'portalId' | 'userId' | 'token' | 'domain' | 'domainTicket' | 'domainPlatform'
    >;
  } & { show?: boolean };
  notificationConfig?: { show?: boolean };
  // accountSharingConfig: AccountSharingProps & { show?: boolean };
  accountSharingConfig: Pick<
    AccountSharingProps,
    | 'isShowSharing'
    | 'isShareAccountAccess'
    | 'u_ogs'
    | 'appCode'
    | 'callbackLogout'
    | 'callbackGetInfoAccount'
  > & { show?: boolean };

  useURLParam?: boolean;
  rightContent?: React.ReactNode;
  config?: {
    env?: TEnv;
    language?: string;
    userId?: number;
    portalId?: number;
    token?: string;
    appCode?: string;
    menuCode?: string;
    permissionDomain?: string;
    iamDomain?: string;
  };
  onCLickLogo?: () => void;
}

// const DOUBLE_RIGHT_POINTING = '&raquo;'

export const HeaderV2: React.FC<HeaderV2Props> = memo(props => {
  const {
    auth,
    env: envContext,
    languageCode = 'en',
    appCode: contextAppCode,
    urlLogout: urlLogoutProvider = '',
    menuCode: contextMenuCode,
  } = useContextSelector(AppConfigContext, state => state);
  const { search } = useLocation();
  const { replace } = useCustomRouter();

  // * Prefer to use Header in AppConfigContext
  const {
    show = true,
    className,
    style,
    breadcrumbs,
    showLogo = false,
    rightContent,
    pageTitle = '',
    accountSelection = {},
    helpConfig,
    notificationConfig = {},
    accountSharingConfig,
    useURLParam,
    config,
    onCLickLogo,
  } = props;
  /** General config for children component */

  const {
    env = envContext,
    language = languageCode,
    // NOTE: Just hot fix for change userId to accountId
    userId = isNaN(Number(auth?.accountId || auth?.userId))
      ? 0
      : Number(auth?.accountId || auth?.userId),
    portalId = auth?.portalId,
    token = auth?.token,
    permissionDomain = PERMISSION_API,
    iamDomain = IAM_API,
    appCode = contextAppCode,
    menuCode = contextMenuCode,
  } = config || {};

  const {
    currentAccount,
    inputStyle = 'select',
    onSelectAccount,
    ...accountSelectionProps
  } = accountSelection;

  const urlParams = useMemo(() => new URLSearchParams(search), [search]);
  const oidParam = urlParams.get('oid') || currentAccount || 'all';

  const [selectedAccount, setSelectedAccount] = useState<string>(
    `${useURLParam ? oidParam : `${currentAccount ?? userId ?? 'all'}`}`,
  );

  useLayoutEffect(() => {
    if (useURLParam) setSelectedAccount(oidParam);
  }, [oidParam, useURLParam]);

  // Memo
  const accountSelectionDomain = useMemo(() => {
    if (portalId !== PORTALS_IDS.SANDBOX_76753) return CDP_API[env || 'development'];

    switch (appCode) {
      case DATAFLOWS:
      case APP_ANTALYSER:
        return APP_PERMISSION_DOMAIN[appCode || '']?.[env || ''];

      default:
        return CDP_API[env || 'development'];
    }
  }, [portalId, env, appCode]);

  if (!env || !language || !userId || !portalId || !token) return null;

  const { show: showHelp = true, configs, ...helpProps } = helpConfig;
  const { show: showNotification = true } = notificationConfig;
  const {
    show: showAccountSharing = true,
    isShowSharing = true,
    isShareAccountAccess = true,
    ...accountSharingProps
  } = accountSharingConfig;

  const accountSelectionApiConfig = {
    domain: accountSelectionDomain,
    permissionDomain,
    languageCode: language,
    userId,
    portalId,
    token,
    appCode,
    menuCode,
  };

  const helpConfigProps: IHelpProps['configs'] = {
    ...configs,
    portalId: portalId.toString(),
    userId: userId.toString(),
    token,
    domain: CDP_API[env],
    domainTicket: ISSUE_API[env],
    domainPlatform: PLATFORM_API,
    domainUpload: MEDIA_TEMPLATE_API[env],

    config: {
      ...configs.config,
      user_language: language,
    },
  };

  const notificationConfigProps: NotificationProps = {
    accountId: userId,
    networkId: portalId,
    token,
    lang: language,
    permissionDomain,
    socketDomain: SOCKET_API,
  };

  const accountSharingPropsFormatted: Omit<
    AccountSharingProps,
    | 'isShowSharing'
    | 'isShareAccountAccess'
    | 'u_ogs'
    | 'appCode'
    | 'callbackLogout'
    | 'callbackGetInfoAccount'
  > = {
    permissionDomain,
    iamDomain,
    accountId: userId,
    networkId: portalId,
    // NOTE: old edit link
    urlEditProfile: `${permissionDomain}/${portalId}#/account/profile/`,
    // urlEditProfile: `${permissionDomain}/${portalId}#/${userId}/permission/account?ui=drawer-detail&accountId=${userId}&tab=settings`,

    // 6006 is storybook port so direct to sandbox page
    urlLogout: urlLogoutProvider || window.location.origin,
    // urlLogout: window.location.origin,
    token,
    lang: language,
  };

  const handleChangeAccount = (userId: string) => {
    setSelectedAccount(userId);
    if (useURLParam) {
      if (userId === 'all') {
        urlParams.delete('oid');
      } else {
        urlParams.set('oid', userId);
      }

      replace({ search: urlParams.toString() });
    }
    onSelectAccount?.(userId);
  };

  const QUOTE_ENTITY = <>&raquo;</>;

  return (
    <HeaderV2Styled $show={show} className={className || ''} style={style}>
      <div className="left-side">
        {showLogo && (
          <LogoWrapper className="logo-wrapper" onClick={onCLickLogo}>
            <img src={SubLogoAntsomi} alt="Antsomi sub logo" />
          </LogoWrapper>
        )}
        <AccountSelection
          {...accountSelectionProps}
          currentAccount={selectedAccount}
          apiConfig={accountSelectionApiConfig}
          onChange={handleChangeAccount}
          inputStyle={inputStyle}
          refreshOnSelect
        />
        <div className="title-container" data-test="page-title">
          {breadcrumbs ? (
            <div className="header-breadcrumbs" data-test="breadcrumbs">
              {breadcrumbs.map((item, index) => (
                <React.Fragment key={`${item.title}_${item.link}`}>
                  {/* NOTE: check if not set useExternalLink => implicit (default) is false */}
                  {isNil(item.useExternalLink) && !item.useExternalLink ? (
                    <Link to={item.link || ''} className="header-breadcrumbs__item">
                      {item.title}
                    </Link>
                  ) : (
                    <a href={item.link} className="header-breadcrumbs__item">
                      {item.title}
                    </a>
                  )}
                  {breadcrumbs.length === index + 1 ? null : <>&ensp;{QUOTE_ENTITY}&ensp;</>}
                </React.Fragment>
              ))}
            </div>
          ) : null}
          {typeof pageTitle === 'string' ? (
            <div className="page-title">{pageTitle}</div>
          ) : (
            pageTitle
          )}
        </div>
      </div>
      <div data-test="header-menu" className="right-side">
        <Flex gap={10}>
          {rightContent}
          {showHelp ? <Help configs={helpConfigProps} {...helpProps} /> : null}
          {showNotification ? <Notification {...notificationConfigProps} /> : null}
        </Flex>

        {showAccountSharing ? (
          <AccountSharing
            isShowSharing={isShowSharing}
            isShareAccountAccess={isShareAccountAccess}
            {...accountSharingPropsFormatted}
            {...accountSharingProps}
          />
        ) : null}
      </div>
    </HeaderV2Styled>
  );
});
