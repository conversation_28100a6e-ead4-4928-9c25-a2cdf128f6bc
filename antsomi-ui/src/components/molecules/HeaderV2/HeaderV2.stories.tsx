import React from 'react';
import { Meta, StoryObj } from '@storybook/react';

import { HeaderV2 } from './HeaderV2';
import { EditableName } from '../EditableName';

const meta = {
  title: 'Molecules/HeaderV2',
  component: HeaderV2,
  parameters: {
    docs: {
      description: {
        component: 'Header UI version 2.',
      },
    },
  },
} satisfies Meta<typeof HeaderV2>;

export default meta;

type Story = StoryObj<typeof meta>;

const TOKEN = '5474r2x214z254a4u2a4y4k5a4q5e4q594a4g4t474x5';

export const Default: Story = {
  args: {
    config: { env: 'development', userId: **********, portalId: 33167, token: TOKEN },
    // accountSelection: {currentAccount: },
    useURLParam: true,
    pageTitle: 'THIS IS TITLE',
    helpConfig: {
      configs: {
        appCode: 'SANDBOX_MARKETING',
        avatar: '//c0-platform.ants.tech/avatar/2021/09/17/0xgbkurioo.png',
        config: {
          p_timezone: 'Asia/Singapore',
          api_pid: 33167,
          p_f_longdatetime: "dd MMMM 'at' HH:mm:ss",
          embeddedData: {},
          INSIGHT_U_OGS: 'uogs',
        },
      },
    },
    accountSharingConfig: {
      u_ogs: 'uogs',
      appCode: 'APP_CUSTOMER_360',
      // callbackGetInfoAccount: value => console.log('🚀 ~ callbackGetInfoAccount ~ value:: ', value),
      // callbackLogout: () => console.log('🚀 ~ callbackLogout ~ value:: '),
    },
  },
};

export const BreadcrumbsAndTitle: Story = {
  args: {
    ...Default.args,
    pageTitle: <EditableName defaultValue="THIS IS TITLE" />,
    breadcrumbs: [
      {
        title: 'Dashboard',
        useExternalLink: true,
        link: 'https://mail.google.com/mail/u/1/#chat/space/AAAACF53uy4',
      },
      { title: 'Level 2', link: 'https://mail.google.com/mail/u/1/#chat/space/AAAACF53uy4' },
      { title: 'Level 3', link: 'dashboard' },
    ],
  },
};
