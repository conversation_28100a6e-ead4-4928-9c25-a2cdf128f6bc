import React, { useMemo } from 'react';
import { PopoverProps } from 'antd';

/* Components */
import { Space } from '@antscorp/antsomi-ui/es/components/atoms/Space';
import { Switch } from '@antscorp/antsomi-ui/es/components/atoms/Switch';
import { ColorSetting } from '@antscorp/antsomi-ui/es/components/molecules/ColorSetting';
import { SliderWithInputNumber } from '@antscorp/antsomi-ui/es/components/molecules/SliderWithInputNumber';
import { SettingWrapper } from '@antscorp/antsomi-ui/es/components/molecules/SettingWrapper';
import type { SettingWrapperProps } from '@antscorp/antsomi-ui/es/components/molecules/SettingWrapper';
import { SettingWrapperPopover } from '@antscorp/antsomi-ui/es/components/molecules/SettingWrapperPopover';
import { FontFamilySelect } from '@antscorp/antsomi-ui/es/components/molecules/FontFamilySelect';
import { FontWeightSelect } from '@antscorp/antsomi-ui/es/components/molecules/FontWeightSelect';
import { TextTransformSelect } from '@antscorp/antsomi-ui/es/components/molecules/TextTransformSelect';
import { TextDecorationSelect } from '@antscorp/antsomi-ui/es/components/molecules/TextDecorationSelect';

/* Types */
import { TFontSettings, TFontStyles } from './types';
import { FONT_SETTING_DEFAULT, FONT_STYLE_DEFAULT } from './constants';

type TFontSettingOptions =
  | 'fontFamily'
  | 'color'
  | 'fontSize'
  | 'fontWeight'
  | 'textTransform'
  | 'textDecoration'
  | 'italic'
  | 'lineHeight'
  | 'letterSpacing';

export type TFontSettingProps = {
  label?: string;
  settings?: Partial<TFontSettings>;
  styles?: Partial<TFontStyles>;
  showSettings?: TFontSettingOptions[];
  wrapperProps?: Omit<SettingWrapperProps, 'label'>;
  popoverProps?: Omit<PopoverProps, 'content'>;
  childrenProps?: Omit<TFontSettingEditProps, 'settings' | 'styles' | 'onChange'>;
  onChange?: (settings: Partial<TFontSettings>, styles: Partial<TFontStyles>) => void;
};

export type TFontSettingEditProps = {
  settings: Partial<TFontSettings>;
  styles: Partial<TFontStyles>;
  showSettings?: TFontSettingOptions[];
  onChange?: (settings: Partial<TFontSettings>, styles: Partial<TFontStyles>) => void;
};

export const FontSettingEdit: React.FC<TFontSettingEditProps> = ({
  settings,
  styles,
  showSettings,
  onChange,
}) => {
  const handleChange = (setting: Partial<TFontSettings>, style: Partial<TFontStyles>) => {
    if (typeof onChange === 'function') {
      const newSettings = { ...settings, ...setting };
      const newStyles = { ...styles, ...style };
      onChange(newSettings, newStyles);
    }
  };

  const settingMap: Record<TFontSettingOptions, React.ReactNode> = useMemo(
    () => ({
      fontFamily: (
        <FontFamilySelect
          label="Font Family"
          value={settings?.fontFamily}
          onChange={fontFamily => handleChange({ fontFamily }, { fontFamily })}
        />
      ),
      color: (
        <ColorSetting
          label="Font Color"
          color={settings?.fontColor}
          onChange={fontColor => handleChange({ fontColor }, { color: fontColor })}
        />
      ),
      fontSize: (
        <SettingWrapper label="Font Size" vertical>
          <SliderWithInputNumber
            value={typeof settings?.fontSize !== 'undefined' ? +settings.fontSize : 0}
            onAfterChange={fontSize => handleChange({ fontSize }, { fontSize })}
          />
        </SettingWrapper>
      ),
      fontWeight: (
        <FontWeightSelect
          label="Font Weight"
          value={settings?.fontWeight}
          onChange={fontWeight => handleChange({ fontWeight }, { fontWeight })}
        />
      ),
      lineHeight: (
        <SettingWrapper label="Line Height" vertical>
          <SliderWithInputNumber
            defaultValue={typeof settings?.lineHeight !== 'undefined' ? +settings.lineHeight : 0}
            min={1}
            max={5}
            step={0.1}
            onAfterChange={lineHeight => handleChange({ lineHeight }, { lineHeight })}
          />
        </SettingWrapper>
      ),
      letterSpacing: (
        <SettingWrapper label="Letter Spacing" vertical>
          <SliderWithInputNumber
            defaultValue={
              typeof settings?.letterSpacing !== 'undefined' ? +settings.letterSpacing : 0
            }
            min={-10}
            max={10}
            onAfterChange={letterSpacing => handleChange({ letterSpacing }, { letterSpacing })}
          />
        </SettingWrapper>
      ),
      textTransform: (
        <TextTransformSelect
          label="Text Transform"
          value={settings?.textTransform}
          onChange={textTransform => handleChange({ textTransform }, { textTransform })}
        />
      ),
      textDecoration: (
        <TextDecorationSelect
          label="Text Decoration"
          value={settings?.textDecoration}
          onChange={textDecoration => handleChange({ textDecoration }, { textDecoration })}
        />
      ),
      italic: (
        <SettingWrapper label="Font Style Italic">
          <Switch
            checked={settings?.fontItalic}
            onChange={fontItalic =>
              handleChange({ fontItalic }, { fontStyle: fontItalic ? 'italic' : 'normal' })
            }
          />
        </SettingWrapper>
      ),
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [settings],
  );

  const filteredSetting = useMemo(() => {
    if (!showSettings || showSettings?.length === 0) {
      return Object.keys(settingMap).map(keySetting => settingMap[keySetting]);
    }

    return showSettings.map(keySetting => settingMap[keySetting]);
  }, [showSettings, settingMap]);

  return <Space direction="vertical">{filteredSetting}</Space>;
};

export const FontSetting: React.FC<TFontSettingProps> = props => {
  const {
    label,
    settings = FONT_SETTING_DEFAULT,
    styles = FONT_STYLE_DEFAULT,
    wrapperProps,
    popoverProps,
    childrenProps,
    showSettings,
    onChange,
  } = props;

  return (
    <SettingWrapperPopover
      label={label ?? 'Font Settings'}
      popoverProps={{ overlayStyle: { top: 0 }, placement: 'bottomRight', ...popoverProps }}
      wrapperProps={{ containerStyle: { minWidth: '288px' }, ...wrapperProps }}
    >
      <FontSettingEdit
        settings={settings}
        styles={styles}
        showSettings={showSettings}
        onChange={onChange}
        {...childrenProps}
      />
    </SettingWrapperPopover>
  );
};

// FontSetting.defaultProps = {
//   settings: FONT_SETTING_DEFAULT,
//   styles: FONT_STYLE_DEFAULT,
// };
