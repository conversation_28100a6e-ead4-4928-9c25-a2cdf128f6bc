import React from 'react';
import { Meta, StoryFn } from '@storybook/react';
import { FontSetting, FontSettingEdit } from '..';
import { FONT_SETTING_DEFAULT, FONT_STYLE_DEFAULT } from './constants';

export default {
  title: 'Molecules/FontSetting',
  component: FontSetting,
} as Meta<typeof FontSetting>;

const TemplateEdit: StoryFn<typeof FontSettingEdit> = args => <FontSettingEdit {...args} />;

export const Default = {
  args: {
    label: 'Font Settings',
    settings: FONT_SETTING_DEFAULT,
    styles: FONT_STYLE_DEFAULT,
    onChange: () => {},
  },
};

export const Editor = {
  render: TemplateEdit,
  args: {
    settings: FONT_SETTING_DEFAULT,
    styles: FONT_STYLE_DEFAULT,
    onChange: () => {},
  },
};

export const ShowWithOptions = {
  args: {
    settings: FONT_SETTING_DEFAULT,
    styles: FONT_STYLE_DEFAULT,
    showSettings: ['fontFamily', 'fontColor'],
    onChange: () => {},
  },
};
