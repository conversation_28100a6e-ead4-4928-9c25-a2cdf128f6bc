// Libraries
import React, { useState } from 'react';
import { StoryFn, Meta } from '@storybook/react';
import { CaretRightOutlined, SettingOutlined } from '@ant-design/icons';

// Components
import { Collapse } from './Collapse';
import { Table } from '../../organism';
import { Select } from '../Select';
import { TABLE_API_COLUMNS } from '../../../constants';
import { Space } from '../../atoms';

// Constants

const { Panel } = Collapse;
const { Option } = Select;

export default {
  title: 'Molecules/Collapse',
  component: Collapse,
  argTypes: {
    accordion: {
      name: 'accordion',
      defaultValue: false,
      description: 'If true, <PERSON><PERSON><PERSON> renders as Accordion',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: false },
      },
      control: {
        type: 'boolean',
      },
    },
    activeKey: {
      name: 'activeKey',
      description: `Key of the active panel	`,
      table: {
        type: { summary: 'string[] | string | number[] | number' },
        defaultValue: {
          summary: `No default value. In accordion mode, it's the key of the first panel`,
        },
      },
      control: null,
    },
    border: {
      name: 'border	',
      defaultValue: true,
      description: 'Toggles rendering of the border around the collapse block	',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: true },
      },
      control: {
        type: 'boolean',
      },
    },
    collapsible: {
      name: 'collapsible',
      description:
        'Specify whether the panels of children be collapsible or the trigger area of collapsible',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'header | icon | disabled' },
      },
      control: null,
    },
    defaultActiveKey: {
      name: 'defaultActiveKey',
      description: 'Key of the initial active panel	',
      table: {
        type: { summary: null },
        defaultValue: {
          summary: 'string[] | string | number[] | number',
        },
      },
      control: null,
    },
    destroyInactivePanel: {
      name: 'destroyInactivePanel',
      description: 'Destroy Inactive Panel	',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: false },
      },
      control: {
        type: 'boolean',
      },
    },
    expandIcon: {
      name: 'expandIcon',
      description: 'Allow to customize collapse icon',
      table: {
        type: { summary: '' },
        defaultValue: { summary: 'panelProps => ReactNode' },
      },
      control: null,
    },
    expandIconPosition: {
      name: 'expandIconPosition',
      description: 'Set expand icon position',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'start | end' },
      },
      control: null,
    },
    ghost: {
      name: 'ghost',
      defaultValue: false,
      description: 'Make the collapse borderless and its background transparent	',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: false },
      },
      control: {
        type: 'boolean',
      },
    },
    onChange: {
      name: 'onChange',
      description: 'Callback function executed when active panel is changed	',
      table: {
        type: { summary: null },
        defaultValue: { summary: 'function' },
      },
      control: null,
    },
  },
  parameters: {
    docs: {
      description: {
        component:
          'Simple rectangular container.' +
          '\n### When To Use' +
          '\n' +
          '- A card can be used to display content related to a single subject. The content can consist of multiple elements of varying types and sizes.' +
          '\n',
      },
    },
  },
} as Meta<typeof Collapse>;

const text = `
  A dog is a type of domesticated animal.
  Known for its loyalty and faithfulness,
  it can be found as a welcome guest in many households across the world.
`;

// Default
const Template: StoryFn<typeof Collapse> = args => {
  const onChange = (_key: string | string[]) => {};

  return (
    <Collapse defaultActiveKey={['1']} onChange={onChange} {...args}>
      <Panel header="This is panel header 1" key="1">
        <p>{text}</p>
      </Panel>
      <Panel header="This is panel header 2" key="2">
        <p>{text}</p>
      </Panel>
      <Panel header="This is panel header 3" key="3">
        <p>{text}</p>
      </Panel>
    </Collapse>
  );
};

export const Basic = {
  render: Template,
};

export const Accordion = {
  render: () => (
    <Collapse accordion>
      <Panel header="This is panel header 1" key="1">
        <p>{text}</p>
      </Panel>
      <Panel header="This is panel header 2" key="2">
        <p>{text}</p>
      </Panel>
      <Panel header="This is panel header 3" key="3">
        <p>{text}</p>
      </Panel>
    </Collapse>
  ),

  parameters: {
    docs: {
      description: {
        story: 'In accordion mode, only one panel can be expanded at a time.',
      },
    },
  },
};

export const NestedPanel = {
  render: () => {
    const onChange = (_key: string | string[]) => {};

    return (
      <Collapse onChange={onChange}>
        <Panel header="This is panel header 1" key="1">
          <Collapse defaultActiveKey="1">
            <Panel header="This is panel nest panel" key="1">
              <p>{text}</p>
            </Panel>
          </Collapse>
        </Panel>
        <Panel header="This is panel header 2" key="2">
          <p>{text}</p>
        </Panel>
        <Panel header="This is panel header 3" key="3">
          <p>{text}</p>
        </Panel>
      </Collapse>
    );
  },

  parameters: {
    docs: {
      description: {
        story: '`Collapse` is nested inside the `Collapse`.',
      },
    },
  },
};

export const Borderless = {
  render: () => (
    <Collapse bordered={false} defaultActiveKey={['1']}>
      <Panel header="This is panel header 1" key="1">
        {text}
      </Panel>
      <Panel header="This is panel header 2" key="2">
        {text}
      </Panel>
      <Panel header="This is panel header 3" key="3">
        {text}
      </Panel>
    </Collapse>
  ),

  parameters: {
    docs: {
      description: {
        story: 'A borderless style of Collapse.',
      },
    },
  },
};

export const CustomPanel = {
  render: () => (
    <Collapse
      bordered={false}
      defaultActiveKey={['1']}
      expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
      className="site-collapse-custom-collapse"
    >
      <Panel header="This is panel header 1" key="1" className="site-collapse-custom-panel">
        <p>{text}</p>
      </Panel>
      <Panel header="This is panel header 2" key="2" className="site-collapse-custom-panel">
        <p>{text}</p>
      </Panel>
      <Panel header="This is panel header 3" key="3" className="site-collapse-custom-panel">
        <p>{text}</p>
      </Panel>
    </Collapse>
  ),

  parameters: {
    docs: {
      description: {
        story: 'Customize the background, border, margin styles and icon for each panel.',
      },
    },
  },
};

export const NoArrow = {
  render: () => {
    const onChange = (_key: string | string[]) => {};

    return (
      <Collapse defaultActiveKey={['1']} onChange={onChange}>
        <Panel header="This is panel header with arrow icon" key="1">
          <p>{text}</p>
        </Panel>
        <Panel showArrow={false} header="This is panel header with no arrow icon" key="2">
          <p>{text}</p>
        </Panel>
      </Collapse>
    );
  },

  parameters: {
    docs: {
      description: {
        story:
          'You can hide the arrow icon by passing `showArrow={false}` to `CollapsePanel` component.',
      },
    },
  },
};

type ExpandIconPosition = 'start' | 'end';

export const ExtraNode = {
  render: () => {
    const [expandIconPosition, setExpandIconPosition] = useState<ExpandIconPosition>('start');

    const onPositionChange = (newExpandIconPosition: ExpandIconPosition) => {
      setExpandIconPosition(newExpandIconPosition);
    };

    const onChange = (key: string | string[]) => {
      console.log(key);
    };

    const genExtra = () => (
      <SettingOutlined
        onClick={event => {
          // If you don't want click extra trigger collapse, you can prevent this:
          event.stopPropagation();
        }}
      />
    );

    return (
      <>
        <Collapse
          defaultActiveKey={['1']}
          onChange={onChange}
          expandIconPosition={expandIconPosition}
        >
          <Panel header="This is panel header 1" key="1" extra={genExtra()}>
            <div>{text}</div>
          </Panel>
          <Panel header="This is panel header 2" key="2" extra={genExtra()}>
            <div>{text}</div>
          </Panel>
          <Panel header="This is panel header 3" key="3" extra={genExtra()}>
            <div>{text}</div>
          </Panel>
        </Collapse>
        <br />
        <span>Expand Icon Position: </span>
        <Select value={expandIconPosition} style={{ margin: '0 8px' }} onChange={onPositionChange}>
          <Option value="start">start</Option>
          <Option value="end">end</Option>
        </Select>
      </>
    );
  },

  parameters: {
    docs: {
      description: {
        story:
          'More than one panel can be expanded at a time, the first panel is initialized to be active in this case.',
      },
    },
  },
};

export const GhostCollapse = {
  render: () => (
    <Collapse defaultActiveKey={['1']} ghost>
      <Panel header="This is panel header 1" key="1">
        <p>{text}</p>
      </Panel>
      <Panel header="This is panel header 2" key="2">
        <p>{text}</p>
      </Panel>
      <Panel header="This is panel header 3" key="3">
        <p>{text}</p>
      </Panel>
    </Collapse>
  ),

  parameters: {
    docs: {
      description: {
        story: `Making collapse's background to transparent.`,
      },
    },
  },
};

export const Collapsible = {
  render: () => (
    <Space direction="vertical">
      <Collapse collapsible="header" defaultActiveKey={['1']}>
        <Panel header="This panel can only be collapsed by clicking text" key="1">
          <p>{text}</p>
        </Panel>
      </Collapse>
      <Collapse collapsible="icon" defaultActiveKey={['1']}>
        <Panel header="This panel can only be collapsed by clicking icon" key="1">
          <p>{text}</p>
        </Panel>
      </Collapse>
      <Collapse collapsible="disabled">
        <Panel header="This panel can't be collapsed" key="1">
          <p>{text}</p>
        </Panel>
      </Collapse>
    </Space>
  ),

  parameters: {
    docs: {
      description: {
        story: 'Specify the trigger area of collapsible by `collapsible`.',
      },
    },
  },
};

export const CollapsePanel: StoryFn<any> = () => {
  const dataSource = [
    {
      key: '1',
      property: 'collapsible',
      description: `Specify whether the panel be collapsible or the trigger area of collapsible	`,
      type: 'header | icon | disabled',
      default: '-',
    },
    {
      key: '2',
      property: 'extra',
      description: 'The extra element in the corner	',
      type: 'ReactNode',
      default: '-',
    },
    {
      key: '3',
      property: 'forceRender',
      description:
        'Forced render of content on panel, instead of lazy rendering after clicking on header',
      type: 'boolean',
      default: 'false',
    },
    {
      key: '4',
      property: 'header',
      description: 'Title of the panel	',
      type: 'ReactNode',
      default: '-',
    },
    {
      key: '5',
      property: 'key',
      description: 'Unique key identifying the panel from among its siblings',
      type: 'string | number',
      default: '-',
    },
    {
      key: '6',
      property: 'showArrow',
      description: `If false, panel will not show arrow icon. If false, collapsible can't be set as icon`,
      type: 'boolean',
      default: true,
    },
  ];

  return <Table dataSource={dataSource} columns={TABLE_API_COLUMNS} pagination={false} />;
};
