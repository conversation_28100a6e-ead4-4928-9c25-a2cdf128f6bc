import React, { useState } from 'react';
import type { Meta, StoryFn } from '@storybook/react';

import { GradientSetting } from './index';

export default {
  title: 'Molecules/GradientSetting',
  component: GradientSetting,
  argTypes: {
    onChange: {
      name: 'onChange',
      defaultValue: undefined,
      description: 'Return gradient config whenever settings change',
    },
  },
  parameters: {
    docs: {
      description: {
        component:
          'Usually place in setting side panel and wrapper by SettingWrapper component to setting background of a particular element',
      },
    },
  },
} as Meta<typeof GradientSetting>;

// Default
const Template: StoryFn<typeof GradientSetting> = args => {
  const [backgroundProperty, setBackgroundProperty] = useState('');

  return (
    <div style={{ display: 'flex', gap: '12px' }}>
      <div style={{ flex: 1 }}>
        <div
          style={{
            background: backgroundProperty,
            width: '100%',
            height: 300,
            borderRadius: '4px',
          }}
        />
        <p style={{ marginTop: 12, fontSize: 16 }}>
          CSS background property:
          <br />
          <code
            style={{
              borderRadius: 4,
              padding: '4px 6px',
              marginTop: '8px',
              backgroundColor: '#c0c0c0',
              lineHeight: '32px',
            }}
          >
            {backgroundProperty}
          </code>
        </p>
      </div>
      <GradientSetting
        {...args}
        getCSSProperty={value => setBackgroundProperty(value)}
        style={{ flex: 1 }}
      />
    </div>
  );
};

export const Default = {
  render: Template,

  args: {
    onChange: value => console.log(value),
    customColors: undefined,
  },
};
