/* eslint-disable react/button-has-type */
import React from 'react';
import Quill from 'quill';
import styled from 'styled-components';

interface QuillEditor {
  quill: any;
}

const CustomUndo = () => (
  <svg viewBox="0 0 18 18">
    <polygon className="ql-fill ql-stroke" points="6 10 4 12 2 10 6 10" />
    <path className="ql-stroke" d="M8.09,13.91A4.6,4.6,0,0,0,9,14,5,5,0,1,0,4,9" />
  </svg>
);

const CustomRedo = () => (
  <svg viewBox="0 0 18 18">
    <polygon className="ql-fill ql-stroke" points="12 10 14 12 16 10 12 10" />
    <path className="ql-stroke" d="M9.91,13.91A4.6,4.6,0,0,1,9,14a5,5,0,1,1,5-5" />
  </svg>
);

function undoChange(this: QuillEditor) {
  this.quill.history.undo();
}
function redoChange(this: QuillEditor) {
  this.quill.history.redo();
}

// const Size: any = Quill.import('formats/size');
// Size.whitelist = ['small', 'medium', 'large']; // 'extra-small',
// Quill.register(Size, true);

const SizeStyle: any = Quill.import('attributors/style/size');
SizeStyle.whitelist = [
  '12px',
  '14px',
  '16px',
  '18px',
  '20px',
  '22px',
  '24px',
  '26px',
  '28px',
  '30px',
];
Quill.register(SizeStyle, true);

// const icons: any = Quill.import('ui/icons');
// icons.link = `<svg width="24" height="24" focusable="false">
//                   <path
//                     d="M6.2 12.3a1 1 0 011.4 1.4l-2.1 2a2 2 0 102.7 2.8l4.8-4.8a1 1 0 000-1.4 1 1 0 111.4-1.3 2.9 2.9 0 010 4L9.6 20a3.9 3.9 0 01-5.5-5.5l2-2zm11.6-.6a1 1 0 01-1.4-1.4l2-2a2 2 0 10-2.6-2.8L11 10.3a1 1 0 000 1.4A1 1 0 119.6 13a2.9 2.9 0 010-4L14.4 4a3.9 3.9 0 015.5 5.5l-2 2z"
//                     fillRule="nonzero"
//                   />
//                 </svg>`;

// const Font: any = Quill.import('formats/font');
// Font.whitelist = ['arial', 'comic-sans', 'courier-new', 'georgia', 'helvetica', 'lucida'];
// Quill.register(Font, true);

export const modules = id => ({
  toolbar: {
    container: `#${id}`,
    handlers: {
      undo: undoChange,
      redo: redoChange,
    },
  },
  history: {
    delay: 500,
    maxStack: 100,
    userOnly: true,
  },
});

export const formats = [
  'header',
  'font',
  'size',
  'bold',
  'italic',
  'underline',
  'align',
  'strike',
  'script',
  'blockquote',
  'background',
  'list',
  // 'bullet',
  'indent',
  'link',
  'image',
  'color',
  'code-block',
  'imageBlot',
];

const ToolBarWrapper = styled.div<{ isRoundCorner?: boolean; borderColor: string }>`
  border: 1px solid ${props => props.borderColor} !important;
  border-bottom: none !important;
  border-radius: ${props => (props.isRoundCorner ? '10px 10px 0 0' : '0')};
  * {
    font-weight: 400;
  }
`;

export const QuillToolbar = ({ id, isRoundCorner = true, borderColor }) => (
  <ToolBarWrapper id={id} isRoundCorner={isRoundCorner} borderColor={borderColor}>
    <span className="ql-formats">
      {/* <select className="ql-font" defaultValue="false">
        <option value="false">Sans Serif</option>
        <option value="serif">Serif</option>
        <option value="monospace">Monospace</option>
      </select> */}
      <select className="ql-size" defaultValue="12px">
        {/* <option value="extra-small">Size 1</option> */}
        <option value="12px">Paragraph</option>
        <option value="24px">Heading 1</option>
        <option value="18px">Heading 2</option>
        <option value="14px">Heading 3</option>
      </select>
      {/* <select className="ql-header" defaultValue="3">
        <option value="1">Heading1</option>
        <option value="2">Heading2</option>
        <option value="3">Heading3</option>
      </select> */}
    </span>
    <span className="ql-formats">
      <button className="ql-bold" />
      <button className="ql-italic" />
      <button className="ql-underline" />
      <button className="ql-strike" />
    </span>
    <span className="ql-formats">
      <button className="ql-list" value="ordered" />
      <button className="ql-list" value="bullet" />
      <button className="ql-indent" value="-1" />
      <button className="ql-indent" value="+1" />
    </span>
    {/* <span className="ql-formats">
      <button className="ql-script" value="super" />
      <button className="ql-script" value="sub" />
      <button className="ql-direction" />
    </span> */}
    <span className="ql-formats">
      <select className="ql-align" />
      <select className="ql-color" />
      <select className="ql-background" />
    </span>
    <span className="ql-formats">
      <button className="ql-link" />
      <button className="ql-image" />
      {/* <button className="ql-video" /> */}
    </span>
    <span className="ql-formats">
      {/* <button className="ql-formula" /> */}
      <button className="ql-blockquote" />
      <button className="ql-code-block" />
      <button className="ql-clean" />
    </span>
    <span className="ql-formats">
      <button className="ql-undo">
        <CustomUndo />
      </button>
      <button className="ql-redo">
        <CustomRedo />
      </button>
    </span>
  </ToolBarWrapper>
);

export default QuillToolbar;
