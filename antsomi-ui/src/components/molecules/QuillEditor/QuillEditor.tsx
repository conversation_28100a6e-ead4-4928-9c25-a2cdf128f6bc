import React, { useCallback, useEffect, useRef, useState } from 'react';
import Quill from 'quill';
import { isFunction } from 'lodash';
import QuillImageDropAndPaste from 'quill-image-drop-and-paste';

// Hooks
import debounce from 'lodash/debounce';

// Components
import EditorToolbar, { modules, formats } from './EditorToolbar';

// Styles
import 'quill/dist/quill.snow.css';
import { EditorWrapper, LoadingOverlay, QuillWrapper } from './styled';
import { Spin } from '../../atoms';

const Block: any = Quill.import('blots/block');
Block.tagName = 'DIV';
Quill.register(Block, true);
Quill.register('modules/imageDropAndPaste', QuillImageDropAndPaste);

export enum TCallBack {
  LOADING_UPLOAD = 'LOADING_UPLOAD',
  ERROR = 'error',
}

type TEditorProps = {
  value: string;
  onChange: (value: string) => void;
  callback?: (type: TCallBack, value: string | boolean) => void;
  placeholder: string;
  uploadService?: Function;
  height?: string | number;
  maxImgHeight?: string;
  borderColor?: string;
  isRoundCorner?: boolean;
};

export const QuillEditor = ({
  value,
  onChange,
  placeholder = '',
  uploadService,
  height = 'auto',
  maxImgHeight = '300px',
  borderColor = '#d4d4d4',
  isRoundCorner,
  callback = () => {},
}: TEditorProps) => {
  const editor = useRef<Quill | null>(null);
  const rand = useRef<string>(String(Math.random()).replace(/\./g, ''));
  const [isUploading, setIsUploading] = useState(false);

  const processText = () => {
    editor.current?.root.querySelectorAll('li').forEach(li => {
      let max = 0;
      li.querySelectorAll('[style*="font-size"]').forEach(el => {
        const fontS = +(el as HTMLElement).style.fontSize.replace(/\D/g, '');
        max = fontS > max ? fontS : max;
      });

      li.style.fontSize = max ? `${max}px` : 'unset';
    });
  };

  const debounceQuickProcess = debounce(processText, 100);
  const debounceChange = debounce(onChange, 500);

  // Upload handler function
  const _onUpload = async file => {
    try {
      setIsUploading(true);
      callback(TCallBack.LOADING_UPLOAD, true);

      const result = await uploadService!({ files: [file], mode: 'file' });
      if (result?.data?.length) {
        return result.data[0].url;
      }

      return '';
    } finally {
      setIsUploading(false);
      isFunction(callback) && callback(TCallBack.LOADING_UPLOAD, false);
    }
  };

  const handlerDropImage = async (_imageDataUrl, _type, imageData) => {
    callback(TCallBack.ERROR, 'RESET');
    const file = imageData.toFile();

    if (!editor.current) return;

    if (file.size > 10 * 1024 * 1024) {
      callback(TCallBack.ERROR, 'FILE_TOO_LARGE');
      return;
    }

    const range = editor.current?.getSelection();
    if (!range) return;

    // Insert loading placeholder

    try {
      const fileUrl = await _onUpload(file);

      if (fileUrl) {
        const currentRange = editor.current?.getSelection();
        editor.current?.insertEmbed(
          currentRange?.index || range.index,
          'image',
          fileUrl,
          Quill.sources.USER,
        );
      }
    } catch (error) {
      callback(TCallBack.ERROR, 'UPLOAD_FAILED');
    }
  };

  const imageHandler = useCallback(() => {
    callback(TCallBack.ERROR, 'RESET');

    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.click();
    input.onchange = async () => {
      if (input !== null && input.files !== null) {
        if (!editor.current) return;

        const file = input.files[0];

        if (file.size > 10 * 1024 * 1024) {
          callback(TCallBack.ERROR, 'FILE_TOO_LARGE');
          return;
        }

        const range = editor.current?.getSelection();
        if (!range) return;

        try {
          const fileUrl = await _onUpload(file);

          if (fileUrl) {
            const currentRange = editor.current?.getSelection();
            editor.current?.insertEmbed(
              currentRange?.index || range.index,
              'image',
              fileUrl,
              Quill.sources.USER,
            );
          }
        } catch (error) {
          callback(TCallBack.ERROR, 'UPLOAD_FAILED');
        }
      }
    };
  }, []);

  useEffect(() => {
    if (!value && editor.current) {
      editor.current.setText('');
    }
  }, [value]);

  useEffect(() => {
    if (editor.current) return;

    const configModule = modules(`toolbar-${rand.current}`);

    editor.current = new Quill(`#editor-${rand.current}`, {
      theme: 'snow',
      modules: {
        ...configModule,
        toolbar: {
          ...configModule.toolbar,
          handlers: {
            ...configModule.toolbar.handlers,
            image: imageHandler,
          },
        },
        imageDropAndPaste: {
          handler: handlerDropImage,
        },
      },
      formats,
      placeholder,
    });

    editor.current.root.innerHTML = value || '';

    editor.current.on('text-change', (delta, oldDelta, source) => {
      // if (source === 'api') {}
      if (source === 'user') {
        debounceQuickProcess();
        if (editor.current?.getText().trim().length === 0) {
          debounceChange('');
        } else {
          debounceChange(editor.current?.root.innerHTML || '');
        }
        callback(TCallBack.ERROR, 'RESET');
      }
    });
  }, []);

  return (
    <QuillWrapper>
      <EditorToolbar
        id={`toolbar-${rand.current}`}
        isRoundCorner={isRoundCorner}
        borderColor={borderColor}
      />
      <EditorWrapper
        id={`editor-${rand.current}`}
        style={{ height, color: '#000000' }}
        borderColor={borderColor}
      >
        {isUploading && (
          <LoadingOverlay>
            <Spin spinning={isUploading} />
          </LoadingOverlay>
        )}
      </EditorWrapper>
    </QuillWrapper>
  );
};
