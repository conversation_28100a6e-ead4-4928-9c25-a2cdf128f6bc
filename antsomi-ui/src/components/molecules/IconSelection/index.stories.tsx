/* eslint-disable no-console */
import { Meta } from '@storybook/react';

import { IconSelection } from './index';
import { ICON_TYPE } from './constants';

export default {
  title: 'Molecules/IconSelection',
  component: IconSelection,
  argTypes: {},
  parameters: {},
} as Meta<typeof IconSelection>;

export const Default = {
  args: {
    onChange: icon => console.log(icon),
    style: {
      maxWidth: '400px',
    },
  },
};

export const SelectedIcon = {
  args: {
    iconTypes: [ICON_TYPE.FONT_AWESOME, ICON_TYPE.CUSTOM],
    icon: 'cus local_mall',
  },
};

export const SelectedWithoutIcontypePrefix = {
  args: {
    icon: 'fas address-book',
  },
};

export const AddOtherIconType = {
  args: {
    icon: 'cus interactive',
    iconTypes: [ICON_TYPE.FONT_AWESOME, ICON_TYPE.CUSTOM],
  },
};
