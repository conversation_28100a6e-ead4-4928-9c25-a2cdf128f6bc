import { Meta } from '@storybook/react';
import { IconSelectionRenderer } from '.';
import { ICON_TYPE } from '../../constants';

export default {
  title: 'Molecules/IconSelection/IconSelectionRenderer',
  component: IconSelectionRenderer,
  argTypes: {},
  parameters: {},
} as Meta<typeof IconSelectionRenderer>;

export const MUIIcon = {
  args: {
    iconName: 'fact_check',
    iconType: ICON_TYPE.CUSTOM,
    fontSize: '40px',
  },
};

export const FontawesomeIcon = {
  args: {
    iconName: 'fas address-card',
    iconType: ICON_TYPE.FONT_AWESOME,
    fontSize: 40,
  },
};
