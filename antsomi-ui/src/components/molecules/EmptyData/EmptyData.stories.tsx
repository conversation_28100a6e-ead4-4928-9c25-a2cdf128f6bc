// Libraries
import React, { useState } from 'react';
import { <PERSON>a, StoryObj } from '@storybook/react';
import { SmileOutlined } from '@ant-design/icons';
import { ConfigProvider, List, Transfer } from 'antd';

// Components
import { EmptyData } from './EmptyData';
import {
  Button,
  Switch,
  Divider,
  Space,
  Flex,
  Typography,
  Row,
  Col,
} from '@antscorp/antsomi-ui/es/components/atoms';

const meta: Meta<typeof EmptyData> = {
  title: 'Molecules/EmptyData',
  component: EmptyData,
  argTypes: {
    size: {
      name: 'size',
      description: 'Customize size',
      table: {
        type: { summary: 'TUISize' },
        defaultValue: { summary: 'medium' },
      },
      control: {
        type: 'select',
        labels: {
          small: 'Small',
          medium: 'Medium',
          large: 'Large',
        },
      },
      options: ['small', 'medium', 'large'],
    },
    title: {
      name: 'title',
      description: 'Customize title, will be displayed if provided',
      table: {
        type: { summary: 'ReactNode' },
        defaultValue: { summary: '' },
      },
      control: 'text',
    },
    subTitle: {
      name: 'subTitle',
      description: 'Customize subTitle, will be displayed if provided',
      table: {
        type: { summary: 'ReactNode' },
        defaultValue: { summary: '' },
      },
      control: 'text',
    },
    description: {
      name: 'description',
      description: 'Customize description, will be displayed if provided',
      table: {
        type: { summary: 'ReactNode' },
        defaultValue: { summary: '' },
      },
      control: 'text',
    },
    icon: {
      name: 'icon',
      description: 'Customize icon',
      table: {
        type: { summary: 'ReactNode' },
        defaultValue: { summary: '' },
      },
      control: 'text',
    },
    showIcon: {
      name: 'showIcon',
      description: 'Customize showIcon',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'true' },
      },
      control: 'boolean',
    },
    image: {
      name: 'image',
      description: 'Customize image. Will treat as image url when string provided	',
      table: {
        type: { summary: 'ReactNode' },
        defaultValue: { summary: 'Empty.PRESENTED_IMAGE_DEFAULT' },
      },
      control: null,
    },
    imageStyle: {
      name: 'imageStyle',
      description: 'The style of image',
      table: {
        type: { summary: 'CSSProperties' },
      },
      control: null,
    },
    children: {
      name: 'children',
      description: 'Customize children',
      table: {
        type: { summary: 'ReactNode' },
        defaultValue: { summary: '' },
      },
      control: null,
    },
  },
  parameters: {
    docs: {
      description: {
        component:
          'Empty state placeholder.' +
          '\n### When To Use' +
          '\n' +
          '- When there is no data provided, display for friendly tips.' +
          '\n' +
          '- User tutorial to create something in fresh new situation.' +
          '\n',
      },
    },
  },
} as Meta<typeof EmptyData>;

export default meta;
type Story = StoryObj<typeof EmptyData>;

export const Default: Story = {
  render: args => <EmptyData {...args} />,
  args: {
    size: 'medium',
    icon: 'icon-ants-media',
    title: 'No data available',
    subTitle: "There's nothing here",
    description: 'Add some data to see it here',
  },
  parameters: {},
};

export const Size: Story = {
  render: args => (
    <Flex align="center" gap={10}>
      <EmptyData size="small" {...args} />
      <EmptyData size="medium" {...args} />
      <EmptyData size="large" {...args} />
    </Flex>
  ),
  args: {
    title: 'No data available',
    subTitle: "There's nothing here",
    description: 'Add some data to see it here',
  },
  parameters: {
    docs: {
      description: {
        story: 'You can customize the size of `EmptyData`.',
      },
    },
  },
};

export const ShowHideText: Story = {
  render: args => (
    <Row justify="center">
      <Col span={6}>
        <Flex align="center" vertical gap={10}>
          <Typography.Text strong>Show Title, Hide SubTitle/Description</Typography.Text>
          <EmptyData size="small" {...args} />
        </Flex>
      </Col>
      <Col span={6}>
        <Flex align="center" vertical gap={10}>
          <Typography.Text strong>Hide Title, Show SubTitle/Description</Typography.Text>
          <EmptyData size="small" {...args} title={undefined} />
        </Flex>
      </Col>
      <Col span={6}>
        <Flex align="center" vertical gap={10}>
          <Typography.Text strong>Show Title/Description, Hide Subtitle</Typography.Text>
          <EmptyData size="small" {...args} subTitle={undefined} />
        </Flex>
      </Col>
      <Col span={6}>
        <Flex align="center" vertical gap={10}>
          <Typography.Text strong>Show Title, Hide Subtitle/Description</Typography.Text>
          <EmptyData size="small" {...args} subTitle={undefined} description={undefined} />
        </Flex>
      </Col>
    </Row>
    // <Row >
    //   <Flex align="center" vertical gap={10}>
    //     <Typography.Text>Show Title, Hide SubTitle/Description</Typography.Text>
    //     <EmptyData size="small" {...args} />
    //   </Flex>
    //   <EmptyData size="medium" {...args} />
    //   <EmptyData size="large" {...args} />
    // </Row>
  ),
  args: {
    title: 'No data available',
    subTitle: "There's nothing here",
    description: 'Add some data to see it here',
  },
  parameters: {
    docs: {
      description: {
        story: 'You can customize whether to show description, title, subTitle or not.',
      },
    },
  },
};

export const ExtraContent: Story = {
  render: args => (
    <Flex align="center" gap={10}>
      <EmptyData
        title="No data available"
        {...args}
        // eslint-disable-next-line react/no-children-prop
        children={
          <Flex align="center" gap={10}>
            <Button type="primary">Continue</Button>
            <Button>Cancel</Button>
          </Flex>
        }
      />

      <EmptyData
        size="small"
        title="You have not created any dashboard "
        subTitle="Chose a report now"
        {...args}
        icon="icon-ants-grid-view"
        // eslint-disable-next-line react/no-children-prop
        children={<Button type="primary">Choose a report</Button>}
      />
    </Flex>
  ),
  args: {
    icon: 'icon-ants-table-vertical',
  },
  parameters: {
    docs: {
      description: {
        story: 'You can add extra content to `EmptyData`.',
      },
    },
  },
};

// export const ChoseImage = {
//   render: () => (
//     <EmptyData image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg" />
//   ),

//   parameters: {
//     docs: {
//       description: {
//         story:
//           'You can choose another style of image by setting `image` to `Empty.PRESENTED_IMAGE_SIMPLE`.',
//       },
//     },
//   },
// };

// export const Customize = {
//   render: () => (
//     <EmptyData
//       image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
//       imageStyle={{ height: 60 }}
//       description={
//         <span>
//           Customize <a href="#">Description</a>
//         </span>
//       }
//     >
//       <Button type="primary">Create Now</Button>
//     </EmptyData>
//   ),

//   parameters: {
//     docs: {
//       description: {
//         story: 'Customize image source, image size, description and extra content.',
//       },
//     },
//   },
// };

// export const ConfigProviderEmpty = {
//   render: () => {
//     const customizeRenderEmpty = () => (
//       <div style={{ textAlign: 'center' }}>
//         <SmileOutlined style={{ fontSize: 20 }} />
//         <p>Data Not Found</p>
//       </div>
//     );
//     const style: React.CSSProperties = { width: 200 };
//     const [customize, setCustomize] = useState(true);
//     return (
//       <>
//         <Switch
//           unCheckedChildren="default"
//           checkedChildren="customize"
//           checked={customize}
//           onChange={setCustomize}
//         />
//         <Divider />
//         <ConfigProvider renderEmpty={customize ? customizeRenderEmpty : undefined}>
//           <Space direction="vertical" style={{ width: '100%' }}>
//             <h4>Select</h4>
//             <Select style={style} />
//             <h4>TreeSelect</h4>
//             <TreeSelect style={style} treeData={[]} />
//             <h4>Cascader</h4>
//             <Cascader style={style} options={[]} showSearch />
//             <h4>Transfer</h4>
//             <Transfer />
//             <h4>Table</h4>
//             <Table
//               style={{ marginTop: 8 }}
//               columns={[
//                 { title: 'Name', dataIndex: 'name', key: 'name' },
//                 { title: 'Age', dataIndex: 'age', key: 'age' },
//               ]}
//             />
//             <h4>List</h4>
//             <List />
//           </Space>
//         </ConfigProvider>
//       </>
//     );
//   },

//   parameters: {
//     docs: {
//       description: {
//         story: 'Use ConfigProvider set global Empty style.',
//       },
//     },
//   },
// };
