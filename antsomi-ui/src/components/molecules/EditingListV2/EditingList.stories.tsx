/* eslint-disable react/jsx-boolean-value */
// Libraries
import { Meta, StoryObj } from '@storybook/react/*';
import { useState } from 'react';

// Components
import { EditingListV2 } from './EditingList';

const meta = {
  title: 'Molecules/EditingListV2',
  component: EditingListV2,
} satisfies Meta<typeof EditingListV2>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default = {
  args: {
    options: [
      {
        key: 'account_id',
        label: 'Account ID',
        errorMessage: 'Error message',
      },
      {
        key: 'ticket_id',
        label: 'Ticket ID',
      },
      {
        key: 'sia_id',
        label: 'Sia ID',
      },
      {
        key: 'created_at',
        label: 'Created At',
      },
      // {
      //   key: 'updated_at',
      //   label: (
      //     <Flex align="center">
      //       <CurvedConnectorIcon />
      //       Updated At
      //     </Flex>
      //   ),
      // },
      {
        key: 'long_attr',
        errorMessage: 'Error message',
        label:
          'Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr Long Attr',
      },
      {
        key: '124',
        label: 'Test search',
        errorMessage: 'Error message',
        search: '124 Test search',
      },
    ],
  },
  render: args => {
    const { options, ...restArgs } = args;
    const [selected, setSelected] = useState(['account_id', 'ticket_id', '124']);

    // const removable = selected.length > 1;

    return (
      <div style={{ height: 400, width: 300 }}>
        <EditingListV2
          {...restArgs}
          removable={true}
          selected={selected}
          options={options}
          onChange={v => setSelected(v)}
        />
      </div>
    );
  },
} satisfies Story;
