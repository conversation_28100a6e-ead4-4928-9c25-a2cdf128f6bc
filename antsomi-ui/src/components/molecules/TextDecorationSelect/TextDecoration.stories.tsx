// FontFamilySelect.stories.tsx

import { Meta } from '@storybook/react';

import { TextDecorationSelect } from '.';

export default {
  title: 'Molecules/TextDecorationSelect',
  component: TextDecorationSelect,
} as Meta;

export const Default = {
  args: {
    defaultValue: 'none',
    value: 'none',
    label: 'Text Dedoration',
    onChange: (select: string) => {
      console.log(`Selected text decoration: ${select}`);
    },
  },
};

export const LimitTextDecoration = {
  args: {
    defaultValue: 'none',
    value: 'none',
    label: 'Text Dedoration Limit',
    showOptions: ['None', 'Underline', 'Overline'],
    onChange: (select: string) => {
      console.log(`Selected text decoration: ${select}`);
    },
  },

  parameters: {
    docs: {
      description: {
        story: 'Limit text-decoration options by props "showOptions".',
      },
    },
  },
};
