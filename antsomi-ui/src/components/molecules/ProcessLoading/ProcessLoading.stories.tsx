// Libraries
import React from 'react';
import { Meta, StoryObj } from '@storybook/react';

// Components
import { Button, Flex, Typography } from '../../atoms';
import { ProcessLoading } from './ProcessLoading';

// Hooks
import { useToggle } from '@antscorp/antsomi-ui/es/hooks';

const meta: Meta<typeof ProcessLoading> = {
  component: ProcessLoading,
  title: 'Molecules/ProcessLoading',
  argTypes: {
    open: {
      defaultValue: false,
      control: {
        type: 'boolean',
      },
      description: 'Process Loading Open',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
      },
    },
    message: {
      defaultValue: 'Loading ...',
      description: 'Process Loading Message',
      table: {
        type: { summary: 'ReactNode' },
        defaultValue: { summary: 'Loading ...' },
      },
      control: {
        type: 'text',
      },
    },
    timeout: {
      defaultValue: {
        second: 7,
        message: (
          <div>
            This might take a little longer than usual. <br />
            Thank you for waiting!
          </div>
        ),
        show: true,
      },
      description: 'Process Loading Timeout',
      table: {
        type: { summary: 'TimeOut' },
        defaultValue: {
          summary: `{ second: 7, message: <div>
                     This might take a little longer than usual. <br />
            Thank you for waiting!</div>, show: true }`,
        },
      },
      control: {
        type: 'object',
      },
    },
  },
  parameters: {
    docs: {
      description: {
        component: 'Process Loading',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof ProcessLoading>;

// Renders
const RenderDefault = args => {
  const [open, toggle] = useToggle(false);

  return (
    <>
      <Flex vertical gap={10}>
        <Typography.Text>
          Processing notification will be auto hide after 10 seconds
        </Typography.Text>
        <Button
          onClick={() => {
            toggle();

            setTimeout(() => {
              toggle(false);
            }, 10000);
          }}
        >
          Open Process Loading
        </Button>
      </Flex>

      <ProcessLoading open={open} onCancel={() => toggle(false)} {...args} />
    </>
  );
};

const RenderMessage = args => {
  const [open, toggle] = useToggle(false);

  return (
    <>
      <Flex vertical gap={10}>
        <Button
          onClick={() => {
            toggle();

            setTimeout(() => {
              toggle(false);
            }, 10000);
          }}
        >
          Open Process Loading
        </Button>
      </Flex>

      <ProcessLoading open={open} onCancel={() => toggle(false)} {...args} />
    </>
  );
};

export const Default: Story = {
  render: RenderDefault,
  args: {
    message: 'Loading ...',
    timeout: {
      second: 7,
      message: (
        <div>
          This might take a little longer than usual. <br />
          Thank you for waiting!
        </div>
      ),
      show: true,
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'Default',
      },
    },
  },
};

export const CustomMessage: Story = {
  render: RenderMessage,
  args: {
    message: (
      <div>
        Please wait... <br /> <br />
        Your update is currently being processed. Do not refresh or close the tab.
      </div>
    ),
    timeout: {
      message: 'The process is processing so long, thanks for waiting!',
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'Custom Message',
      },
    },
  },
};

export const HideTimeoutMessage: Story = {
  render: RenderMessage,
  args: {
    timeout: {
      show: false,
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'Hide Timeout Message',
      },
    },
  },
};
