// Constants
import { DYNAMIC_CONTENT_TYPE_KEY } from './constants';

export type TDataType =
  | 'string'
  | 'number'
  | 'datetime'
  | 'array'
  | 'boolean'
  | 'object'
  | 'text'
  | 'array_string'
  | 'array_number'
  | 'array_datetime'
  | 'array_text'
  | 'suggestion'
  | 'date'
  | 'object_id'
  | 'unique'
  | null;

export type APIConfig = {
  domain: string;
  slug: string;
  token: string;
  userId: string;
  accountId: string;
};

export type APIParams = {
  url: string;
} & Record<string, any>;

export type DynamicContentType =
  (typeof DYNAMIC_CONTENT_TYPE_KEY)[keyof typeof DYNAMIC_CONTENT_TYPE_KEY];
