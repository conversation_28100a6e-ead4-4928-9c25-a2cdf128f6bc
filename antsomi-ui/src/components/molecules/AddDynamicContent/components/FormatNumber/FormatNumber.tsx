// Libraries
import React, { useEffect, useMemo, useState } from 'react';

// Components
import { Radio } from '@antscorp/antsomi-ui/es/components/atoms';
import { Col, Row, Tooltip, Form, Select, Checkbox } from 'antd';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { ButtonCustom } from '../../styled';

// Json
import Currencies from '../../currencies.json';

// Constants
import { ATTRIBUTE_NUMBERIC_FORMAT_OPTION } from '../../constants';

// Services
import { getCurrencyList } from '@antscorp/antsomi-ui/es/services/MediaTemplateDesign/ListCurrency';

// Locales
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';

// Types
import { TDisplayFormat } from '../DisplayFormat';
import { PayloadInfo } from '@antscorp/antsomi-ui/es/types';

const groupingOptions = ATTRIBUTE_NUMBERIC_FORMAT_OPTION.GROUPING_SEPARATOR;
const decimalOptions = ATTRIBUTE_NUMBERIC_FORMAT_OPTION.DECIMAL_SEPARATOR;

export type TGroupingSeparator = (typeof groupingOptions)[keyof typeof groupingOptions]['value'];
export type TDecimalSeparator = (typeof decimalOptions)[keyof typeof decimalOptions]['value'];

export type TNumberFormatSettings = {
  decimalPlaces: number;
  grouping: TGroupingSeparator;
  decimal: TDecimalSeparator;
  isCompact: boolean;
  currencyCode: string;
  prefixType: 'symbol' | 'code';
  type?: TDisplayFormat;
};

type FormatNumberProps = {
  onSettingsChange?: (settingValues: TNumberFormatSettings) => void;
  type: TDisplayFormat;
} & TNumberFormatSettings & { infos: PayloadInfo; url: string };

type LabeledValue = {
  label: string;
  value: string;
  disabled?: boolean;
};

const FormatNumber = (props: FormatNumberProps) => {
  const { t, language } = i18nInstance;

  const { onSettingsChange, type: displayType, infos, url } = props;

  const { currencyCode, decimalPlaces, grouping, decimal, prefixType, isCompact } = props;

  const settingValues: TNumberFormatSettings = {
    decimalPlaces,
    grouping,
    decimal,
    isCompact,
    currencyCode,
    prefixType,
  };

  const [listCurrencyOptions, setListCurrencyOptions] = useState<LabeledValue[]>([]);
  const [isLoadingCurrencyOptions, setIsLoadingCurrencyOptions] = useState<boolean>(true);

  const onChangeSettingsHandle = (settingValues: TNumberFormatSettings) => {
    if (onSettingsChange) {
      onSettingsChange({
        ...settingValues,
        type: displayType,
      });
    }
  };

  useEffect(() => {
    (async () => {
      // Regex for getting text between the last brackets (with brakets) of line
      const regex = /\(([^)]*)\)[^(]*$/;

      if (listCurrencyOptions.length) {
        setListCurrencyOptions(prevOptions =>
          prevOptions.map(currencyOption => {
            // setLabel: Dollal Mỹ (USD) or Dollal Mỹ ($)
            currencyOption.label =
              prefixType === 'symbol'
                ? currencyOption.label.replace(
                    regex,
                    `(${Currencies[currencyOption.value]?.symbol})`,
                  )
                : currencyOption.label.replace(regex, `(${currencyOption.value})`);

            return currencyOption;
          }),
        );
      } else {
        setIsLoadingCurrencyOptions(true);
        try {
          const data = await getCurrencyList({ lang: language, url }, infos);

          const currencies = data.reduce((accumulate: any, currency: any) => {
            if (Currencies[currency.code]) {
              accumulate.push({
                value: currency.code,
                // setLabel: Dollal Mỹ (USD) or Dollal Mỹ ($)
                label:
                  prefixType === 'symbol'
                    ? `${currency.name.replace(regex, '')} (${Currencies[currency.code]?.symbol})`
                    : `${currency.name.replace(regex, '')} (${currency.code})`,
              });
            } else {
              // console.log('library not found ', currency.code + ' - ' + currency.name);
            }

            return accumulate;
          }, [] as LabeledValue[]);

          setListCurrencyOptions(currencies);
        } catch (err) {
          setListCurrencyOptions([]);
        } finally {
          setIsLoadingCurrencyOptions(false);
        }
      }
    })();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [prefixType]);

  const listGroupingSeparatorOptions = useMemo(
    (): LabeledValue[] =>
      Object.values(groupingOptions)
        .sort((a, b) => a.index - b.index)
        .map(grouping => ({
          ...grouping,
          ...(grouping.value === decimal && { disabled: true }),
        })),
    [decimal],
  );

  const listDecimalSeparatorOptions = useMemo(
    (): LabeledValue[] =>
      Object.values(decimalOptions)
        .sort((a, b) => a.index - b.index)
        .map(decimal => ({
          ...decimal,
          ...(decimal.value === grouping && { disabled: true }),
        })),
    [grouping],
  );

  return (
    <Form style={{ width: '100%', fontSize: '12px' }} labelAlign="left">
      {displayType === 'currency' && (
        <>
          <Form.Item
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            label={
              t(translations.dynamicContent.modal.label.formNumberDisplayFormat.currency) as string
            }
            colon={false}
          >
            <Radio.Group
              value={prefixType}
              onChange={({ target: { value } }) =>
                onChangeSettingsHandle({
                  ...settingValues,
                  prefixType: value as 'code' | 'symbol',
                })
              }
            >
              <Radio style={{ fontSize: 12 }} value="code">
                {
                  t(
                    translations.dynamicContent.modal.label.formNumberDisplayFormat.currencyCode,
                  ) as string
                }
              </Radio>
              <Radio style={{ fontSize: 12 }} value="symbol">
                {
                  t(
                    translations.dynamicContent.modal.label.formNumberDisplayFormat.currencySymbol,
                  ) as string
                }
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label=" " labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} colon={false}>
            <Select
              style={{ height: 28 }}
              disabled={isLoadingCurrencyOptions}
              options={listCurrencyOptions}
              value={currencyCode}
              onChange={value =>
                onChangeSettingsHandle({
                  ...settingValues,
                  currencyCode: value,
                })
              }
              showSearch
            />
          </Form.Item>
        </>
      )}
      <Form.Item>
        <Checkbox
          style={{ fontSize: 12 }}
          checked={isCompact}
          onChange={({ target: { checked } }) => {
            onChangeSettingsHandle({
              ...settingValues,
              isCompact: checked,
            });
          }}
        >
          {t(translations.dynamicContent.modal.label.formNumberDisplayFormat.compact) as string}
        </Checkbox>
      </Form.Item>
      <Form.Item
        label={`${t(
          translations.dynamicContent.modal.label.formNumberDisplayFormat.decimalPlace,
        )} (${decimalPlaces})`}
        style={{ marginBottom: 12 }}
        colon={false}
      >
        <div style={{ display: 'flex', marginLeft: 40 }}>
          <ButtonCustom
            disabled={decimalPlaces <= 0}
            onClick={() =>
              onChangeSettingsHandle({
                ...settingValues,
                decimalPlaces: decimalPlaces - 1,
              })
            }
          >
            {'<< .0'}
          </ButtonCustom>
          <ButtonCustom
            onClick={() =>
              onChangeSettingsHandle({
                ...settingValues,
                decimalPlaces: decimalPlaces + 1,
              })
            }
          >
            {'.00 >>'}
          </ButtonCustom>
        </div>
      </Form.Item>
      <Row gutter={15}>
        <Col span={12}>
          <Form.Item
            label={
              <span>
                {
                  t(
                    translations.dynamicContent.modal.label.formNumberDisplayFormat.grouping,
                  ) as string
                }
                <Tooltip
                  placement="top"
                  title={
                    t(
                      translations.dynamicContent.modal.label.formNumberDisplayFormat
                        .groupingIconTooltipTitle,
                    ) as string
                  }
                  style={{ marginLeft: 4, opacity: 0.4 }}
                >
                  <FontAwesomeIcon icon={['fas', 'info-circle']} />
                </Tooltip>
              </span>
            }
            colon={false}
          >
            <Select
              style={{ height: 28 }}
              value={grouping}
              onChange={value =>
                onChangeSettingsHandle({
                  ...settingValues,
                  grouping: value,
                })
              }
              options={listGroupingSeparatorOptions}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={
              <span>
                {
                  t(
                    translations.dynamicContent.modal.label.formNumberDisplayFormat.decimal,
                  ) as string
                }
                <Tooltip
                  placement="top"
                  title={
                    t(
                      translations.dynamicContent.modal.label.formNumberDisplayFormat
                        .decimalIconTooltipTitle,
                    ) as string
                  }
                  style={{ marginLeft: 4, opacity: 0.4 }}
                >
                  <FontAwesomeIcon icon={['fas', 'info-circle']} />
                </Tooltip>
              </span>
            }
            colon={false}
          >
            <Select
              style={{ height: 28 }}
              value={decimal}
              onChange={value =>
                onChangeSettingsHandle({
                  ...settingValues,
                  decimal: value,
                })
              }
              options={listDecimalSeparatorOptions}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

export default FormatNumber;
