// Libraries
import { Meta, StoryObj } from '@storybook/react';

// Components
import { AddDynamicContent } from './AddDynamicContent';

const meta = {
  title: 'Molecules/AddDynamicContent',
  component: AddDynamicContent,
  args: {},
} satisfies Meta<typeof AddDynamicContent>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    showDisplayFormat: true,
    showIndex: true,
    APIConfig: {
      domain: 'https://sandbox-media-template.antsomi.com',
      slug: '/api/v1',
      token: '',
      userId: '',
      accountId: '',
    },
  },
};
