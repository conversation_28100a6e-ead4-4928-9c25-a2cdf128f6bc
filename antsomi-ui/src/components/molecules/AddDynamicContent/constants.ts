// Types
import { TDataType } from './types';

export const DYNAMIC_CONTENT_SETTING_KEY = {
  EVENT: 'event',
  INDEX: 'index',
  ATTRIBUTE: 'attribute',
  DYNAMIC_CONTENT_NUMBERIC_ATTR_FORMAT_SETTINGS: 'numberFormatSettings',
  DYNAMIC_CONTENT_DATETIME_ATTR_FORMAT_SETTINGS: 'datetimeFormatSettings',
  DISPLAY_FORMAT_TYPE: 'dfType',
  DYNAMIC_CONTENT_TYPE: 'type',
  PROMOTION_POOL: 'pool',
  SOURCE: 'source',
  FUNCTION: 'customFunction',
  PERNAME: 'templateName',
  SAVE_AS_TEMPLATE: 'saveAsTemplate',
  DATA_TYPE: 'dataType',
};

export const DYNAMIC_CONTENT_TYPE_KEY = {
  BO_SETTINGS: 'bo-settings',
  CUSTOMER_ATTRIBUTE: 'customer-attribute',
  EVENT_ATTRIBUTE: 'event-attribute',
  PROMOTION_CODE: 'promotion-code',
  VISITOR_ATTRIBUTE: 'visitor-attribute',
  CUSTOM_FUNCTION: 'custom',
} as const;

export const DYNAMIC_CONTENT_TYPE: {
  [key: string]: {
    index: number;
    label: string;
    value: string;
  };
} = {
  BO_SETTINGS: {
    index: 4,
    label: '',
    value: DYNAMIC_CONTENT_TYPE_KEY.BO_SETTINGS,
  },
  CUSTOMER_ATTRIBUTE: {
    index: 1,
    label: 'Customer Attribute',
    value: DYNAMIC_CONTENT_TYPE_KEY.CUSTOMER_ATTRIBUTE,
  },
  EVENT_ATTRIBUTE: {
    index: 2,
    label: 'Event Attribute',
    value: DYNAMIC_CONTENT_TYPE_KEY.EVENT_ATTRIBUTE,
  },
  PROMOTION_CODE: {
    index: 3,
    label: 'Promotion Code',
    value: DYNAMIC_CONTENT_TYPE_KEY.PROMOTION_CODE,
  },
  VISITOR_ATTRIBUTE: {
    index: 0,
    label: 'Visitor Attribute',
    value: DYNAMIC_CONTENT_TYPE_KEY.VISITOR_ATTRIBUTE,
  },
};

export const DYNAMIC_CONTENT_ATTR_DF_TYPE: { label: string; value: string; dataType: TDataType }[] =
  [
    {
      label: 'Number',
      value: 'number',
      dataType: 'number',
    },
    {
      label: 'Percentage',
      value: 'percentage',
      dataType: 'number',
    },
    {
      label: 'Currency',
      value: 'currency',
      dataType: 'number',
    },
    {
      label: 'Datetime',
      value: 'datetime',
      dataType: 'datetime',
    },
    {
      label: 'Raw string',
      value: 'raw_string',
      dataType: 'string',
    },
  ];

export const DATA_TYPE: {
  [key: string]: {
    index: number;
    label: string;
    value: string;
  };
} = {
  NUMBER: {
    index: 0,
    label: 'Number',
    value: 'number',
  },
  DATE_TIME: {
    index: 1,
    label: 'Datetime',
    value: 'datetime',
  },
  STRING: {
    index: 2,
    label: 'String',
    value: 'string',
  },
};

export const ATTRIBUTE_STATUS = {
  4: {
    value: 4,
    label: 'Archive',
    errorMessage: 'This attribute is not available',
  },
  DELETE: {
    value: 3,
    label: 'Delete',
    errorMessage: 'This attribute does not exist',
  },
};

export const ATTRIBUTE_TYPE = {
  ITEM: 2,
};

export const BO_STATUS = {
  4: {
    value: 4,
    label: 'Archive',
    errorMessage: 'This BO is archive',
  },
  DELETE: {
    value: 3,
    label: 'Delete',
    errorMessage: 'This BO does not exist',
  },
};

export const ATTRIBUTE_NUMBERIC_FORMAT_OPTION = {
  DECIMAL_SEPARATOR: {
    COMMA: {
      index: 0,
      label: ',',
      value: ',',
    },
    DOT: {
      index: 1,
      label: '.',
      value: '.',
    },
  },
  GROUPING_SEPARATOR: {
    COMMA: {
      index: 0,
      label: ',',
      value: ',',
    },
    DOT: {
      index: 1,
      label: '.',
      value: '.',
    },
    NONE: {
      index: 2,
      label: 'none',
      value: 'none',
    },
  },
} as const;

export const COLLECTION_STATUS = {
  2: {
    value: 2,
    label: 'Disable',
    errorMessage: 'This collection is disabled',
  },
  4: {
    value: 4,
    label: 'Archive',
    errorMessage: 'This collection is not available',
  },
  DELETE: {
    value: 3,
    label: 'Delete',
    errorMessage: 'This collection does not exist',
  },
};
