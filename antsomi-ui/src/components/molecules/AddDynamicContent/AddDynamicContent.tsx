/* eslint-disable no-case-declarations */
/* eslint-disable @typescript-eslint/no-use-before-define */
// Libraries
import { useQueryClient } from '@tanstack/react-query';
import { flatMapDeep, get, isEmpty, isNaN, omit, set } from 'lodash';
import React, { useCallback, useEffect, useLayoutEffect, useMemo, useState } from 'react';

// Hooks
import { useDeepCompareEffect, useDeepCompareMemo } from '@antscorp/antsomi-ui/es/hooks';

// Components
import { Button } from '@antscorp/antsomi-ui/es/components/atoms/Button';
import { Checkbox } from '@antscorp/antsomi-ui/es/components/atoms/Checkbox';
import { Spin } from '@antscorp/antsomi-ui/es/components/atoms/Spin';
import { Text } from '@antscorp/antsomi-ui/es/components/atoms/Text';
import { Form, Select, TreeSelect } from 'antd';
import { TreeNode } from 'rc-tree-select';

// Locales
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';

// Query
import {
  useAddSavedCSFunction,
  useGetCustomFunction,
  useGetDynamicContentAttr,
  useGetListAllEvents,
  useGetListAttributeBO,
  useGetListBO,
  useGetListEventAttr,
  useGetListPromotionCodeAttr,
  useGetListPromotionPool,
  useGetListSourceByEvent,
  useUpdateCSFunction,
} from '@antscorp/antsomi-ui/es/queries';
import { useGetEventTrackingAttributes } from '@antscorp/antsomi-ui/es/queries/ThirdParty';

// Queries config
import { QUERY_KEYS } from '@antscorp/antsomi-ui/es/constants/queries';

// Types
import DisplayFormat, { TDisplayFormat } from './components/DisplayFormat';
import { dateExample, TDatetimeFormatSetting } from './components/FormatDatetime';
import { TNumberFormatSettings } from './components/FormatNumber';
import { APIConfig, TDataType } from './types';

// Constants
import {
  ATTRIBUTE_TYPE,
  DATA_TYPE,
  DYNAMIC_CONTENT_ATTR_DF_TYPE,
  DYNAMIC_CONTENT_SETTING_KEY,
  DYNAMIC_CONTENT_TYPE,
} from './constants';

// Utils
import { handleError } from '@antscorp/antsomi-ui/es/utils';
import {
  buildMappingFields,
  buildOptionAttrArchive,
  checkStatusAttr,
  CUSTOM_TYPE,
  formatDatetimeDF,
  formatNumberDF,
  getAvailableAttrs,
  getStringFormatDateDF,
  regexCSType,
  serializeCSSelectedString,
  serializeLabelToCode,
  serilizeBOAttr,
  serilizeDynamicContentAttr,
  serilizeEventAttr,
  serilizePromotionPool,
} from './utils';

// Style
import { EditorScript } from '../EditorScript';
import { InputSuggestion, ModalCustom } from './styled';

export interface LabeledValue {
  label: string;
  value: string;
  dataType?: TDataType;
  disabled?: boolean;
  status?: any;
}
export interface LabeledTreeValue extends LabeledValue {
  [key: string]: any;
  children: LabeledTreeValue[];
  disableCheckbox?: boolean;
  selectable?: boolean;
  checkable?: boolean;
  type: number | null;
  status?: any;
}

interface AddDynamicContentProps {
  defaultDynamicIndex?: number;
  journeySettings: Record<string, any>;
  contentSources: Record<string, any>;
  additionalAttrProperties?: string[];
  defaultData?: Record<string, any>;
  visible: boolean;
  showDisplayFormat?: boolean;
  showIndex?: boolean;
  onCancel?: () => void;
  onOk?: (value: Record<string, any>) => void;
  modalTitle?: string;
  isShowCustomFunction?: boolean;
  APIConfig: APIConfig;
}

const getMembers = (member: any) => {
  if (!member.children || !member.children.length) {
    return member;
  }

  return [member, member.children];
};

const PATH = 'src/components/molecules/AddDynamicContent/AddDynamicContent.tsx';

const initNumberDFSettings: TNumberFormatSettings = {
  grouping: ',',
  decimal: '.',
  decimalPlaces: 2,
  isCompact: false,
  currencyCode: 'USD',
  prefixType: 'code',
};

const initDatetimeDFSettings: TDatetimeFormatSetting = {
  hasDateFormat: true,
  hasTimeFormat: true,
  dateParseOption: 'medium',
  timeParseOption: 'medium',
  dateParseFormat: 'MM/DD/YYYY',
  timeParseFormat: '12hour',
};

const defaultDynamicTypes = [...Object.values(DYNAMIC_CONTENT_TYPE)]
  .filter((i: any) => i.value !== 'bo-settings')
  .sort((a: any, b: any) => a.index - b.index);

const optionDataType = [...Object.values(DATA_TYPE)];

export const AddDynamicContent: React.FC<AddDynamicContentProps> = props => {
  //* Props
  const {
    defaultData = {},
    defaultDynamicIndex = 1,
    visible,
    showDisplayFormat,
    showIndex,
    journeySettings,
    contentSources,
    APIConfig,
    isShowCustomFunction = false,
    modalTitle,
  } = props;
  const { accountId, userId, token, domain, slug } = APIConfig;
  const infos = { accountId, userId, token };
  const { onCancel, onOk, additionalAttrProperties = [] } = props;
  const url = useMemo(() => `${domain}${slug}`, [domain, slug]);

  // Hook Queries
  const { mutate: addSavedCSFunction } = useAddSavedCSFunction();
  const { data: customFunction } = useGetCustomFunction({ url }, infos);
  const { mutate: updateCSFunction } = useUpdateCSFunction();

  //* Hooks
  const { t, language } = i18nInstance;

  //* Selectors
  const { triggerEvent } = journeySettings || {};

  const [itemTypeId, setItemTypeId] = useState<number | null>(null);

  // Form states
  const [form] = Form.useForm();

  const selectedDynamicContentType = Form.useWatch<string | undefined>(
    DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE,
    form,
  );

  const selectedAttr = Form.useWatch<LabeledTreeValue | undefined>(
    DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE,
    form,
  );

  const selectedDFType = Form.useWatch<TDisplayFormat | undefined>(
    DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE,
    form,
  );

  const selectedEventSource = Form.useWatch<string | undefined>(
    DYNAMIC_CONTENT_SETTING_KEY.EVENT,
    form,
  );

  const selectedSource = Form.useWatch<string | undefined>(
    DYNAMIC_CONTENT_SETTING_KEY.SOURCE,
    form,
  );

  const selectedBoIndex = Form.useWatch<string | undefined>(
    DYNAMIC_CONTENT_SETTING_KEY.INDEX,
    form,
  );

  const [formSubmitDisabled, setFormSubmitDisabled] = useState<boolean>(true);
  const [saveAsTemplate, setsaveAsTemplate] = useState<boolean>(false);
  const [hasError, setHasError] = useState<Record<string, boolean>>({
    [DYNAMIC_CONTENT_SETTING_KEY.EVENT]: false,
    [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: false,
    [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: false,
    [DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL]: false,
    [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: false,
    [DYNAMIC_CONTENT_SETTING_KEY.FUNCTION]: false,
    [DYNAMIC_CONTENT_SETTING_KEY.PERNAME]: false,
  });
  // Queries
  const { data: listBO } = useGetListBO({ url }, infos);

  // defaultSettings
  const [defaultAttrDFSetings, setDefaultAttrDFSetings] = useState<{
    itemPropertyName: string;
    settings: any;
  } | null>(null);

  // Variables
  const [eventActionId, eventCategoryId] = selectedEventSource?.split(':').map(Number) || [];

  // Selection Options states
  const [listDynamicContentTypes, setListDynamicContentTypes] =
    useState<LabeledValue[]>(defaultDynamicTypes);
  const [listCustomerAttributes, setListCustomerAttributes] = useState<LabeledValue[]>([]);
  const [listVisitorAttributes, setListVisitorAttributes] = useState<LabeledValue[]>([]);
  const [listCustomTemplates, setlistCustomTemplates] = useState<any[]>([]);
  const [listCustomTemplatesSearch, setlistCustomTemplatesSearch] = useState<any[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');
  // Expanded keys in tree select
  const [expandedEventAttributes, setExpandedEventAttributes] = useState<string[]>([]);
  const [isActionArchive, setActionArchive] = useState<boolean>(false);
  const [errorMessageAttr, setErrorMessageAttr] = useState<string>();
  // Number Display format
  const [showDetailEditDF, setShowDetailEditDF] = useState<boolean>(false);
  const [formatedAttr, setFormatedAttr] = useState<string>('');
  const [attrDFSettings, setAttrDFSettings] = useState<
    TNumberFormatSettings | TDatetimeFormatSetting | undefined
  >(undefined);
  const [templateName, setTemplateName] = useState<string>('');
  const [attrDFOptions, setAttrDFOptions] = useState<LabeledValue[]>([]);
  // Template Custom
  const [templateId, settemplateId] = useState<number[]>();

  const queryClient = useQueryClient();

  const {
    data: listBoAttributes = [],
    isFetching: isFetchingListBoAttr,
    isError: isErrorBoAttr,
  } = useGetListAttributeBO<LabeledValue[]>(
    {
      url,
      itemTypeIds: itemTypeId ? [itemTypeId] : [],
      options: {
        onSuccess: (data: any) => {
          setHasError({
            [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: false,
            [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: false,
          });

          if (
            data.length &&
            selectedDynamicContentType &&
            regexCSType.test(selectedDynamicContentType)
          ) {
            if (
              form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE) &&
              form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.INDEX)
            )
              return;

            form.setFieldsValue({
              [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: data[0],
              [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: defaultDynamicIndex,
            });
          }
        },
        select: (data: any) => serilizeBOAttr(data, additionalAttrProperties),
        onError: (error: any) => {
          setHasError({
            [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: true,
            [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: true,
          });

          handleError(error, {
            path: PATH,
            name: 'useGetListAttributeBO',
            args: error?.message,
          });
        },
        enabled: !!itemTypeId,
      },
    },
    infos,
  );

  const {
    data: dynamicContentAttr,
    isFetching: isFetchingDynamicContentAttr,
    isError: isErrorDynamicContentAttr,
  } = useGetDynamicContentAttr({ url }, infos, {
    onSuccess: () => {
      setHasError({
        [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: false,
      });
    },
    onError: (error: any) => {
      setHasError({
        [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: true,
      });

      handleError(error, {
        path: PATH,
        name: 'useGetDynamicContentAttr',
        args: error?.message,
      });
    },
  });

  const { data: listSources, isFetching: isFetchingSourceBO } = useGetListSourceByEvent({
    apiConfig: {
      url,
    },
    infos,
    eventActionId,
    eventCategoryId,
    options: {
      onSuccess: () => {
        setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: false });
      },
      select: (data: any) => {
        const { rows } = data || {};

        return rows.map((row: any) => ({
          label: row.label,
          value: row.value,
        }));
      },
      onError: (error: any) => {
        setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: true });
        handleError(error, {
          path: PATH,
          name: 'useGetListSourceBO',
          args: error?.message,
        });
      },
    },
  });

  const { data: listEvents, isFetching: isFetchingEventBySource } = useGetListAllEvents({
    apiConfig: {
      url,
    },
    infos,
    options: {
      onSuccess: (data: any) => {
        setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.EVENT]: false });

        if (
          !isEmpty(data) &&
          selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value
        ) {
          form.setFieldsValue({
            [DYNAMIC_CONTENT_SETTING_KEY.EVENT]: data[0].value,
          });
        }
      },
      onError: (error: any) => {
        setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.EVENT]: true });

        handleError(error, {
          path: PATH,
          name: 'useGetListEventBySource',
          args: error?.message,
        });
      },
      select: (data: any) => {
        const { rows = [] } = data || {};
        return rows.map((event: any) => ({
          label: event.label,
          value: `${event.eventActionId}:${event.eventCategoryId}`,
        }));
      },
    },
  });

  const {
    data: listEventAttributes,
    isFetching: isFetchingEventAttr,
    isError: isErrorEventAttr,
  } = useGetListEventAttr<LabeledTreeValue[]>(
    { url },
    infos,
    selectedEventSource || '',
    Array.isArray(selectedSource) ? selectedSource.join(',') : selectedSource || '',
    {
      onSuccess: () => setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: false }),
      onError: (error: any) => {
        setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: true });

        handleError(error, {
          path: PATH,
          name: 'useGetListEventAttr',
          args: error?.message,
        });
      },
      select: (data: any) => serilizeEventAttr(data, language, additionalAttrProperties),
    },
  );

  const { data: listPromotionPools, isFetching: isFetchingPromotionPool } = useGetListPromotionPool<
    LabeledValue[]
  >({ url }, infos, {
    onSuccess: () => {
      setHasError({
        [DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL]: false,
      });
    },
    select: (data: any) => serilizePromotionPool(data, additionalAttrProperties),
    onError: (error: any) => {
      setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: true });

      handleError(error, {
        path: PATH,
        name: 'useGetListPromotionPool',
        args: error?.message,
      });
    },
  });

  const {
    data: listPromotionCodeAttributes,
    isFetching: isFetchingPromotionCodeAttr,
    isError: isErrorListPromotionCodeAttr,
  } = useGetListPromotionCodeAttr<LabeledValue[]>({ url }, infos, {
    onSuccess: () => {
      setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: false });
    },
    select: (data: any) =>
      data
        .filter((attr: any) => +attr?.status === 1)
        .map((attr: any) => ({
          label:
            attr.propertyDisplayMultilang[language] ||
            attr.propertyDisplayMultilang[
              get(attr, "propertyDisplayMultilang['DEFAULT_LANG']", 'EN')
            ],
          value: attr.itemPropertyName,
          dataType: attr.dataType,
          disabled: parseInt(attr.status) === 4,
          status: attr.status,
        })),
    onError: (error: any) => {
      setHasError({
        [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: true,
      });

      handleError(error, {
        path: PATH,
        name: 'useGetPromotionCodeAttr',
        args: error?.message,
      });
    },
  });

  const { data: eventTrackingAttributes } = useGetEventTrackingAttributes({
    apiConfig: { url },
    infos,
    eventActionId,
    eventCategoryId,
  });

  // Memo
  const isShowEventIndexField = useDeepCompareMemo(() => {
    if (selectedAttr && selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      if (selectedAttr.itemTypeId) {
        return !!get(selectedAttr, 'eventPropertySyntax', '').includes('items');
      }

      return selectedAttr.type === ATTRIBUTE_TYPE.ITEM;
    }

    return false;
  }, [selectedAttr, selectedDynamicContentType, eventTrackingAttributes?.mainObjects]);

  const itemTypeName = useMemo(() => {
    let draftItemTypeName = '';

    if (itemTypeId) {
      draftItemTypeName = listBO?.find((bo: any) => bo.id === itemTypeId)?.name || '';
    }

    return draftItemTypeName;
  }, [itemTypeId, listBO]);

  // Add BO settings (if exists) to list Dynamic content Type, or removed from the previous states
  useEffect(() => {
    const temp = [...defaultDynamicTypes];

    contentSources.groups.forEach((g: any) => {
      if (!g.itemTypeId) return;

      temp.push({
        index: temp.length,
        label: `${g.groupName} (${g.itemTypeDisplay})`,
        value: `content-source::${g.groupId}::${g.itemTypeId}`,
      });
    });
    if (isShowCustomFunction) {
      temp.push({
        ...CUSTOM_TYPE,
        index: temp.length,
      });
    }

    setListDynamicContentTypes(temp);
  }, [contentSources.groups, isShowCustomFunction]);

  useEffect(() => {
    if (Array.isArray(customFunction) && customFunction.length) {
      const temp: any[] = [];
      customFunction.forEach(item => {
        temp.push({
          ...item,
          label: item.template_name,
          value: item.template_name,
        });
      });
      setlistCustomTemplates(temp);
    }
  }, [customFunction, visible]);

  useDeepCompareEffect(() => {
    if (dynamicContentAttr) {
      const { customer, visitor } = dynamicContentAttr;
      const dynamicContentType = form.getFieldValue(
        DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE,
      );
      const parseCustomers: LabeledValue[] = serilizeDynamicContentAttr(
        customer,
        language,
        additionalAttrProperties,
      );
      const parseVisitors: LabeledValue[] = serilizeDynamicContentAttr(
        visitor,
        language,
        additionalAttrProperties,
      );

      setListCustomerAttributes(parseCustomers);
      setListVisitorAttributes(parseVisitors);

      if (form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE)) return;

      if (dynamicContentType === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value) {
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: getAvailableAttrs(parseVisitors)[0],
        });
      }

      if (dynamicContentType === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value) {
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: getAvailableAttrs(parseCustomers)[0],
        });
      }
    }
  }, [dynamicContentAttr, form, language]);

  useDeepCompareEffect(() => {
    if (listSources && selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      if (!isEmpty(form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.SOURCE))) return;

      const defaultValue = listSources[0] ? [listSources[0].value] : [];

      form.setFieldsValue({
        [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: defaultValue,
      });
    }
  }, [selectedEventSource, listSources, form, selectedDynamicContentType]);

  useDeepCompareEffect(() => {
    if (
      listEventAttributes &&
      selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value
    ) {
      const initEventAttr = listEventAttributes[0]?.children.length
        ? getAvailableAttrs(listEventAttributes[0]?.children)[0]
        : listEventAttributes[0];

      if (form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE)) {
        const eventAttr = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE);

        setExpandedEventAttributes([eventAttr.value.split('.')[0]]);

        return;
      }

      form.setFieldsValue({
        [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: initEventAttr,
      });

      setExpandedEventAttributes([initEventAttr.value.split('.')[0]]);
    }
  }, [selectedSource, selectedEventSource, listEventAttributes, form, selectedDynamicContentType]);

  useDeepCompareEffect(() => {
    if (
      listPromotionCodeAttributes &&
      selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value
    ) {
      if (form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE)) return;

      form.setFieldsValue({
        [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: getAvailableAttrs(listPromotionCodeAttributes)[0],
      });
    }
  }, [selectedDynamicContentType, listPromotionCodeAttributes, form]);

  // Update form field data with default data/ or reset it to initial states
  const resetStateAndFormValueDF = useCallback(() => {
    setShowDetailEditDF(false);
    setAttrDFSettings(undefined);
    setAttrDFOptions([]);
    setFormatedAttr('');
    setsaveAsTemplate(false);
    setTemplateName('');
    setSearchValue('');
    form.setFieldsValue({
      [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: undefined,
      [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]: undefined,
      [DYNAMIC_CONTENT_SETTING_KEY.FUNCTION]: undefined,
      [DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE]: optionDataType[0].value,
    });
  }, [form]);

  useEffect(() => {
    if (!visible) {
      setTimeout(() => {
        resetStateAndFormValueDF();
      }, 200);
    }
  }, [visible, resetStateAndFormValueDF]);

  useEffect(() => {
    let listTmp: any[] = [];
    switch (true) {
      case selectedDynamicContentType && regexCSType.test(selectedDynamicContentType):
        listTmp = [...listBoAttributes];
        break;
      case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
        listTmp = listPromotionCodeAttributes ? [...listPromotionCodeAttributes] : [];
        break;
      case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
        listTmp = [...listCustomerAttributes];
        break;
      case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
        listTmp = [...listVisitorAttributes];
        break;
      default:
        break;
    }
    const { errorMessage, isDisable } = checkStatusAttr({
      listBO: listBO as any,
      itemTypeId: itemTypeId || 0,
      listAttribute: listTmp,
      field: selectedAttr?.value,
    });

    setActionArchive(isDisable);
    setErrorMessageAttr(errorMessage);
  }, [selectedAttr]);

  useEffect(() => {
    if (!visible) {
      return;
    }

    if (Object.keys({ ...defaultData })?.length) {
      let draftDefaultData = { ...defaultData };
      if (draftDefaultData.type !== 'custom') {
        if (triggerEvent?.eventActionId) {
          draftDefaultData = omit(draftDefaultData, ['event', 'source']);
        }

        form.setFieldsValue(draftDefaultData);

        const defaultDMType = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE);
        const defaultAttr = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE);

        if (defaultDMType && regexCSType.test(defaultDMType)) {
          const { itemTypeId } = serializeCSSelectedString(defaultDMType);

          if (!isNaN(itemTypeId)) setItemTypeId(itemTypeId);
        }
        if (defaultAttr) {
          const defaultAttr = draftDefaultData[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE];

          // eslint-disable-next-line no-prototype-builtins
          if (!defaultAttr.hasOwnProperty('dataType')) return;

          let key = '';

          if (defaultAttr.dataType === 'number') {
            key = DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_NUMBERIC_ATTR_FORMAT_SETTINGS;
          }
          if (defaultAttr.dataType === 'datetime') {
            key = DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_DATETIME_ATTR_FORMAT_SETTINGS;
          }

          if (key && defaultAttr[key]) {
            const { type, ...settingValues } = defaultAttr[key];

            form.setFieldsValue({
              [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]: type,
            });

            setDefaultAttrDFSetings({
              itemPropertyName: defaultAttr.value,
              settings: settingValues,
            });
            setAttrDFSettings(settingValues);
            setAttrDFOptions(
              DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(
                (dfType: any) => dfType.dataType === defaultAttr.dataType,
              ),
            );
          }
        }
      } else {
        const { customFunction } = draftDefaultData;
        const { outputFormat } = customFunction;
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE]: draftDefaultData.type,
          [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]: outputFormat.type,
          [DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE]: customFunction.outputDataType,
          [DYNAMIC_CONTENT_SETTING_KEY.PERNAME]: customFunction.templateName,
          [DYNAMIC_CONTENT_SETTING_KEY.FUNCTION]: customFunction.customFunction,
        });
        setTemplateName(customFunction.templateName);
        setAttrDFOptions(
          DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(
            (dfType: any) => dfType.dataType === customFunction.outputDataType,
          ),
        );
        setShowDetailEditDF(true);
        setAttrDFSettings(
          customFunction.outputDataType === 'string' ? undefined : outputFormat.config,
        );
      }
      // Check if trigger event of journey has event then don's set current event and source
    } else {
      // Hard reset to Visitor attr
      if (listVisitorAttributes) {
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE]:
            DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value,
          [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: listVisitorAttributes[0],
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultData, visible, form]);

  useDeepCompareEffect(() => {
    if (selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      const { eventActionId, eventCategoryId, insightPropertyIds } = triggerEvent || {};

      if (!!eventActionId && !!eventCategoryId) {
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.EVENT]: `${eventActionId}:${eventCategoryId}`,
        });
      }

      if (Array.isArray(insightPropertyIds) && insightPropertyIds.length) {
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: insightPropertyIds,
        });
      }
    }
  }, [triggerEvent, selectedDynamicContentType]);

  // Disable Apply button if there's any error
  useLayoutEffect(() => {
    setFormSubmitDisabled(Object.values(hasError).some(error => error));
  }, [hasError]);

  useLayoutEffect(() => {
    if (
      selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value ||
      selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value
    ) {
      setFormSubmitDisabled(isFetchingDynamicContentAttr);
    }

    if (selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
      setFormSubmitDisabled(isFetchingSourceBO || isFetchingEventBySource || isFetchingEventAttr);
    }

    if (selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value) {
      setFormSubmitDisabled(isFetchingPromotionPool || isFetchingPromotionCodeAttr);
    }

    if (selectedDynamicContentType && regexCSType.test(selectedDynamicContentType)) {
      setFormSubmitDisabled(isFetchingListBoAttr);
    }
  }, [
    isFetchingDynamicContentAttr,
    selectedDynamicContentType,
    isFetchingSourceBO,
    isFetchingEventBySource,
    isFetchingEventAttr,
    isFetchingPromotionPool,
    isFetchingPromotionCodeAttr,
    isFetchingListBoAttr,
  ]);

  useLayoutEffect(() => {
    let formatedDFValue: string = '';
    let settings = attrDFSettings;
    const selectedAttr = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE);
    const dataType = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE);
    const dynamicType = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE);
    if (
      (Object.keys(defaultData).length > 0 && defaultData.type !== 'custom') ||
      dynamicType !== 'custom'
    ) {
      if (
        selectedAttr &&
        selectedAttr.dataType &&
        ['datetime', 'number'].includes(selectedAttr.dataType)
      ) {
        /* when change select  if attr selected is previous selected default attr, set settings as default settings */
        if (
          !settings &&
          defaultAttrDFSetings?.itemPropertyName === (selectedAttr.value as string)
        ) {
          // Visitor and Customer don't have additional field
          let equalDefaultAttr: boolean = true;

          // Event need check additional field source, event by soucre
          if (selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
            const source = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.SOURCE);
            const event = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.EVENT);

            if (
              source !== defaultData[DYNAMIC_CONTENT_SETTING_KEY.SOURCE] ||
              event !== defaultData[DYNAMIC_CONTENT_SETTING_KEY.EVENT]
            )
              equalDefaultAttr = false;
          }

          // Promotion need check additional field promotion pool
          if (selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value) {
            const pool = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL);

            if (pool !== defaultData[DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL])
              equalDefaultAttr = false;
          }

          // BO settings need check additional field index
          if (selectedDynamicContentType && regexCSType.test(selectedDynamicContentType)) {
            if (selectedBoIndex !== defaultData[DYNAMIC_CONTENT_SETTING_KEY.INDEX])
              equalDefaultAttr = false;
          }

          if (equalDefaultAttr) {
            settings = defaultAttrDFSetings.settings;
          }
        }
        // *********** //

        // Numberic Attribute DF
        if (selectedAttr?.dataType === 'number') {
          settings = settings ?? initNumberDFSettings;
          formatedDFValue = formatNumberDF(
            1234.56789,
            selectedDFType as 'number' | 'currency' | 'percentage',
            settings as TNumberFormatSettings,
          );
        }

        // Datatime Attribute DF
        if (selectedAttr?.dataType === 'datetime') {
          settings = settings ?? { ...initDatetimeDFSettings, language };
          formatedDFValue = formatDatetimeDF(dateExample, settings as TDatetimeFormatSetting);
        }

        if (!attrDFOptions.length) {
          setAttrDFOptions(
            DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(
              (dfType: any) => dfType.dataType === selectedAttr.dataType,
            ),
          );
        }
        setAttrDFSettings(settings);
        setFormatedAttr(formatedDFValue);
      }
    }
    if (dataType && ['datetime', 'number'].includes(dataType) && dynamicType === 'custom') {
      // Numberic Attribute DF
      if (dataType === 'number') {
        settings = settings ?? initNumberDFSettings;
        formatedDFValue = formatNumberDF(
          1234.56789,
          selectedDFType as 'number' | 'currency' | 'percentage',
          settings as TNumberFormatSettings,
        );
      }
      // Datatime Attribute DF
      if (dataType === 'datetime') {
        settings = settings ?? { ...initDatetimeDFSettings, language };
        formatedDFValue = formatDatetimeDF(dateExample, settings as TDatetimeFormatSetting);
      }
      if (!attrDFOptions.length) {
        setAttrDFOptions(
          DYNAMIC_CONTENT_ATTR_DF_TYPE.filter((dfType: any) => dfType.dataType === dataType),
        );
      }

      setAttrDFSettings(settings);
      setFormatedAttr(formatedDFValue);
    }
  }, [
    attrDFSettings,
    selectedDFType,
    attrDFOptions.length,
    language,
    defaultAttrDFSetings,
    form,
    selectedAttr,
    defaultData,
    selectedDynamicContentType,
    selectedBoIndex,
  ]);

  //* Handlers
  const handleOnCancelModal = () => {
    if (onCancel) {
      onCancel();
    }
  };
  const onChangeDataInput = (value: any) => {
    setTemplateName(value);
    let tempState = [...listCustomTemplates];
    if (value.length > 0) {
      tempState = tempState.filter(item =>
        item.label.toLowerCase().includes(value.trim().toLowerCase()),
      );
    }
    setlistCustomTemplatesSearch(tempState);
    setSearchValue(value);
  };

  const handleSelect = (data: any) => {
    const dataTmp = listCustomTemplates.filter(each => each.value === data);
    settemplateId(dataTmp[0].template_id);
    let dataFormat = dataTmp[0].output_format.type.toLowerCase();
    switch (dataTmp[0].output_format.type) {
      case 'DATE_AND_TIME':
        dataFormat = 'date_and_time';
        break;
      case 'RAW_STRING':
        dataFormat = 'raw_string';
        break;
      default:
        break;
    }

    form.setFieldsValue({
      [DYNAMIC_CONTENT_SETTING_KEY.FUNCTION]: dataTmp[0].custom_function,
      [DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE]: dataTmp[0].output_data_type,
      [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]:
        dataFormat === 'date_and_time' ? 'datetime' : dataFormat,
    });
    setTemplateName(dataTmp[0].template_name);
    setAttrDFOptions(
      DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(
        (dfType: any) => dfType.dataType === dataTmp[0].output_data_type,
      ),
    );
    const format = dataTmp[0].output_format.config;
    switch (dataFormat) {
      case 'currency': {
        setAttrDFSettings({
          ...initNumberDFSettings,
          grouping: format.group || ',',
          decimal: format.decimal,
          decimalPlaces: format.decimalPlace,
          isCompact: format.isCompactNumber || false,
          currencyCode: format.currency || 'USD',
          prefixType: format.prefixType || 'code',
        });
        break;
      }
      case 'number':
      case 'percentage': {
        setAttrDFSettings({
          ...initNumberDFSettings,
          grouping: format.group || ',',
          decimal: format.decimal,
          decimalPlaces: format.decimalPlace,
          isCompact: format.isCompactNumber || false,
        });
        break;
      }
      case 'date_and_time': {
        setAttrDFSettings({
          ...initDatetimeDFSettings,
          language,
          hasDateFormat: format.date.check || true,
          hasTimeFormat: format.time.check || true,
          dateParseOption: format.date.value || 'medium',
          timeParseOption: format.time.value || 'medium',
          dateParseFormat: format.format || 'MM/DD/YYYY',
          timeParseFormat: `${format.time.timeFormat || 12}hour`,
        });

        break;
      }
      case 'raw_string': {
        setAttrDFSettings(undefined);

        break;
      }
      default:
        break;
    }
  };

  const handleOnOkModal = () => {
    if (
      isActionArchive &&
      selectedDynamicContentType !== DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value
    ) {
      return;
    }
    form.validateFields().then((values: Record<string, any>) => {
      values = omit(values, 'dfType');

      if (onOk) {
        const type = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE);
        if (type === 'custom') {
          const outputFormat = {};
          const perName = templateName;
          const functionCustom = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.FUNCTION);
          const dataType = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE);
          const displayFormat = form.getFieldValue([
            DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE,
          ]);
          if (templateId && !saveAsTemplate) {
            try {
              updateCSFunction({
                url,
                infos,
                templateId,
                name: perName,
                dataType,
                functionCustom,
                dataFormat: attrDFSettings,
                displayFormat,
                outputFormat,
              });
            } catch (error) {
              handleError(error, {
                path: PATH,
                name: 'onClickSaveDynamicContent',
                args: {},
              });
            }
          } else if (saveAsTemplate) {
            try {
              addSavedCSFunction({
                url,
                infos,
                name: perName,
                dataType,
                functionCustom,
                dataFormat: attrDFSettings,
                displayFormat,
                outputFormat,
              });
            } catch (error) {
              handleError(error, {
                path: PATH,
                name: 'onClickSaveDynamicContent',
                args: {},
              });
            }
          }
          if (!showIndex) {
            values = omit(values, 'index');
          }
          const config: any = {
            ...attrDFSettings,
          };
          if (dataType === 'datetime') {
            config.dateFormatString = getStringFormatDateDF(
              attrDFSettings as TDatetimeFormatSetting,
            );
          }
          onOk({
            ...values,
            customFunction: {
              outputFormat: {
                type: displayFormat,
                config,
              },
              templateCode: serializeLabelToCode(perName),
              templateName: perName,
              templateType: 'custom',
              customFunction: functionCustom,
              outputDataType: dataType,
            },
            attribute: {
              label: perName,
              value: serializeLabelToCode(perName),
            },
            mappingFields: `#{custom.${serializeLabelToCode(perName)}}`,
          });
        } else {
          let key = '';
          const otherField: any = {};

          const selectedAttr = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE);

          if (selectedAttr?.dataType === 'number') {
            key = DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_NUMBERIC_ATTR_FORMAT_SETTINGS;
          }
          if (selectedAttr?.dataType === 'datetime') {
            key = DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_DATETIME_ATTR_FORMAT_SETTINGS;
            otherField.dateFormatString = getStringFormatDateDF(
              attrDFSettings as TDatetimeFormatSetting,
            );
          }

          if (key && showDisplayFormat) {
            values[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE][key] = {
              ...attrDFSettings,
              ...otherField,
              type: selectedDFType,
            };
          }

          // Handle Merge tag for event attributes
          if (values.type === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value) {
            if (values.index != null && !get(values, 'attribute.itemTypeName', null)) {
              const itemTypeName =
                listBO?.find(({ id }) =>
                  get(eventTrackingAttributes, 'mainObjects', []).some(
                    ({ itemTypeId }) => itemTypeId === id,
                  ),
                )?.name || null;

              if (itemTypeName) {
                set(values, 'attribute.itemTypeName', itemTypeName);
              }
            }
          }

          // Set itemTypeName if Selecting content source
          if (regexCSType.test(values.type) && !!itemTypeName) {
            set(values, 'attribute.itemTypeName', itemTypeName);
          }

          if (!showIndex) {
            values = omit(values, 'index');
          }

          const mappingFields = buildMappingFields(values);

          onOk({
            ...values,
            ...(mappingFields && { mappingFields }),
          });
        }
      }
    });
  };

  const handleOnClickParentNode = (item: LabeledTreeValue) => {
    if (item.children.length) {
      if (expandedEventAttributes.includes(item.value)) {
        // close expand
        const newExpandedKeys = expandedEventAttributes.filter(key => key !== item.value);
        handleTreeExpand(newExpandedKeys);
      } else {
        // open expand
        handleTreeExpand((prev: any) => [...prev, item.value]);
      }
    }
  };

  const handleTreeExpand = (expandedKeys: any) => {
    setExpandedEventAttributes(expandedKeys);
  };
  const handleSaveAsTemplate = () => {
    setsaveAsTemplate(!saveAsTemplate);
  };
  const handleFormValuesChanges = (changedValues: any, _allValues: any) => {
    const [fieldName, fieldValue] = Object.entries(changedValues)[0] as [string, any];

    switch (fieldName) {
      case DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE:
        resetStateAndFormValueDF();

        switch (true) {
          case fieldValue === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
            form.setFieldsValue({
              [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: getAvailableAttrs(listCustomerAttributes)[0],
            });

            setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: isErrorDynamicContentAttr });
            break;
          case fieldValue === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
            form.setFieldsValue({
              [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: getAvailableAttrs(listVisitorAttributes)[0],
            });

            setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: isErrorDynamicContentAttr });
            break;
          case fieldValue === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
            if (!listEvents?.length) break;

            form.setFieldsValue({
              [DYNAMIC_CONTENT_SETTING_KEY.EVENT]: listEvents[0].value,
              [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: undefined,
            });

            setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: isErrorEventAttr });

            break;
          case fieldValue === CUSTOM_TYPE.value:
            if (!listEvents?.length) break;
            let formatedDFValue: string = '';
            let settings = attrDFSettings;
            settings = settings ?? initNumberDFSettings;
            formatedDFValue = formatNumberDF(
              1234.56789,
              selectedDFType as 'number' | 'currency' | 'percentage',
              settings as TNumberFormatSettings,
            );
            setFormatedAttr(formatedDFValue);
            setAttrDFSettings(settings);
            form.setFieldsValue({
              [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]: optionDataType[0].value,
              [DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE]: optionDataType[0].value,
            });
            setAttrDFOptions(
              DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(
                dfType => dfType.dataType === optionDataType[0].value,
              ),
            );
            setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: isErrorEventAttr });
            setShowDetailEditDF(true);
            break;
          case fieldValue === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
            if (!listPromotionCodeAttributes || !listPromotionPools) break;

            form.setFieldsValue({
              [DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL]: listPromotionPools[0]?.value,
              [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: getAvailableAttrs(
                listPromotionCodeAttributes,
              )[0],
            });

            setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: isErrorListPromotionCodeAttr });
            break;
          case regexCSType.test(fieldValue): {
            const { itemTypeId: selectedItemTypeId } = serializeCSSelectedString(
              String(fieldValue),
            );

            if (isNaN(selectedItemTypeId)) break;

            setItemTypeId(Number(selectedItemTypeId));

            const key = [QUERY_KEYS.GET_EVENT_ATTRIBUTE_BO, [selectedItemTypeId]];
            const listAttBOByItemTypeId = serilizeBOAttr(queryClient.getQueryData(key));

            if (listAttBOByItemTypeId.length) {
              form.setFieldsValue({
                [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: listAttBOByItemTypeId[0],
                [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: defaultDynamicIndex,
              });
            }

            setHasError({ [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: isErrorBoAttr });
            break;
          }
          default: {
            break;
          }
        }

        break;

      case DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE:
        let listAttributes: any[] = [];

        switch (true) {
          case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value:
            if (!listEventAttributes) break;

            let newSelectEventAttr = listEventAttributes.find((row: any) => {
              if (row?.children.length) {
                return row.children.find((item: any) => item.value === fieldValue.value);
              }

              return row.value === fieldValue.value;
            });

            if (newSelectEventAttr?.children.length) {
              newSelectEventAttr = newSelectEventAttr.children.find(
                (item: any) => item.value === fieldValue.value,
              );
            }

            listAttributes = flatMapDeep(listEventAttributes, getMembers);

            form.setFieldsValue({ [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: defaultDynamicIndex });

            break;
          case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value:
            listAttributes = listCustomerAttributes;
            break;
          case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value:
            listAttributes = listPromotionCodeAttributes ?? [];
            break;
          case selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value:
            listAttributes = listVisitorAttributes;
            break;
          case selectedDynamicContentType && regexCSType.test(selectedDynamicContentType):
            form.setFieldsValue({ [DYNAMIC_CONTENT_SETTING_KEY.INDEX]: defaultDynamicIndex });

            if (!listBoAttributes) break;

            listAttributes = listBoAttributes;
            break;
          default: {
            break;
          }
        }

        // add dataType to
        if (listAttributes.length) {
          const attr = listAttributes.find(attr => {
            if (attr.value === fieldValue.value) {
              return attr;
            }

            return null;
          });

          form.setFieldsValue({
            [DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]: attr,
          });

          // set new value for options display format
          if (attr) {
            const newAttrDFOptions = DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(
              (dfType: any) => dfType.dataType === attr.dataType,
            );

            if (newAttrDFOptions.length) {
              form.setFieldsValue({
                [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]: newAttrDFOptions[0].value,
              });
              setAttrDFOptions(newAttrDFOptions);
            }

            // reset detail display format settings
            setAttrDFSettings(undefined);
            setShowDetailEditDF(false);
          }
        }
        break;

      case DYNAMIC_CONTENT_SETTING_KEY.INDEX:
        break;
      case DYNAMIC_CONTENT_SETTING_KEY.FUNCTION:
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.FUNCTION]: fieldValue,
        });
        break;
      case DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE:
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE]: fieldValue,
          [DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]:
            fieldValue === 'string' ? 'raw_string' : fieldValue,
        });
        let settings = attrDFSettings;

        // Datatime Attribute DF
        if (fieldValue === 'datetime') {
          settings = { ...initDatetimeDFSettings, language };
        } else if (fieldValue === 'string') {
          settings = undefined;
        }
        setAttrDFOptions(
          DYNAMIC_CONTENT_ATTR_DF_TYPE.filter((dfType: any) => dfType.dataType === fieldValue),
        );
        setAttrDFSettings(settings);
        break;
      case DYNAMIC_CONTENT_SETTING_KEY.EVENT:
        form.setFieldsValue({
          [DYNAMIC_CONTENT_SETTING_KEY.SOURCE]: undefined,
        });

        resetStateAndFormValueDF();
        break;

      case DYNAMIC_CONTENT_SETTING_KEY.SOURCE:
        resetStateAndFormValueDF();

        break;

      case DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE:
        switch (fieldValue) {
          case 'currency': {
            setAttrDFSettings({
              ...initNumberDFSettings,
              decimalPlaces: 1,
            });
            break;
          }
          case 'number':
          case 'percentage': {
            setAttrDFSettings(initNumberDFSettings);
            break;
          }
          case 'datetime': {
            setAttrDFSettings({
              ...initDatetimeDFSettings,
              language,
            });
            break;
          }
          case 'raw_string': {
            setAttrDFSettings(undefined);
            break;
          }
          default: {
            break;
          }
        }
        break;
      default: {
        break;
      }
    }
  };

  //* Render functions
  const renderBoAttrFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]) {
      return (
        <Text type="error">{t(translations.dynamicContent.modal.error.boAttr) as string}</Text>
      );
    }

    return (
      <Form.Item
        style={{ alignItems: 'center' }}
        label={t(translations.dynamicContent.modal.label.attribute) as string}
        name={DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE}
        required
      >
        <Select
          loading={isFetchingListBoAttr}
          style={{ height: '28px' }}
          labelInValue
          options={buildOptionAttrArchive(listBoAttributes || [], selectedAttr)}
          // errorArchive={errorMessageAttr}
          disabled={isActionArchive}
          showSearch
        />
      </Form.Item>
    );
  };

  const renderIndexFields = ({ disabled = false, loading = false }) => {
    if (!showIndex) return null;

    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]) {
      return null;
    }

    return (
      <Form.Item
        style={{ alignItems: 'center' }}
        label={t(translations.dynamicContent.modal.label.index) as string}
        name={DYNAMIC_CONTENT_SETTING_KEY.INDEX}
        required
      >
        <Select
          disabled={disabled}
          loading={loading}
          style={{ height: '28px' }}
          options={Array.from({ length: 90 }, (_, i) => ({ value: i + 1, label: i + 1 }))}
          showSearch
        />
      </Form.Item>
    );
  };
  const renderCustomFunctionFields = () => {
    const dataType = form.getFieldValue([DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE]);

    return (
      <>
        <Form.Item
          label={t(translations.dynamicContent.modal.label.personalizationName) as string}
          name={DYNAMIC_CONTENT_SETTING_KEY.PERNAME}
          style={{ alignItems: 'center' }}
          required
        >
          <InputSuggestion
            onSelect={handleSelect}
            placeholder="Custom function name..."
            options={searchValue.length > 0 ? listCustomTemplatesSearch : listCustomTemplates}
            onChange={onChangeDataInput}
            value={templateName}
          />
          {hasError[DYNAMIC_CONTENT_SETTING_KEY.PERNAME] && (
            <Text type="error">{t(translations.dynamicContent.modal.error.perName) as string}</Text>
          )}
        </Form.Item>
        <Form.Item
          style={{ alignItems: 'center' }}
          label={t(translations.dynamicContent.modal.label.function) as string}
          name={DYNAMIC_CONTENT_SETTING_KEY.FUNCTION}
          required
        >
          <EditorScript mode="javascript" hideExpand />
        </Form.Item>
        <Form.Item
          style={{ alignItems: 'center' }}
          label={t(translations.dynamicContent.modal.label.ouputDataType) as string}
          name={DYNAMIC_CONTENT_SETTING_KEY.DATA_TYPE}
          required
        >
          <Select style={{ height: 28 }} options={optionDataType} />
        </Form.Item>
        <Form.Item
          style={{ alignItems: 'start' }}
          required
          label={t(translations.dynamicContent.modal.label.ouputFormat) as string}
          name={DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE}
          extra={
            <div
              style={{
                display: 'flex',
                flexWrap: 'wrap',
                alignItems: 'center',
                gap: '8px',
                marginTop: '16px',
              }}
            >
              {dataType !== 'raw_string' ? (
                <Text
                  color="#555"
                  dangerouslySetInnerHTML={{
                    __html: `${t(
                      translations.dynamicContent.modal.label.displayFormat,
                    )}: <bdo dir='ltr'>${formatedAttr}</bdo>`,
                  }}
                />
              ) : null}

              {!showDetailEditDF && (
                <Button
                  disabled={
                    isActionArchive &&
                    selectedDynamicContentType !== DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value
                  }
                  type="text"
                  style={{ display: 'inline-block' }}
                  onClick={() => setShowDetailEditDF(prevShow => !prevShow)}
                >
                  {t(translations.edit.title) as string}
                </Button>
              )}
            </div>
          }
        >
          <Select
            disabled={
              isActionArchive &&
              selectedDynamicContentType !== DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value
            }
            style={{ height: 28 }}
            options={attrDFOptions}
          />
        </Form.Item>
      </>
    );
  };
  const renderCustomerAttrFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]) {
      return (
        <Text type="error">
          {t(translations.dynamicContent.modal.error.customerAttr) as string}
        </Text>
      );
    }

    return (
      <Form.Item
        style={{ alignItems: 'center' }}
        initialValue={listCustomerAttributes[0]}
        label={t(translations.dynamicContent.modal.label.attribute) as string}
        name={DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE}
        required
      >
        <Select
          loading={isFetchingDynamicContentAttr}
          style={{ height: 28 }}
          labelInValue
          // errorArchive={errorMessageAttr}
          disabled={isActionArchive}
          options={buildOptionAttrArchive(listCustomerAttributes, selectedAttr)}
          showSearch
        />
      </Form.Item>
    );
  };

  const renderEventAttrFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]) {
      return (
        <Text type="error">{t(translations.dynamicContent.modal.error.eventAttr) as string}</Text>
      );
    }

    return (
      <>
        <Form.Item
          style={{ alignItems: 'center' }}
          label={t(translations.dynamicContent.modal.label.selectEventAttr) as string}
          name={DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE}
          required
        >
          <TreeSelect
            loading={isFetchingEventAttr || !listEvents?.length}
            style={{ height: 28 }}
            labelInValue
            showSearch
          >
            {listEventAttributes &&
              listEventAttributes.map((attr: any) => (
                <TreeNode
                  key={attr.value}
                  value={attr.value}
                  selectable={!attr?.children?.length}
                  title={
                    attr.children?.length ? (
                      <div onClick={() => handleOnClickParentNode(attr)}>{attr.label}</div>
                    ) : (
                      attr.label
                    )
                  }
                >
                  {buildOptionAttrArchive(attr?.children, selectedAttr)?.map((item: any) => (
                    <TreeNode key={item.value} value={item.value} title={item.label} />
                  ))}
                </TreeNode>
              ))}
          </TreeSelect>
        </Form.Item>

        {form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE) &&
          form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE)?.status !== 1 && (
            <Text type="warning">
              {t(translations.dynamicContent.modal.message.selectDisableEventAttr) as string}
            </Text>
          )}
      </>
    );
  };

  const renderEventFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.EVENT]) {
      return (
        <Text type="error">
          {t(translations.dynamicContent.modal.error.eventBySource) as string}
        </Text>
      );
    }

    return (
      <Form.Item
        style={{ alignItems: 'center' }}
        label={t(translations.dynamicContent.modal.label.selectEvent) as string}
        name={DYNAMIC_CONTENT_SETTING_KEY.EVENT}
        required
        preserve={false}
      >
        <Select
          disabled={!!triggerEvent?.eventActionId && !!triggerEvent?.eventCategoryId}
          loading={isFetchingEventBySource}
          style={{ height: 28 }}
          options={listEvents}
          showSearch
        />
      </Form.Item>
    );
  };

  const renderSourceFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.SOURCE]) {
      return (
        <Text type="error">{t(translations.dynamicContent.modal.error.source) as string}</Text>
      );
    }

    return (
      <Form.Item
        style={{ alignItems: 'center' }}
        label={t(translations.dynamicContent.modal.label.selectSource) as string}
        name={DYNAMIC_CONTENT_SETTING_KEY.SOURCE}
        required
      >
        <Select
          disabled={!isEmpty(triggerEvent?.insightPropertyIds)}
          mode="multiple"
          loading={isFetchingSourceBO}
          options={listSources}
          showSearch
        />
      </Form.Item>
    );
  };

  const renderPromotionCodeAttr = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]) {
      return (
        <Text type="error">
          {t(translations.dynamicContent.modal.error.promotionCodeAttr) as string}
        </Text>
      );
    }
    return (
      <Form.Item
        style={{ alignItems: 'center' }}
        label={t(translations.dynamicContent.modal.label.promotionCodeAttr) as string}
        name={DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE}
        required
      >
        <Select
          loading={isFetchingPromotionCodeAttr}
          style={{ height: 28 }}
          labelInValue
          options={buildOptionAttrArchive(listPromotionCodeAttributes || [], selectedAttr)}
          showSearch
          // errorArchive={errorMessageAttr}
          disabled={isActionArchive}
        />
      </Form.Item>
    );
  };

  const renderPromotionPoolFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL]) {
      return (
        <Text type="error">
          {t(translations.dynamicContent.modal.error.promotionPools) as string}
        </Text>
      );
    }

    return (
      <Form.Item
        style={{ alignItems: 'center' }}
        label={t(translations.dynamicContent.modal.label.promotionPools) as string}
        name={DYNAMIC_CONTENT_SETTING_KEY.PROMOTION_POOL}
        required
      >
        <Select
          loading={isFetchingPromotionPool}
          style={{ height: 28 }}
          options={listPromotionPools}
          showSearch
        />
      </Form.Item>
    );
  };

  const renderVisitorAttrFields = () => {
    if (hasError[DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE]) {
      return (
        <Text type="error">{t(translations.dynamicContent.modal.error.visitorAttr) as string}</Text>
      );
    }

    return (
      <Form.Item
        style={{ alignItems: 'center' }}
        initialValue={listVisitorAttributes[0]}
        label={t(translations.dynamicContent.modal.label.attribute) as string}
        name={DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE}
        required
      >
        <Select
          loading={isFetchingDynamicContentAttr}
          style={{ height: 28 }}
          labelInValue
          options={buildOptionAttrArchive(listVisitorAttributes, selectedAttr?.value)}
          showSearch
          // errorArchive={errorMessageAttr}
          disabled={isActionArchive}
        />
      </Form.Item>
    );
  };

  const renderDisplayFormat = () => {
    const selectedAttr = form.getFieldValue(DYNAMIC_CONTENT_SETTING_KEY.ATTRIBUTE);

    if (
      formSubmitDisabled ||
      !attrDFSettings ||
      isEmpty(attrDFSettings) ||
      !selectedAttr?.dataType ||
      !attrDFOptions.length
    )
      return null;

    if (selectedAttr.dataType === 'number' || selectedAttr.dataType === 'datetime')
      return (
        <Form.Item
          initialValue={attrDFOptions[0].value}
          style={{ alignItems: 'start' }}
          label={t(translations.dynamicContent.modal.label.displayFormat) as string}
          name={DYNAMIC_CONTENT_SETTING_KEY.DISPLAY_FORMAT_TYPE}
          extra={
            <div
              style={{
                display: 'flex',
                flexWrap: 'wrap',
                alignItems: 'center',
                gap: '8px',
                marginTop: '16px',
              }}
            >
              <Text
                color="#555"
                dangerouslySetInnerHTML={{
                  __html: `${t(
                    translations.dynamicContent.modal.label.displayFormat,
                  )}: <bdo dir='ltr'>${formatedAttr}</bdo>`,
                }}
              />
              {!showDetailEditDF && (
                <Button
                  disabled={
                    isActionArchive &&
                    selectedDynamicContentType !== DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value
                  }
                  type="text"
                  style={{ display: 'inline-block' }}
                  onClick={() => setShowDetailEditDF(prevShow => !prevShow)}
                >
                  {t(translations.edit.title) as string}
                </Button>
              )}
            </div>
          }
        >
          <Select
            disabled={
              isActionArchive &&
              selectedDynamicContentType !== DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value
            }
            style={{ height: 28 }}
            options={attrDFOptions}
          />
        </Form.Item>
      );

    return null;
  };

  const renderDetailDFSettings = () => {
    if (showDetailEditDF && selectedDFType && attrDFSettings)
      return (
        <div
          style={{
            border: '1px solid #9e9e9e',
            borderRadius: '2px',
            padding: '8px',
            marginTop: '8px',
            display: 'flex',
            flexDirection: 'column',
            gap: '8px',
          }}
        >
          <DisplayFormat
            infos={infos}
            url={url}
            displayFormatType={selectedDFType}
            formatSettings={attrDFSettings}
            onSettingsChange={(values: any) => setAttrDFSettings(values)}
            dynamicContentType={form.getFieldValue(
              DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE,
            )}
          />
        </div>
      );
  };

  return (
    <ModalCustom
      closable={false}
      destroyOnClose
      forceRender
      mask
      okButtonProps={{ disabled: formSubmitDisabled }}
      title={modalTitle || t(translations.dynamicContent.modal.title.addDynamicContent)}
      visible={visible}
      onCancel={handleOnCancelModal}
      onOk={handleOnOkModal}
    >
      <Spin
        spinning={
          isFetchingSourceBO ||
          isFetchingListBoAttr ||
          isFetchingEventAttr ||
          isFetchingPromotionPool ||
          isFetchingPromotionCodeAttr ||
          isFetchingDynamicContentAttr
        }
      >
        <Form
          style={{ width: '100%' }}
          form={form}
          labelAlign="left"
          labelCol={{ span: 9 }}
          wrapperCol={{ span: 15 }}
          onValuesChange={handleFormValuesChanges}
          initialValues={{
            [DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE]: listDynamicContentTypes[0].value,
          }}
        >
          <Form.Item
            style={{ alignItems: 'center' }}
            label={t(translations.dynamicContent.modal.label.contentSource) as string}
            name={DYNAMIC_CONTENT_SETTING_KEY.DYNAMIC_CONTENT_TYPE}
            required
          >
            <Select style={{ height: 28 }} options={listDynamicContentTypes} />
          </Form.Item>

          {selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.VISITOR_ATTRIBUTE.value && (
            <>{renderVisitorAttrFields()}</>
          )}

          {selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.CUSTOMER_ATTRIBUTE.value && (
            <>{renderCustomerAttrFields()}</>
          )}
          {selectedDynamicContentType === CUSTOM_TYPE.value && <>{renderCustomFunctionFields()}</>}
          {selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.EVENT_ATTRIBUTE.value && (
            <>
              {renderEventFields()}
              {renderSourceFields()}
              {renderEventAttrFields()}
              {isShowEventIndexField
                ? renderIndexFields({ disabled: false, loading: false })
                : null}
            </>
          )}

          {selectedDynamicContentType === DYNAMIC_CONTENT_TYPE.PROMOTION_CODE.value && (
            <>
              {renderPromotionPoolFields()}
              {renderPromotionCodeAttr()}
            </>
          )}

          {selectedDynamicContentType && regexCSType.test(selectedDynamicContentType) && (
            <>
              {renderBoAttrFields()}
              {renderIndexFields({ disabled: isActionArchive, loading: isFetchingListBoAttr })}
            </>
          )}

          {showDisplayFormat && renderDisplayFormat()}
        </Form>

        {showDisplayFormat && renderDisplayFormat() !== null && renderDetailDFSettings()}
        {isShowCustomFunction && selectedDynamicContentType === CUSTOM_TYPE.value && (
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'start',
              marginBottom: '8px',
              marginTop: '10px',
            }}
          >
            <Checkbox onChange={handleSaveAsTemplate} checked={saveAsTemplate}>
              <span style={{ fontSize: '12px' }}>
                {t(translations.dynamicContent.modal.label.saveTemplate) as string}
              </span>
            </Checkbox>
          </div>
        )}
      </Spin>
    </ModalCustom>
  );
};

AddDynamicContent.defaultProps = {
  showDisplayFormat: true,
  showIndex: true,
  APIConfig: {
    domain: 'https://sandbox-media-template.antsomi.com',
    slug: '/api/v1',
    token: '',
    userId: '',
    accountId: '',
  },
};
