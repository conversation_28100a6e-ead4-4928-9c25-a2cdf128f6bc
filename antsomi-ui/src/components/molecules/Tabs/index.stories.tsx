// Libraries
import React from 'react';
import { StoryFn, Meta } from '@storybook/react';

// Components
import { Tabs } from './index';
import styled from 'styled-components';

const StyledTabs = styled(Tabs)`
  .antsomi-tabs-nav {
    margin-bottom: 10px !important;
  }
`;

export default {
  title: 'Molecules/Tabs',
  component: StyledTabs,
  // More on argTypes: https://storybook.js.org/docs/react/api/argtypes
  argTypes: {
    activeKey: {
      name: 'activeKey',
      defaultValue: undefined,
      description: "Current TabPane's key	",
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: '-' },
      },
      control: {
        type: 'text',
      },
    },
    addIcon: {
      name: 'addIcon',
      defaultValue: undefined,
      description: `Customize add icon, only works with type="editable-card"`,
      table: {
        type: { summary: 'ReactNode' },
        defaultValue: { summary: '<PlusOutlined />' },
      },
      control: {
        type: undefined,
      },
    },
    animated: {
      name: 'animated',
      defaultValue: { inkBar: true, tabPane: false },
      description: `Whether to change tabs with animation.`,
      table: {
        type: { summary: 'boolean | { inkBar: boolean, tabPane: boolean }	' },
        defaultValue: { summary: '{ inkBar: true, tabPane: false }' },
      },
      control: {
        type: undefined,
      },
    },
    centered: {
      name: 'centered',
      defaultValue: false,
      description: 'Centers tabs',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
      },
      control: {
        type: 'boolean',
      },
    },
    defaultActiveKey: {
      name: 'defaultActiveKey',
      defaultValue: undefined,
      description: "Initial active TabPane's key, if activeKey is not set",
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: '-' },
      },
      control: {
        type: 'text',
      },
    },
    hideAdd: {
      name: 'hideAdd',
      defaultValue: undefined,
      description: `Hide plus icon or not. Only works while type="editable-card"`,
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
      },
      control: {
        type: 'boolean',
      },
    },
    moreIcon: {
      name: 'moreIcon',
      defaultValue: undefined,
      description: `The custom icon of ellipsis	`,
      table: {
        type: { summary: 'ReactNode' },
        defaultValue: { summary: '<EllipsisOutlined />' },
      },
      control: {
        type: undefined,
      },
    },
    popupClassName: {
      name: 'popupClassName',
      defaultValue: undefined,
      description: `className for more dropdown.`,
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: '-' },
      },
      control: {
        type: 'text',
      },
    },
    renderTabBar: {
      name: 'renderTabBar',
      defaultValue: undefined,
      description: `Replace the TabBar`,
      table: {
        type: {
          summary:
            '(props: DefaultTabBarProps, DefaultTabBar: React.ComponentClass) => React.ReactElement	',
        },
        defaultValue: { summary: '-' },
      },
      control: {
        type: undefined,
      },
    },
    size: {
      name: 'size',
      defaultValue: 'middle',
      description: `Preset tab bar size`,
      table: {
        type: {
          summary: 'large | middle | small',
        },
        defaultValue: { summary: 'middle' },
      },
      control: {
        type: 'select',
        labels: {
          default: 'Large',
          circle: 'Middle',
          round: 'Small',
        },
      },
      options: ['large', 'middle', 'small'],
    },
    tabBarExtraContent: {
      name: 'tabBarExtraContent',
      defaultValue: undefined,
      description: `Extra content in tab bar	`,
      table: {
        type: {
          summary: 'ReactNode | {left?: ReactNode, right?: ReactNode}	',
        },
        defaultValue: { summary: '-' },
      },
      control: undefined,
    },
    tabBarGutter: {
      name: 'tabBarGutter',
      defaultValue: undefined,
      description: `The gap between tabs	`,
      table: {
        type: {
          summary: 'number',
        },
        defaultValue: { summary: '-' },
      },
      control: 'number',
    },
    tabBarStyle: {
      name: 'tabBarStyle',
      defaultValue: undefined,
      description: `Tab bar style object	`,
      table: {
        type: {
          summary: 'CSSProperties',
        },
        defaultValue: { summary: '-' },
      },
      control: undefined,
    },
    tabPosition: {
      name: 'tabPosition',
      defaultValue: 'top',
      description: `Position of tabs	`,
      table: {
        type: {
          summary: 'top | right | bottom | left',
        },
        defaultValue: { summary: 'top' },
      },
      control: {
        type: 'select',
        labels: {
          top: 'Top',
          right: 'Right',
          bottom: 'Bottom',
          left: 'Left',
        },
      },
      options: ['top', 'right', 'bottom', 'left'],
    },
    destroyInactiveTabPane: {
      name: 'destroyInactiveTabPane',
      defaultValue: false,
      description: `Whether destroy inactive TabPane when change tab	`,
      table: {
        type: {
          summary: 'boolean',
        },
        defaultValue: { summary: false },
      },
      control: 'boolean',
    },
    type: {
      name: 'type',
      defaultValue: false,
      description: `Basic style of tabs	`,
      table: {
        type: {
          summary: 'line | card | editable-card',
        },
        defaultValue: { summary: 'line' },
      },
      control: {
        type: 'select',
        labels: {
          line: 'Line',
          card: 'Card',
          'editable-card': 'Editable Card',
        },
      },
      options: ['line', 'card', 'editable-card'],
    },
    onChange: {
      name: 'onChange',
      defaultValue: undefined,
      description: `Callback executed when active tab is changed	`,
      table: {
        type: {
          summary: '(activeKey: string) => void	',
        },
        defaultValue: { summary: '-' },
      },
      control: undefined,
    },
    onEdit: {
      name: 'onEdit',
      defaultValue: undefined,
      description: `Callback executed when tab is added or removed. Only works while type="editable-card"	`,
      table: {
        type: {
          summary: `(action === 'add' ? event : targetKey, action) => void	`,
        },
        defaultValue: { summary: '-' },
      },
      control: undefined,
    },
    onTabClick: {
      name: 'onTabClick',
      defaultValue: undefined,
      description: `Callback executed when tab is clicked	`,
      table: {
        type: {
          summary: '(activeKey: string) => void	',
        },
        defaultValue: { summary: '-' },
      },
      control: undefined,
    },
    onTabScroll: {
      name: 'onTabScroll',
      defaultValue: undefined,
      description: `Trigger when tab scroll	`,
      table: {
        type: {
          summary: '({ direction: left | right | top | bottom }) => void	',
        },
        defaultValue: { summary: '-' },
      },
      control: undefined,
    },
  },
  // argTypes: {
  //   label: {
  //     name: 'Label',
  //     defaultValue: undefined,
  //     description: 'Label of Slider block',
  //     table: {
  //       type: { summary: 'string' },
  //       defaultValue: { summary: '-' },
  //     },
  //     control: {
  //       type: 'text',
  //     },
  //   },
  //   precision: {
  //     name: 'Precision',
  //     defaultValue: 0,
  //     description: 'Precision of inputNumber',
  //     table: {
  //       type: { summary: 'number' },
  //       defaultValue: { summary: '0' },
  //     },
  //     control: {
  //       type: 'number',
  //     },
  //   },
  //   defaultValue: {
  //     name: 'defaultValue',
  //     defaultValue: 0,
  //     description: 'Default value of Slider',
  //     table: {
  //       type: { summary: 'number' },
  //       defaultValue: { summary: '0' },
  //     },
  //     control: {
  //       type: 'number',
  //     },
  //   },
  //   value: {
  //     name: 'value',
  //     defaultValue: undefined,
  //     description: 'Value of Slider',
  //     table: {
  //       type: { summary: 'number' },
  //       defaultValue: { summary: '-' },
  //     },
  //     control: {
  //       type: 'number',
  //     },
  //   },
  //   min: {
  //     name: 'min',
  //     defaultValue: 0,
  //     description: 'Min of slider',
  //     table: {
  //       type: { summary: 'number' },
  //       defaultValue: { summary: '0' },
  //     },
  //     control: {
  //       type: 'number',
  //     },
  //   },
  //   max: {
  //     name: 'max',
  //     defaultValue: 100,
  //     description: 'Max of slider',
  //     table: {
  //       type: { summary: 'number' },
  //       defaultValue: { summary: '100' },
  //     },
  //     control: {
  //       type: 'number',
  //     },
  //   },
  //   onAfterChange: {
  //     name: 'onAfterChange',
  //     defaultValue: undefined,
  //     description: 'Fire when onmouseup is fired',
  //     table: {
  //       type: { summary: '(value) => void' },
  //       defaultValue: { summary: null },
  //     },
  //     control: {
  //       type: 'null',
  //     },
  //   },
  // },
} as Meta<typeof Tabs>;

export const Default = {
  args: {
    items: [
      {
        key: '1',
        label: 'Tab 1',
        children: 'Content of Tab Pane 1',
      },
      {
        key: '2',
        label: 'Tab 2',
        children: 'Content of Tab Pane 2',
      },
      {
        key: '3',
        label: 'Tab 3',
        children: 'Content of Tab Pane 3',
      },
    ],
  },
};
