/* eslint-disable react/no-array-index-key */
/* eslint-disable no-console */
/* eslint-disable react/jsx-no-undef */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @typescript-eslint/no-unused-vars */
// Libraries
import React, { RefObject, useCallback, useRef, useState } from 'react';
import Tagify from '@yaireo/tagify';
import { useImmer } from 'use-immer';
import { Meta } from '@storybook/react';

// Components
import TagifyInput from '../../TagifyInput';
import { Button, Flex, Popover, Scrollbars, Typography, message } from '../../../../atoms';
import { EmojiPopover } from '../../../EmojiPopover';
import { UserIcon } from '../../../../icons';

// Constants
import { DEFAULT_ACCEPT_TAGS, SHORT_LINK_TYPE, SHORT_LINK_V2, TAG_TYPE } from '../../constants';

// Types
import type { TagDataCustomize, TagifyInputRef } from '../../types';

// Utils
import { emojiManufacturer, getImageSourceViberEmoji } from '../../utils';
import { isExistKey } from '@antscorp/antsomi-ui/es/components/organism/AlgorithmsSetting/utils';

const { Text } = Typography;

const {
  CUSTOMER,
  VISITOR,
  EVENT,
  JOURNEY,
  CAMPAIGN,
  VARIANT,
  PROMOTION_CODE,
  CUSTOM_FN,
  SHORT_LINK,
  OBJECT_WIDGET,
  EMOJI,
  CONTENT_SOURCE_GROUP,
} = TAG_TYPE;

export default {
  title: 'Molecules/TagifyInput/Demo',
  component: TagifyInput,
  argTypes: {
    key: {
      name: 'key',
      description:
        'The key of the input. Should be unique per component to make component *re-render* with new value if the key changes.',
      control: {
        type: 'text',
      },
    },
    escapeHTML: {
      name: 'escapeHTML',
      control: {
        type: 'boolean',
      },
    },
    name: {
      name: 'name',
      control: {
        type: 'text',
      },
    },
    placeholder: {
      name: 'placeholder',
      control: {
        type: 'text',
      },
    },
    readonly: {
      name: 'readonly',
      control: {
        type: 'boolean',
      },
    },
    readonlyTag: {
      name: 'readonlyTag',
      control: {
        type: 'boolean',
      },
    },
    disabled: {
      name: 'disabled',
      control: {
        type: 'boolean',
      },
    },
    maxLength: {
      name: 'maxLength',
      control: {
        type: 'number',
      },
    },
    maxPersonalizeTags: {
      name: 'maxPersonalizeTags',
      control: {
        type: 'number',
      },
    },
    minWidth: {
      name: 'minWidth',
      control: {
        type: 'number',
      },
    },
    isSingleLineText: {
      name: 'isSingleLineText',
      control: {
        type: 'boolean',
      },
    },
    acceptableTagPattern: {
      name: 'acceptableTagPattern',
      control: {
        type: 'multi-select',
      },
    },
  },
  parameters: {
    docs: {
      description: {
        component: `## Overview and Main Concept

The **Tagify Input Component** is a specialized input field that allows users to enter and manipulate tags dynamically within a text area. It leverages the [Tagify](https://yaireo.github.io/tagify/) library to create an interactive tagging system, integrated with **React** to handle states, props, and events.

### Main Features:
- **Tag Creation & Deletion**: Users can create and remove tags by typing text and hitting the appropriate key (e.g., Enter, Backspace).
- **URL Detection**: Automatically detects URLs in the text and converts them into clickable tags.
- **Readonly & Disabled Handling**: Supports readonly states for the entire input or individual tags.
- **Debounced Input Handling**: Optimized input handling through debounce to improve performance with large data sets or complex operations.
- **Customizable Tags**: Tags can display special content, like emojis or links, based on the type of input.
`,
      },
    },
  },
} satisfies Meta<typeof TagifyInput>;

const mapAttributes = {
  [JOURNEY]: {
    journey_id: {
      value: 'journey_id',
      bracketed_code: 'journey_id',
      display: 'Journey ID',
      dataType: 'object_id',
      itemDataType: 'string',
      label: 'Journey ID',
      statusItemCode: 'ACTIVE',
    },
  },
  [CAMPAIGN]: {
    campaign_id: {
      value: 'campaign_id',
      bracketed_code: 'campaign_id',
      display: 'Campaign ID',
      dataType: 'object_id',
      itemDataType: 'string',
      label: 'Campaign ID',
      statusItemCode: 'ACTIVE',
    },
  },
  [VARIANT]: {
    variant_id: {
      value: 'variant_id',
      bracketed_code: 'variant_id',
      display: 'Variant ID',
      dataType: 'object_id',
      itemDataType: 'string',
      label: 'Variant ID',
      statusItemCode: 'ACTIVE',
    },
  },
  [CUSTOMER]: {
    customer_id: {
      value: 'customer_id',
      bracketed_code: 'customer_id',
      display: 'Customer ID',
      dataType: 'object_id',
      itemDataType: 'string',
      label: 'Customer ID',
      statusItemCode: 'ACTIVE',
    },
  },
  [EVENT]: {
    'undefined.order_date': {
      bracketed_code: 'undefined.order_date',
      name: 'order_date',
      display: 'Order date',
      dataType: 'datetime',
      itemDataType: 'datetime',
      itemTypeId: 731521,
      value: 'order_date-731521',
      encryptCode: 'order_date',
      propertySyntax: 'undefined',
      label: 'Order date',
      isSort: 0,
      isFilter: 1,
      type: 'item',
      propertyName: 'order_date',
      statusItemCode: 'ACTIVE',
      autoSuggestion: 0,
      configSuggestion: {
        feKey: '731521-undefined-order_date',
        scope: 1,
        itemTypeId: 731521,
        propertyCode: 'order_date',
        itemPropertyName: 'order_date',
        systemDefined: 0,
        isPk: 0,
      },
      displayFormat: {
        type: 'DATE_AND_TIME',
        config: {
          date: {
            check: true,
            value: 'long',
          },
          time: {
            check: true,
            value: 'medium',
            timeFormat: '24',
          },
          format: 'DD/MM/YYYY',
        },
      },
      isEncrypt: 0,
      sources: [*********, *********],
      status: 1,
    },
    ad_zone_id: {
      bracketed_code: 'ad_zone_id',
      name: 'ad_zone_id',
      display: 'Ad Zone',
      dataType: 'string',
      itemDataType: 'string',
      itemTypeId: -1013,
      itemTypeName: 'ad_zone',
      value: 'ad_zone_id--1013',
      propertySyntax: 'dims.ad_zone_id',
      label: 'Ad Zone',
      isSort: 0,
      isFilter: 1,
      type: 'event',
      propertyName: 'dims.ad_zone_id',
      eventPropertyName: 'ad_zone_id',
      statusItemCode: 'ACTIVE',
      displayFormat: {
        type: 'RAW_STRING',
        config: {},
      },
      isEncrypt: 0,
      sources: [
        *********, *********, *********, *********, *********, *********, *********, *********,
        *********, *********, *********, *********, *********, *********, *********, *********,
        *********, *********, *********, *********, *********, *********, *********, *********,
        *********, *********, *********, *********, *********, *********, *********, *********,
        *********, *********, *********, *********, *********, *********, *********, *********,
        *********, *********, *********, *********, *********, *********, *********, *********,
        *********, *********, *********, *********, *********, *********, *********, 556356103,
        556302011, 556301674, 556356959, 556352131, 556302008, 556301875, 556430427, 556301954,
        556301357, 556301727,
      ],
      autoSuggestion: 0,
      status: 0,
    },
  },
  [PROMOTION_CODE]: {
    huy_test_promo_204_1: {
      value: 'huy_test_promo_204_1',
      id: 475947,
      label: 'huy test promo 20/4 1',
      bracketed_code: 'huy_test_promo_204_1',
    },
  },
  [VISITOR]: {
    user_id: {
      value: 'user_id',
      bracketed_code: 'user_id',
      display: 'User ID',
      dataType: 'object_id',
      itemDataType: 'string',
      label: 'User ID',
      statusItemCode: 'ACTIVE',
    },
  },
  [CUSTOM_FN]: {
    test_delivery_datetime_1: {
      personalizationName: 'Test delivery datetime 1',
      dataType: 'datetime',
      displayFormat: {
        type: 'DATE_AND_TIME',
        config: {
          date: {
            check: true,
            value: 'short',
          },
          time: {
            check: true,
            value: 'medium',
            timeFormat: '12',
          },
          format: 'MM/DD/YYYY',
        },
      },
      formular: "d = new Date();\nreturn moment(d).add(1,'year').format('DD MM YYYY');\n",
      row_count: 48,
      template_id: 7683241,
      template_code: 'test_delivery_datetime_1',
      template_type: 'custom',
      template_name: 'Test delivery datetime 1',
      custom_function: "d = new Date();\nreturn moment(d).add(1,'year').format('DD MM YYYY');\n",
      output_data_type: 'datetime',
      output_format: {
        type: 'DATE_AND_TIME',
        config: {
          date: {
            check: true,
            value: 'short',
          },
          time: {
            check: true,
            value: 'medium',
            timeFormat: '12',
          },
          format: 'MM/DD/YYYY',
        },
      },
      status: 1,
      properties: null,
      c_user_id: 1600083946,
      ctime: '2024-08-29T09:02:31.823Z',
      u_user_id: 1600083946,
      utime: '2024-08-29T09:02:31.823Z',
      c_user_name: 'Huỳnh Ngọc Thành',
      u_user_name: 'Huỳnh Ngọc Thành',
    },
  },
  csgnztb2: {
    add_to_cart_today: {
      display: 'Add to cart today',
      label: 'Add to cart today',
    },
  },
  [SHORT_LINK_V2]: {
    // 123123: {
    //   id: 123123,
    //   label: ' label 123123',
    //   value: '123123',
    // },
    12512512: {
      id: 12512512,
      label: ' label 12512512',
      value: '12512512',
    },
  },
};

const mapErrorAttributes = {
  [SHORT_LINK_V2]: {
    123123: {
      isExist: false,
    },
    12512512: {
      isExist: true,
    },
  },
};

export const Default = {};

const initValue = `List of attributes tag:
- Journey tag: #{story.journey_id||"customerFallback"}
- Campaign tag: #{campaign.campaign_id||"fallbackCampaign"}
- Variant tag: #{variant.variant_id||"variantFallback"}
- Link nè: https://test.com.vn short link #{shortlink(https://test.com.vn)} static #{shortlink_static(https://static.test.com.vn)}
- Object widget: #{objectWidget.last_updated} duplicate #{objectWidget.last_updated}
- Content source tag: #{groups.csgnztb2[1].add_to_cart_today}
- Customer tag: #{customer.customer_id||"customerFallback"}
- Emoji nè: 😇 🥰 😝 (smiley) $((line:5ac2213e040ab15980c9b447:001)) 😅 
- Link: #{shortlink(https://sandbox-api.cdp.asia/be/a360/user?portal_id=33167&user_id=personalize_pattern&property_names=user_id,date_created,last_updated,phone_sms&scopes=properties&token=cdp2019)} (smiley)  - Visitor tag #{visitor.user_id||""} - Event Attribute #{event.ad_zone_id||"eventFallback"}
- Promotion code: #{promotion_code.huy_test_promo_204_1}
- Custom function: #{custom.test_delivery_datetime_1} and link https://example.com 
and https://test.example.com end.`;

const initValue2 = `List of attributes tag:
- Journey tag: #{story.journey_id||"customerFallback"}
- Link nè: https://test.com.vn short link v1: #{shortlink(https://test.com.vn)} - v2: #{shortlink_v2(123123, https://test.com.vn)} static #{shortlink_static_v2(12512512, https://static.test.com.vn)}
`;

export const RenderWithTags = {
  render: () => (
    <TagifyInput
      acceptableTagPattern={DEFAULT_ACCEPT_TAGS}
      initialValue={initValue2}
      mapAttributes={mapAttributes}
      mapErrorAttributes={mapErrorAttributes}
      onChange={() => {}}
    />
  ),
};

// export const SingleLine = {
//   name: 'Render single line input',
//   render: () => {
//     const [messageApi, contextHolder] = message.useMessage();

//     return (
//       <Flex vertical gap={20}>
//         <Flex vertical gap={10}>
//           <Typography>Without addons</Typography>
//           <TagifyInput
//             acceptableTagPattern={DEFAULT_ACCEPT_TAGS}
//             initialValue={initValue}
//             mapAttributes={mapAttributes}
//             isSingleLineText
//             onChange={() => {}}
//           />
//         </Flex>
//         {contextHolder}
//         <Flex vertical gap={10}>
//           <Typography>With addons</Typography>
//           <TagifyInput
//             acceptableTagPattern={DEFAULT_ACCEPT_TAGS}
//             initialValue={initValue}
//             mapAttributes={mapAttributes}
//             isSingleLineText
//             onChange={() => {}}
//           >
//             <EmojiPopover
//               onEmojiClick={newEmoji => {
//                 messageApi.open({
//                   type: 'success',
//                   content: `Emoji information: ${JSON.stringify(newEmoji) as any}`,
//                 });
//               }}
//             />
//           </TagifyInput>
//         </Flex>
//       </Flex>
//     );
//   },
// };

// export const Addons = {
//   name: 'Render with add emoji',
//   render: () => {
//     const tagifyRef: RefObject<TagifyInputRef> | null | undefined = useRef(null);

//     const [tags, setTags] = useState<string>('😀 add new emoji here');

//     const onAdd = (tag: TagDataCustomize | string) => {
//       if (!tagifyRef.current) return;

//       tagifyRef.current.onAddNewTag(tag);
//     };

//     const handleChange = useCallback((inputValue: string) => {
//       console.log('----- Change input', { inputValue });
//       setTags(inputValue);
//     }, []);

//     return (
//       <TagifyInput
//         ref={tagifyRef}
//         acceptableTagPattern={DEFAULT_ACCEPT_TAGS}
//         initialValue={tags}
//         mapAttributes={{}}
//         onChange={handleChange}
//       >
//         <EmojiPopover onEmojiClick={onAdd} />
//       </TagifyInput>
//     );
//   },
//   parameters: {
//     docs: {
//       description: {
//         story: 'Render with emoji',
//       },
//     },
//   },
// };

// export const AddonPersonalizeAndEmoji = {
//   name: 'Playground with personalize tag and emoji',
//   render: () => {
//     const tagifyRef: RefObject<TagifyInputRef> | null | undefined = useRef(null);

//     const [stateTagify, setStateTagify] = useImmer<{
//       readonly?: boolean;
//       readonlyTag?: boolean;
//       disabled?: boolean;
//       placeholder?: string;
//       maxTags?: number;
//       mapAttributes?: Record<string, any>;
//     }>({
//       maxTags: undefined,
//       disabled: undefined,
//       readonly: undefined,
//       readonlyTag: undefined,
//       placeholder: undefined,
//       mapAttributes,
//     });

//     const [tags, setTags] = useState<string>(initValue);

//     const onAdd = (tag: TagDataCustomize | string) => {
//       if (!tagifyRef.current) return;

//       tagifyRef.current.onAddNewTag(tag);
//     };

//     const handleChange = useCallback((inputValue: string) => {
//       console.log('----- Change input', { inputValue });
//       setTags(inputValue);
//     }, []);

//     const onTagClick = (tagDetail: Tagify.ClickEventData<TagDataCustomize>) => {
//       console.log('tag clicked', tagDetail);
//       const { data, tag } = tagDetail;

//       if (!tagifyRef.current) return;

//       tagifyRef.current.onReplaceTag(tag, {
//         ...data,
//         label: `${data.label} (smiley)`,
//         value: `#{objectWidget.last_updated||"last_updated"}`,
//       });
//     };

//     const tagListExample = [
//       {
//         label: (
//           <Text>
//             Content source tag <Text mark>{`(#{groups.csgnztb2[1].add_to_cart_today})`}</Text>
//           </Text>
//         ),
//         value: {
//           value: '#{groups.csgnztb2[1].add_to_cart_today}',
//           type: CONTENT_SOURCE_GROUP,
//           label: 'Content source tag',
//         },
//       },
//       {
//         label: (
//           <Text>
//             Journey Attribute <Text mark>(story.journey_id)</Text>
//           </Text>
//         ),
//         value: {
//           value: '#{story.journey_id||"customerFallback"}',
//           type: JOURNEY,
//           label: 'Journey Id',
//         },
//       },
//       {
//         label: (
//           <Text>
//             Campaign Attribute <Text mark>(campaign.campaign_id)</Text>
//           </Text>
//         ),
//         value: {
//           value: '#{campaign.campaign_id||"fallbackCampaign"}',
//           type: CAMPAIGN,
//           label: 'Campaign Id',
//         },
//       },
//       {
//         label: (
//           <Text>
//             Variant Attribute <Text mark>(variant.variant_id)</Text>
//           </Text>
//         ),
//         value: {
//           value: '#{variant.variant_id||"variantFallback"}',
//           type: VARIANT,
//           label: 'Variant Id',
//         },
//       },
//       {
//         label: (
//           <Text>
//             Customer <Text mark>(customer.customer_id)</Text>
//           </Text>
//         ),
//         value: {
//           value: '#{customer.customer_id||"customerFallback"}',
//           type: CUSTOMER,
//           label: 'Customer ID',
//         },
//       },
//       {
//         label: (
//           <Text>
//             Visitor <Text mark>(visitor.user_id)</Text>
//           </Text>
//         ),
//         value: {
//           value: '#{visitor.user_id||""}',
//           type: VISITOR,
//           label: 'User ID',
//         },
//       },
//       {
//         label: (
//           <Text>
//             Event attribute <Text mark>(event.ad_zone.last_updated)</Text>
//           </Text>
//         ),
//         value: {
//           value: '#{event.ad_zone_id||"eventFallback"}',
//           type: EVENT,
//           label: 'Ad Zone',
//         },
//       },
//       {
//         label: (
//           <Text>
//             Promotion code <Text mark>(promotion_code.huy_test_promo_204_1)</Text>
//           </Text>
//         ),
//         value: {
//           value: '#{promotion_code.huy_test_promo_204_1.id}',
//           label: 'huy test promo 20/4 1',
//           type: PROMOTION_CODE,
//         },
//       },
//       {
//         label: (
//           <Text>
//             Custom Function <Text mark>(custom.test_delivery_datetime_1)</Text>
//           </Text>
//         ),
//         value: {
//           value: '#{custom.test_delivery_datetime_1}',
//           type: CUSTOM_FN,
//           label: 'Test delivery datetime 1',
//         },
//       },
//       {
//         label: (
//           <Text>
//             Short link Individual <Text mark>{`(#{shortlink(https://test.com.vn)})`}</Text>
//           </Text>
//         ),
//         value: {
//           label: 'https://ants.ly/1qxtk',
//           value: `#{shortlink(https://test.com.vn)}`,
//           type: SHORT_LINK,
//           shortlinkType: SHORT_LINK_TYPE.INDIVIDUAL,
//         },
//       },
//       {
//         label: (
//           <Text>
//             Short link General
//             <Text mark>{`(#{shortlink_static(https://test-static.com.vn)})`}</Text>
//           </Text>
//         ),
//         value: {
//           value: `#{shortlink_static(https://test-static.com.vn)}`,
//           type: SHORT_LINK,
//           shortlinkType: SHORT_LINK_TYPE.GENERAL,
//           label: 'https://ants.ly/jsxh4',
//         },
//       },
//       {
//         label: (
//           <Flex gap={10} align="center">
//             Viber Emoji (smiley)
//             <img
//               src={getImageSourceViberEmoji('smiley.png')}
//               alt="smiley"
//               style={{ width: 24, height: 24 }}
//             />
//           </Flex>
//         ),
//         value: {
//           value: '(smiley)',
//           label: '(smiley)',
//           type: EMOJI,
//           collection: 'viber',
//         },
//       },
//       {
//         label: <div>Common emoji 😅</div>,
//         value: '😅',
//       },
//       {
//         label: (
//           <Flex gap={10} align="center">
//             Line emoji
//             <img
//               src={emojiManufacturer('$((line:5ac2213e040ab15980c9b447:001))', 'line') as any}
//               alt="line"
//               width={24}
//               height={24}
//             />
//           </Flex>
//         ),
//         value: {
//           value: '$((line:5ac2213e040ab15980c9b447:001))',
//           label: '$((line:5ac2213e040ab15980c9b447:001))',
//           type: EMOJI,
//           collection: 'line',
//         },
//       },
//     ];

//     const Title = <h3>List Tags to playground</h3>;
//     const Content = (
//       <Scrollbars autoHeight autoHeightMax={350}>
//         <Flex gap={10} vertical>
//           {tagListExample.map((ex, idx) => {
//             const { label, value } = ex;

//             return (
//               <Flex key={idx} gap={10} align="center" justify="space-between">
//                 <Flex gap={10} align="center">
//                   {label}
//                 </Flex>
//                 <Button onClick={() => onAdd(value as any)}>Add</Button>
//               </Flex>
//             );
//           })}
//         </Flex>
//       </Scrollbars>
//     );

//     return (
//       <Flex vertical gap={40}>
//         <TagifyInput
//           placeholder={stateTagify.placeholder}
//           readonly={stateTagify.readonly}
//           readonlyTag={stateTagify.readonlyTag}
//           disabled={stateTagify.disabled}
//           ref={tagifyRef}
//           // escapeHTML
//           maxPersonalizeTags={stateTagify.maxTags}
//           minWidth="unset"
//           onChange={handleChange}
//           onTagClick={onTagClick}
//           acceptableTagPattern={DEFAULT_ACCEPT_TAGS}
//           initialValue={tags}
//           mapAttributes={stateTagify.mapAttributes}
//           // status="error"
//           // cssVariables={cssVariables}
//         >
//           <EmojiPopover onEmojiClick={onAdd} />
//           <Popover title={Title} content={Content} placement="bottom" trigger="click">
//             <Button type="link" icon={<UserIcon />} />
//           </Popover>
//         </TagifyInput>

//         <Flex vertical gap={10}>
//           <Button
//             onClick={() => {
//               setStateTagify(draft => {
//                 draft.placeholder = 'Changed placeholder';
//               });
//             }}
//           >
//             Set placeholder
//           </Button>
//           <Button
//             onClick={() =>
//               setStateTagify(draft => {
//                 draft.disabled = !draft.disabled;
//               })
//             }
//           >
//             Toggle disabled
//           </Button>
//           <Button
//             onClick={() =>
//               setStateTagify(draft => {
//                 draft.readonlyTag = !draft.readonlyTag;
//               })
//             }
//           >
//             Toggle read only tag
//           </Button>

//           <Button
//             onClick={() =>
//               setStateTagify(draft => {
//                 draft.readonly = !draft.readonly;
//               })
//             }
//           >
//             Toggle read only
//           </Button>
//           <Button
//             onClick={() =>
//               setStateTagify(draft => {
//                 draft.maxTags = 5;
//               })
//             }
//           >
//             Set max tags
//           </Button>
//           <Button
//             onClick={() =>
//               setStateTagify(draft => {
//                 draft.mapAttributes = mapAttributes;
//               })
//             }
//           >
//             Set mapAttributes
//           </Button>
//           <Button
//             onClick={() => {
//               setTags('');
//             }}
//           >
//             Reset text
//           </Button>
//           <Button
//             onClick={() => {
//               setTags(initValue);
//             }}
//           >
//             Add Text again
//           </Button>
//         </Flex>
//       </Flex>
//     );
//   },
//   parameters: {
//     docs: {
//       description: {
//         story: 'Can be used with various options',
//       },
//     },
//   },
// };

// export const Status = {
//   name: 'Render input with status',
//   render: () => {
//     const tagifyRef: RefObject<TagifyInputRef> | null | undefined = useRef(null);

//     const [tags, setTags] = useState<string>('😀 input with various status');

//     const onAdd = (tag: TagDataCustomize | string) => {
//       if (!tagifyRef.current) return;

//       tagifyRef.current.onAddNewTag(tag);
//     };

//     const handleChange = useCallback((inputValue: string) => {
//       console.log('----- Change input', { inputValue });
//       setTags(inputValue);
//     }, []);

//     return (
//       <Flex vertical gap={30}>
//         <TagifyInput
//           ref={tagifyRef}
//           acceptableTagPattern={DEFAULT_ACCEPT_TAGS}
//           initialValue={tags}
//           mapAttributes={{}}
//           status="success"
//           onChange={handleChange}
//         >
//           <EmojiPopover onEmojiClick={onAdd} />
//         </TagifyInput>
//         <TagifyInput
//           ref={tagifyRef}
//           acceptableTagPattern={DEFAULT_ACCEPT_TAGS}
//           initialValue={tags}
//           mapAttributes={{}}
//           status="warning"
//           onChange={handleChange}
//         >
//           <EmojiPopover onEmojiClick={onAdd} />
//         </TagifyInput>
//         <TagifyInput
//           ref={tagifyRef}
//           acceptableTagPattern={DEFAULT_ACCEPT_TAGS}
//           initialValue={tags}
//           mapAttributes={{}}
//           status="error"
//           onChange={handleChange}
//         >
//           <EmojiPopover onEmojiClick={onAdd} />
//         </TagifyInput>
//       </Flex>
//     );
//   },
//   parameters: {
//     docs: {
//       description: {
//         story: 'Render with some kinds of status like *success*, *warning*, *error*',
//       },
//     },
//   },
// };

// export const ErrorAttributes = {
//   name: 'Render input with error tag',

//   render: () => {
//     const [messageApi, contextHolder] = message.useMessage();

//     const initValueError = `List of attributes tag:
//     - Remove: #{promotion_code.huy_test_promo_204_remove}
//     - No Permission: #{promotion_code.huy_test_promo_204_no_permission}
//       `;

//     const mapErrorAttributes = {
//       [PROMOTION_CODE]: {
//         huy_test_promo_204_remove: {
//           isExist: false,
//           isView: true,
//         },
//         huy_test_promo_204_no_permission: {
//           isExist: true,
//           isView: false,
//         },
//       },
//     };

//     return (
//       <Flex vertical gap={20}>
//         <Flex vertical gap={10}>
//           <Typography>With mapErrorAttributes</Typography>
//           <TagifyInput
//             acceptableTagPattern={DEFAULT_ACCEPT_TAGS}
//             initialValue={initValueError}
//             mapAttributes={mapAttributes}
//             mapErrorAttributes={mapErrorAttributes}
//             isSingleLineText
//             onChange={() => {}}
//           />
//         </Flex>
//         {contextHolder}
//       </Flex>
//     );
//   },
// };
