/* eslint-disable react-hooks/exhaustive-deps */
// Libraries
import React, {
  useRef,
  useEffect,
  useCallback,
  useMemo,
  memo,
  forwardRef,
  useImperativeHandle,
  useLayoutEffect,
  MouseEventHandler,
  useState,
} from 'react';
import _ from 'lodash';
import htmlEntities from 'he';

// Assets
import iconWarning from '@antscorp/antsomi-ui/es/assets/images/warning-icon.png';
import iconError from '@antscorp/antsomi-ui/es/assets/images/error-icon.png';

// Hooks
import { useDeepCompareMemo } from '@antscorp/antsomi-ui/es/hooks/useDeepCompareMemo';
import { useDeepCompareEffect } from '@antscorp/antsomi-ui/es/hooks/useDeepCompareEffect';

// Components
import Tagify from '@yaireo/tagify';

// Css
import '@yaireo/tagify/dist/tagify.css';

// Types
import {
  CustomEventTagify,
  MapAttributesProps,
  TagDataCustomize,
  TagType,
  TagifyInputProps,
  TagifyInputRef,
} from './types';

// Styled
import { TagTextArea, TagifyWrapper, WrapperPlaceHolder } from './styled';

// Utils
import {
  parseTagStringToTagify,
  convertInputStringToOriginal,
  emojiManufacturer,
  getEmojiTag,
  isPersonalizeTagType,
  generateTagContent,
  unescapeString,
  hasLineBreak,
  selectRange,
  isTagClickable,
  findURLInTextNodes,
  getAttributesString,
  isAnchorNodeChildOfElement,
  isShortLinkTagType,
  isCaretAtEndOfTextNodeWithNextTag,
  getCurrentSelectionAndCloneRange,
  handleEnterWithNextTag,
  handleTextNodeBackspace,
} from './utils';
import {
  acceptablePatternChecking,
  detectURLRegex,
  getCachedRegex,
  getPersonalizeTagInfo,
  getShortLinkTagInfo,
  patternHandlers,
} from './patternHandlers';

// Constants
import {
  DETECT_LINK,
  EMOJI,
  FORCE_SHOW_TOOLTIP,
  INVALID_TAG,
  MESSAGE_TAG,
  NO_VIEW_TAG,
  PERSONALIZE_PTN,
  PROMOTION_CODE,
  READONLY_TAG,
  REMOVED_TAG,
  SHORT_LINK,
  SHORT_LINK_PTN,
  SHORT_LINK_V2,
  SHORT_LINK_V2_GENERAL_PTN,
  SHORT_LINK_V2_INDIVIDUAL_PTN,
  defaultCssVariables,
  tagifyDefaultProps,
} from './constants';

const TagifyInput = forwardRef<TagifyInputRef, TagifyInputProps>((props, ref) => {
  // Props
  const {
    initialValue,
    escapeHTML,
    status,
    readonly,
    readonlyTag,
    realtime,
    disabled,
    maxLength,
    maxHeight,
    minWidth,
    placeholder,
    minWidthPlaceholder,
    isSingleLineText,
    acceptableTagPattern,
    mapAttributes,
    mapErrorAttributes,
    maxPersonalizeTags,
    name,
    children,
    cssTagifyVariables,
    onTagClick,
    onChange,
  } = props;

  // States
  const [isLineBreak, setIsLineBreak] = useState(hasLineBreak(initialValue));
  const [tooltipRefresher, setTooltipRefresher] = useState(1);

  // Refs
  const inputRef = useRef<HTMLTextAreaElement | null>(null);
  const tagifyRef = useRef<Tagify<TagDataCustomize> | null>(null);
  const tagifyWrapperRef = useRef<HTMLDivElement | null>(null);
  const placeholderRef = useRef<HTMLDivElement | null>(null);
  const lastRange = useRef<Range | null>(null);

  // Memoizations
  const cssVariablesMemoized = useMemo(
    () => _.assign({}, defaultCssVariables, cssTagifyVariables || {}),
    [cssTagifyVariables],
  );

  // Only run in the first render
  // Use for the initialize value of the tagify instance
  const parsedDefaultValue = useDeepCompareMemo(() => {
    if (!escapeHTML) {
      return parseTagStringToTagify(initialValue, acceptableTagPattern);
    }

    // Sometimes the input value has html tags, it makes the input rendered incorrectly -> it rendered html
    // -> need to escape the html tags to render correctly
    const escapeContent = htmlEntities.escape(initialValue);
    const content = parseTagStringToTagify(escapeContent, acceptableTagPattern);

    return content;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onInputDebounceCallback = useCallback(
    _.debounce((callback, event: CustomEventTagify) => {
      callback(event);
    }, 350),
    [],
  );
  const onChangeDebounceCallback = useCallback(
    _.debounce((callback, event: CustomEventTagify) => {
      callback(event);
    }, 500),
    [],
  );

  const onSyncSelectionStateTagify = useCallback(() => {
    try {
      if (!tagifyRef.current) {
        throw new Error('Tagify instance is not initialized');
      }

      const instancePrototype = Object.getPrototypeOf(tagifyRef.current);

      if (!_.has(instancePrototype, 'setStateSelection')) {
        throw new Error('Tagify instance does not support setStateSelection');
      }

      // Update the selection of the Tagify instance
      (tagifyRef.current as any).setStateSelection();

      return true;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error);
      return false;
    }
  }, []);

  const onSaveLastRange = useCallback((newRange?: Range) => {
    if (newRange) {
      lastRange.current = newRange;
    } else {
      const selection = window.getSelection();
      if (selection?.rangeCount) {
        const range = selection.getRangeAt(0);
        lastRange.current = range;
      }
    }
  }, []);

  const onSelectionAfterInjection = useCallback(
    (addRangeAfterInjected?: boolean) => {
      try {
        if (!tagifyRef.current) {
          throw new Error('Tagify instance is not initialized');
        }

        const { scope } = tagifyRef.current?.DOM;
        const anchorNodeInstance = _.get(
          tagifyRef.current,
          'state.selection.anchorNode',
          null,
        ) as Node | null;
        const isValidSelection = isAnchorNodeChildOfElement(scope, anchorNodeInstance);

        if (isValidSelection) {
          const selection = window.getSelection();
          if (!selection?.rangeCount) return;

          const range = selection.getRangeAt(0);

          if (addRangeAfterInjected) {
            // Need to re-select to keep the caret position
            selection.removeAllRanges();
            selection.addRange(range);
          }

          // Save of the last range to restore it in case lost the selection
          // E.g user blur of the input
          // and then click direct to the popup without focus input to add new tag
          onSaveLastRange(range);
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Error while restoring selection after injection', error);
      }
    },
    [onSaveLastRange],
  );

  const onAdjustSelectionIfNeeded = useCallback(() => {
    try {
      if (!tagifyRef.current) {
        throw new Error('Tagify instance is not initialized');
      }

      const { settings, DOM } = tagifyRef.current;
      const rangeInstance = _.get(tagifyRef.current, 'state.selection.range', null) as Range | null;
      const anchorNodeInstance = _.get(
        tagifyRef.current,
        'state.selection.anchorNode',
        null,
      ) as Node | null;
      const { empty } = settings.classNames;

      const isValidAnchor = isAnchorNodeChildOfElement(DOM.scope, anchorNodeInstance);
      const selection = window.getSelection();
      const isValidRange = selection?.rangeCount && rangeInstance;
      const hasValidSelection = isValidRange && isValidAnchor;

      // In case not have the selection yet or lost the selection,
      if (!hasValidSelection) {
        // need to restore the last range before inject a new tag if the last range exists
        if (lastRange.current) {
          selection?.removeAllRanges();
          selection?.addRange(lastRange.current);
        } else if (!isValidRange) {
          // If the last range is lost, need to select the last text node of the input
          const { input: inputElement } = tagifyRef.current.DOM;

          if (inputElement) {
            // Get all child nodes that are of type TEXT_NODE or ELEMENT_NODE with nodeName TAG
            const nodeList = Array.from(inputElement.childNodes).filter((node: ChildNode) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                return node.nodeName === 'TAG';
              }

              return node.nodeType === Node.TEXT_NODE;
            });

            if (nodeList.length && selection) {
              const lastNode = nodeList[nodeList.length - 1];

              // In case have the last text nodes
              // -> need to place the caret at the end of the last text
              if (lastNode.nodeType === Node.TEXT_NODE && lastNode?.textContent) {
                const textNodeLength = lastNode.textContent.length;

                // Place the caret at the end of the last text
                selectRange(lastNode, textNodeLength, textNodeLength);

                onSyncSelectionStateTagify();
              } else if (lastNode.nodeType === Node.ELEMENT_NODE && lastNode?.nodeName === 'TAG') {
                // In case no text nodes but have an element node
                const lastNodeExceptBrLength = inputElement.childNodes.length - 1; // Exclude the last <br>

                // Place the caret at the end of the last element node
                selectRange(inputElement, lastNodeExceptBrLength, lastNodeExceptBrLength);

                onSyncSelectionStateTagify();
              }
            } else if (DOM?.scope?.classList?.contains(empty)) {
              // In case no text nodes and the input is empty
              selectRange(inputElement, 0, 0);

              onSyncSelectionStateTagify();
            }
          }
        }
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error while adjusting selection', error);
    }
  }, [onSyncSelectionStateTagify]);

  const onInjectTagAtCaret = useCallback(
    (newTag: string | HTMLElement, addRangeAfterInjected?: boolean) => {
      try {
        if (!tagifyRef.current) {
          throw new Error('Tagify instance is not initialized');
        }

        const { settings } = tagifyRef.current;
        const { empty } = settings.classNames;

        // Adjust the selection before injecting the new tag
        onAdjustSelectionIfNeeded();

        // Inject the new tag
        tagifyRef.current.injectAtCaret(newTag);

        // Need to remove the empty class immediately after tag added to make the valid the DOM
        tagifyRef.current.toggleClass(empty, false);

        onSelectionAfterInjection(addRangeAfterInjected);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.warn('Error while injecting tag at caret', error);
      }
    },
    [onAdjustSelectionIfNeeded, onSelectionAfterInjection],
  );

  /**
   * Updates the window selection to highlight a specified range of text within a given node.
   * */
  const updateWindowSelection = useCallback(
    (node: ChildNode, start: number, end: number) => {
      if (!node || !_.isNumber(start) || !_.isNumber(end)) return false;

      if (node) {
        try {
          const range = document.createRange();
          const textNodeLength = node.textContent?.length;

          if (textNodeLength && start <= textNodeLength && end <= textNodeLength) {
            range?.setStart(node, start);
            range?.setEnd(node, end);

            const selection = window.getSelection();
            selection?.removeAllRanges();
            selection?.addRange(range);

            return onSyncSelectionStateTagify();
          }

          throw new Error('Invalid start/end position');
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error(error);
          return false;
        }
      }

      return false;
    },
    [onSyncSelectionStateTagify],
  );

  const replaceURLsWithTags = useCallback(
    (nodesURl: ChildNode[], lastRange: 0 | Range | undefined) => {
      try {
        if (!tagifyRef.current) {
          throw new Error('Tagify instance is not initialized');
        }

        let atLeastOneInjectionFlag = false;

        // Replace all URLs in the given nodes
        nodesURl.forEach((textNode: ChildNode) => {
          const newTags: Array<{
            url: string;
            range: number[];
          }> = [];

          const textContent = textNode.textContent || '';

          if (textContent) {
            let match: any;
            // Reset the regex's lastIndex before each execution
            detectURLRegex.lastIndex = 0;

            // eslint-disable-next-line no-cond-assign
            while ((match = detectURLRegex.exec(textContent)) !== null) {
              const url: string = match[0];
              const start: number = match.index;
              const end: number = start + url.length;

              const newTag = {
                url,
                range: [start, end],
              };

              // add new valid URL to tags
              newTags.push(newTag);
            }

            if (newTags.length > 0) {
              // Sort tags by their position in reverse order
              // starting from the back avoids messing up earlier parts of the document
              newTags.sort((a, b) => b.range[0] - a.range[0]);

              // Add tags one by one, adjusting the selection for each
              newTags.forEach(tag => {
                const [start, end] = tag.range;

                if (tagifyRef.current) {
                  const isUpdated = updateWindowSelection(textNode, start, end);

                  if (isUpdated) {
                    const newTag = tagifyRef.current.createTagElem({
                      value: tag.url,
                      label: tag.url,
                      type: DETECT_LINK,
                    });

                    // Inject the new detect link tag
                    tagifyRef.current.injectAtCaret(newTag);

                    // Update flag to refresh tooltip mount
                    atLeastOneInjectionFlag = true;
                  }
                }
              });
            }
          }
        });

        // Update flag to refresh tooltip mouse event
        if (atLeastOneInjectionFlag) {
          setTooltipRefresher(prev => prev + 1);
        }

        const selection = window.getSelection();
        if (selection) {
          if (lastRange instanceof Range) {
            // Need to restore the selection after execution all process to replace URL to Tag
            // to maintain valid caret
            selection.removeAllRanges();
            selection.addRange(lastRange);
          } else {
            // In case lost the selection not need to restore
            // Reset the selection to clear and prepare the valid caret to add the next tag at fn: "onAddNewTag"
            selection?.removeAllRanges();

            onSyncSelectionStateTagify();
          }
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Error while replacing URL with tag', error);
      }
    },
    [onSyncSelectionStateTagify, updateWindowSelection],
  );

  /**
   * Detects URLs within the text nodes of a specified input element and replaces them with tags.
   *
   * This function scans through all child nodes of the input element, looking for text nodes that contain URLs.
   * When URLs are found, it creates tags for each URL and injects them into the input element, updating the selection
   * as necessary to ensure the DOM structure remains intact.
   * */
  const detectReplaceURLToTag = useCallback(() => {
    const isSupportShortlink = acceptablePatternChecking(SHORT_LINK_PTN, acceptableTagPattern);

    if (!tagifyRef.current || !isSupportShortlink) return;

    try {
      const { input: inputElement } = tagifyRef.current.DOM;

      if (inputElement) {
        const selection = window.getSelection();
        const cacheLastRange =
          selection?.getRangeAt && selection?.rangeCount && selection?.getRangeAt(0);

        const minLengthValidURL = 8; // -> Because of valid URL should be "https://"

        // Get all child nodes that are of type TEXT_NODE and contain an URL
        const nodesURL = findURLInTextNodes(inputElement, minLengthValidURL);

        // Starting from the back to avoid messing up earlier parts of the DOM
        const reversedNodes = (nodesURL as any)?.toReversed() || [];

        // Process URL to Tag
        if (reversedNodes && reversedNodes.length > 0) {
          replaceURLsWithTags(reversedNodes, cacheLastRange);
        }
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }, [acceptableTagPattern, replaceURLsWithTags]);

  const placeCaretAfterNode = useCallback((node: HTMLElement) => {
    if (!tagifyRef.current) {
      throw new Error('Tagify instance is not initialized');
    }
    const selection = window.getSelection();

    // Only place the caret if there is a range selection
    if (selection?.rangeCount) {
      tagifyRef.current.placeCaretAfterNode(node);
    } else {
      // eslint-disable-next-line no-console
      console.warn('Cannot place caret after node', { selection, instance: tagifyRef.current });
    }
  }, []);

  const onOutputData = useCallback(
    (newValue: string) => {
      if (escapeHTML) {
        const unescapedValue = unescapeString(newValue);
        onChange(unescapedValue);
      } else {
        onChange(newValue);
      }
    },
    [escapeHTML, onChange],
  );

  // Expose some methods
  useImperativeHandle(
    ref,
    () => ({
      onAddNewTag: (newTag: TagDataCustomize | string) => {
        if (newTag && tagifyRef.current) {
          const { settings, value: currentTags } = tagifyRef.current;
          const { readonly, maxTags } = settings;
          const currentPersonalizeTags = currentTags.filter(
            tag => !([EMOJI, SHORT_LINK, SHORT_LINK_V2] as string[]).includes(tag.type),
          );
          const currentPersonalizeTagsLength = currentPersonalizeTags.length;

          // Check readonly
          if (readonly) return;

          // For case add new common emoji
          if (typeof newTag === 'string') {
            onInjectTagAtCaret(newTag, true);
          } else {
            // In case add new common tag
            const { type } = newTag;

            /*
             * Validate the max limit before add new tag
             * For emoji and short link, it will be passed
             */
            if (
              !([EMOJI, SHORT_LINK, SHORT_LINK_V2] as string[]).includes(type) &&
              currentPersonalizeTagsLength >= maxTags
            )
              return;

            // Create new tag
            const newTagEle = tagifyRef.current.createTagElem(newTag);

            // Add new tag
            onInjectTagAtCaret(newTagEle);

            // NOTE: place to refactor if not need to add space after the tag
            const spaceEle = tagifyRef.current.insertAfterTag(newTagEle, ' '); //  <- adds space after the tag
            placeCaretAfterNode(spaceEle); // <- place the caret in the space after the tag
          }
        } else {
          throw new Error('Tagify instance is not initialized');
        }
      },
      onReplaceTag: (currentTagEle: HTMLElement, newTag: TagDataCustomize) => {
        if (tagifyRef.current && currentTagEle && newTag) {
          const { settings } = tagifyRef.current;
          const { readonly } = settings;

          // Check readonly
          if (readonly) return;

          const spaceEle = tagifyRef.current.insertAfterTag(currentTagEle, ' '); //  <- adds space after the tag to keep valid caret positioning
          tagifyRef.current.replaceTag(currentTagEle, newTag);

          placeCaretAfterNode(spaceEle); // <- restore the caret after the edited tag
        } else {
          throw new Error('Tagify instance is not initialized');
        }
      },
      onReload: (newValue: string) => {
        if (!tagifyRef.current) {
          throw new Error('Tagify instance is not initialized');
        }
        if (!_.isString(newValue)) {
          throw new Error('Reload value should be string');
        }
        if (newValue === '') {
          tagifyRef.current.loadOriginalValues(newValue);

          return;
        }

        if (!escapeHTML) {
          const newContent = parseTagStringToTagify(newValue, acceptableTagPattern);

          tagifyRef.current.loadOriginalValues(newContent);
        } else {
          // Sometimes the input value has html tags, it makes the input rendered incorrectly -> it rendered html
          // -> need to escape the html tags to render correctly
          const escapeContent = htmlEntities.escape(newValue);
          const newContent = parseTagStringToTagify(escapeContent, acceptableTagPattern);

          tagifyRef.current.loadOriginalValues(newContent);
        }
      },
    }),
    [acceptableTagPattern, escapeHTML, onInjectTagAtCaret, placeCaretAfterNode],
  );

  const onTagItemClick = useCallback(
    (event: CustomEvent<Tagify.ClickEventData<TagDataCustomize>>) => {
      event.stopPropagation();
      event.preventDefault();

      if (event.detail && onTagClick) {
        const { tagify, tag, data } = event.detail;

        const clickable = isTagClickable(tagify, tag, data);

        // Prevent to click on tag if readonly
        if (!clickable) return;

        onTagClick(event.detail);
      }
    },
    [onTagClick],
  );

  // Used to trigger replace URL detection and line break
  const onTagifyTyping = useCallback(
    (event: CustomEvent<Tagify.InputEventData<TagDataCustomize>>) => {
      if (event.detail) {
        const { textContent, tagify } = event.detail as any;

        // Tracking if the tagify input is line break
        setIsLineBreak(hasLineBreak(textContent));

        // Update URL to tag to show hint in the tooltip with tag
        if (textContent) {
          detectReplaceURLToTag();
        }

        if (realtime) {
          const inputValue = tagify.getInputValue();
          const convertedValue = convertInputStringToOriginal(inputValue);

          onOutputData(convertedValue);
        }
      }
    },
    [realtime, detectReplaceURLToTag, onOutputData],
  );
  const onInputTagifyDebounce = useCallback(_.partial(onInputDebounceCallback, onTagifyTyping), [
    onInputDebounceCallback,
    onTagifyTyping,
  ]);

  /**
   * Capture to trigger when tagify changed
   */
  const onTagifyChanged = useCallback(
    (event: CustomEvent<Tagify.ChangeEventData<TagDataCustomize>>) => {
      if (event.detail) {
        const { tagify } = event.detail;

        const inputValue = tagify.getInputValue();
        const convertedValue = convertInputStringToOriginal(inputValue);

        if (convertedValue === '') {
          setIsLineBreak(false);
        }

        onOutputData(convertedValue);
      }
    },
    [onOutputData],
  );
  const onTagifyChangedDebounce = useCallback(
    _.partial(onChangeDebounceCallback, onTagifyChanged),
    [onTagifyChanged, onChangeDebounceCallback],
  );

  const onKeyDown = useCallback((e: CustomEvent<Tagify.KeydownEventData<TagDataCustomize>>) => {
    switch (e.detail.event.key) {
      case 'Enter': {
        const { range } = getCurrentSelectionAndCloneRange();
        if (!range || e.detail.event.shiftKey || !isCaretAtEndOfTextNodeWithNextTag(range)) break;

        handleEnterWithNextTag(range);
        break;
      }
      case 'Backspace': {
        const { range } = getCurrentSelectionAndCloneRange();
        if (!range || e.detail.event.shiftKey || !isCaretAtEndOfTextNodeWithNextTag(range)) break;

        handleTextNodeBackspace(range);
        break;
      }
      default: {
        break;
      }
    }
  }, []);

  const customizeTag = useCallback(
    (tagData: TagDataCustomize, tagify: Tagify<TagDataCustomize>) => {
      const { value, collection, label, type } = tagData;
      const { settings } = tagify;
      const { tagTextProp } = settings;

      let closeIcon = `<x title='' class="${settings.classNames.tagX}" role='button' aria-label='remove tag'></x>`;
      const content = tagData[tagTextProp] || tagData.value;
      let visibleTagContent = `<span class="${settings.classNames.tagText}">${content}</span>`;

      const additionalAttributes: Map<string, boolean | string | number> = new Map();

      switch (type) {
        case EMOJI: {
          closeIcon = '';

          const emojiPath = emojiManufacturer(value, collection);
          visibleTagContent = getEmojiTag({ src: emojiPath as any, emoji: label, code: value });

          break;
        }
        case DETECT_LINK: {
          const hint = 'Click to the link to convert it to a short link';

          additionalAttributes.set(MESSAGE_TAG, hint).set(FORCE_SHOW_TOOLTIP, 'true');

          closeIcon = '';
          visibleTagContent = `<span class="${settings.classNames.tagText}">${content}</span>`;
          break;
        }
        default: {
          break;
        }
      }

      const additionalTagAttrs = getAttributesString(additionalAttributes);

      return `
        <tag
          contenteditable='false'
          spellcheck='false'
          tabIndex="${settings.a11y.focusableTags ? 0 : -1}"
          class="${settings.classNames.tag} ${tagData.class ? tagData.class : ''}"
          ${tagify.getAttributes(tagData)}
          ${additionalTagAttrs}
        >
          ${closeIcon}

          ${generateTagContent({
            tagData,
            content: visibleTagContent,
          })}
        </tag>`;
    },
    [],
  );

  /*
   * Any map attribute change will sync label of the tags
   */
  const makeValidLabelTags = useCallback(
    (attributes: MapAttributesProps, errorAttributes?: MapAttributesProps) => {
      if (tagifyRef.current) {
        const tagElements = tagifyRef.current.getTagElms();
        const {
          pattern,
          name: cachePatternName,
          acceptablePattern: acceptableType,
        } = patternHandlers[PERSONALIZE_PTN];

        tagElements.forEach(tagElement => {
          const { __tagifyTagData: tagData } = tagElement as any;

          if (tagData) {
            const { type, value } = tagData;

            const isPersonalTag = isPersonalizeTagType(type);

            const isShortLinkTag = isShortLinkTagType(type);

            /** Case Personalize */
            if (isPersonalTag && value) {
              const isAccepted = acceptablePatternChecking(acceptableType, acceptableTagPattern);

              // No need to continue if pattern is not accepted
              if (!isAccepted) return;

              // Use the cached regex instead of creating a new one each time
              const regex = getCachedRegex(pattern, 'g', cachePatternName);
              let match: RegExpExecArray | null;

              // Iterate over matches of the current pattern
              // eslint-disable-next-line no-cond-assign
              while ((match = regex.exec(value)) !== null) {
                const [, personalizeContent] = match;
                const [tagCode] = personalizeContent.split('||');

                const {
                  label: tagLabel,
                  isValid,
                  message,
                  type,
                  isRemoved,
                  hasViewPermission,
                } = getPersonalizeTagInfo(tagCode, attributes, errorAttributes);

                const isPromotionCode = type === PROMOTION_CODE;

                const tagTextNode = tagifyRef.current?.getTagTextNode(tagElement);
                /*
                 * Just only update to the correct text of the tag
                 * NOTE: Do not actually affect raw data
                 */
                if (tagTextNode) {
                  tagTextNode.textContent = tagLabel;

                  // Clear all previous attributes
                  tagElement.removeAttribute(REMOVED_TAG);
                  tagElement.removeAttribute(NO_VIEW_TAG);
                  tagElement.removeAttribute(INVALID_TAG);
                  tagElement.removeAttribute(READONLY_TAG);
                  tagElement.removeAttribute(MESSAGE_TAG);

                  // In case promotion pool has something wrong
                  if (isPromotionCode && message) {
                    if (isRemoved) {
                      tagElement.setAttribute(REMOVED_TAG, 'true');
                      tagElement.setAttribute(MESSAGE_TAG, message);

                      tagifyRef.current?.getSetTagData(tagElement, {
                        ...tagData,
                        status: 'error',
                        statusMsg: message,
                      });
                    } else if (!hasViewPermission) {
                      tagElement.setAttribute(NO_VIEW_TAG, 'true');
                      tagElement.setAttribute(MESSAGE_TAG, message);

                      tagifyRef.current?.getSetTagData(tagElement, {
                        ...tagData,
                        status: 'warning',
                        statusMsg: message,
                      });
                    } else if (!isValid) {
                      tagElement.setAttribute(READONLY_TAG, 'true');
                      tagElement.setAttribute(INVALID_TAG, 'true');
                      tagElement.setAttribute(MESSAGE_TAG, message);

                      tagifyRef.current?.getSetTagData(tagElement, {
                        ...tagData,
                        status: 'warning',
                        statusMsg: message,
                      });
                    }
                  }
                }
              }
            }

            /** Case Short Link V2 Tag */
            if (isShortLinkTag) {
              const isAccepted = acceptablePatternChecking(SHORT_LINK_PTN, acceptableTagPattern);

              if (!isAccepted) return;

              const { url, shortener, label } = tagData;

              const {
                label: tagLabel,
                isValid,
                message,
                type: tagType,
              } = getShortLinkTagInfo({
                type,
                label,
                shortener,
                url,
                mapAttributes: attributes,
                mapErrorAttributes: errorAttributes,
              });

              const tagTextNode = tagifyRef.current?.getTagTextNode(tagElement);

              /*
               * Just only update to the correct text of the tag
               * NOTE: Do not actually affect raw data
               */
              if (tagTextNode) {
                tagTextNode.textContent = tagLabel;

                // Clear all previous attributes
                tagElement.removeAttribute(REMOVED_TAG);
                tagElement.removeAttribute(NO_VIEW_TAG);
                tagElement.removeAttribute(INVALID_TAG);
                tagElement.removeAttribute(READONLY_TAG);
                tagElement.removeAttribute(MESSAGE_TAG);

                // In case promotion pool has something wrong
                if (!isValid && message) {
                  tagElement.setAttribute(REMOVED_TAG, 'true');
                  tagElement.setAttribute(MESSAGE_TAG, message);

                  tagifyRef.current?.getSetTagData(tagElement, {
                    ...tagData,
                    status: 'error',
                    statusMsg: message,
                  });
                }
              }
            }
          }
        });

        setTooltipRefresher(prev => prev + 1);
      }
    },
    [acceptableTagPattern],
  );

  const initializeTagify = useCallback(() => {
    if (inputRef.current && !tagifyRef.current) {
      tagifyRef.current = new Tagify<TagDataCustomize>(inputRef.current, {
        templates: {
          tag: customizeTag,
        },
        mode: 'mix',
        // placeholder: 'Enter your text...',
        mixMode: {
          insertAfterTag: '', // Not insert anything after tag
        },
        tagTextProp: 'label', // Use `tagify.label` to display the tags
        enforceWhitelist: false, // Ensure that tags not in the whitelist can be added
        duplicates: true, // Allow duplicate tags
        editTags: false, // Not allow tag editing
        dropdown: {
          enabled: false, // Do not display the dropdown
        },
        pattern: /^$a/, // -> using this regex never match any character to "prevent" add new tag when press enter
        hooks: {
          beforeKeyDown: (
            event: KeyboardEvent,
            data: Tagify.BeforeKeyDownData<TagDataCustomize>,
          ): Promise<void> => {
            /*
             * Implementation for preventing to add more than maximum limit of characters
             */
            const tagifyValue = _.get(data, 'tagify.DOM.originalInput.tagifyValue', '');

            // TODO: need to check
            if (tagifyValue && maxLength) {
              const allowedKeys = [
                'Backspace',
                'Delete',
                'ArrowUp',
                'ArrowDown',
                'ArrowLeft',
                'ArrowRight',
              ];

              const convertedValue = convertInputStringToOriginal(tagifyValue);

              if (!allowedKeys.includes(event.key) && convertedValue.length > maxLength) {
                event.preventDefault();
                return Promise.reject();
              }
            }

            return Promise.resolve();
          },
        },
      });
    }
  }, [maxLength, customizeTag]);

  const onTagifyWrapperClick: MouseEventHandler<HTMLDivElement> = useCallback(event => {
    event.preventDefault();
    event.stopPropagation();

    if (tagifyRef.current && event.target === tagifyWrapperRef.current) {
      const { DOM, settings } = tagifyRef.current;
      const { readonly } = settings;

      // not focus if readonly
      if (readonly) return;

      const { classList } = DOM?.scope;
      const { classNames } = settings;

      if (!classList.contains(classNames.focus) && DOM?.input) {
        try {
          const { lastChild } = DOM?.input;
          const isValidLastBRTag = lastChild && lastChild.nodeName === 'BR';

          const hasEmptyCls = classList.contains(classNames.empty);
          const isEmptyChildNodes = DOM?.input.childNodes.length === 0;
          const isEmptyInputData = _.isNull(lastChild) && hasEmptyCls && isEmptyChildNodes;

          // Create a new range
          const range = document.createRange();
          const selection = window.getSelection();

          // Move the selection to before of the last BR
          if (isValidLastBRTag) {
            // Move the range to the end of the input's content
            range.setStartBefore(lastChild);
            range.collapse(true);

            // Remove any existing selection and add the new range
            selection?.removeAllRanges();
            selection?.addRange(range);
            DOM.input.focus();
          } else if (isEmptyInputData) {
            // Move the range to the end of the input's content
            range.selectNodeContents(DOM.input);
            range.collapse(false);

            selection?.removeAllRanges();
            selection?.addRange(range);
            DOM.input.focus();
          }
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error(error);
        }
      }
    }
  }, []);

  // Initialization tagify
  useLayoutEffect(() => {
    initializeTagify();

    return () => {
      if (tagifyRef.current) {
        tagifyRef.current.destroy();
      }
    };
  }, [initializeTagify]);

  // Settings some tagify attributes
  // Set [readonly, disabled, placeholder]
  useEffect(() => {
    if (!tagifyRef.current) return;

    if (typeof readonly !== 'undefined') {
      tagifyRef.current.setReadonly(!!readonly);
    }
    if (typeof disabled !== 'undefined') {
      tagifyRef.current.setDisabled(!!disabled);
    }
    if (_.isString(placeholder)) {
      tagifyRef.current.setPlaceholder(placeholder);
    }
  }, [disabled, placeholder, readonly]);

  // Set readonly for each tag
  useEffect(() => {
    if (tagifyRef.current && typeof readonlyTag !== 'undefined') {
      const tagElementList = tagifyRef.current.getTagElms();

      tagElementList.forEach((tagElement: HTMLElement) => {
        const tagType = _.get(tagElement, '__tagifyTagData.type', '') as TagType;

        const isPersonalizeTag = isPersonalizeTagType(tagType);

        // Only support readonly for personalize tag
        if (!isPersonalizeTag) return;

        // Caution: Don't use readonly attribute -> readonly attribute Tagify library managed
        if (readonlyTag) {
          tagElement.setAttribute(READONLY_TAG, readonlyTag.toString());
        } else {
          // Only remove readonly-tag attribute if the invalid attribute is not existing
          if (!tagElement.hasAttribute(INVALID_TAG)) {
            tagElement.removeAttribute(READONLY_TAG);
          }
        }
      });
    }
  }, [readonlyTag]);

  // Set max personalize tags
  useLayoutEffect(() => {
    if (tagifyRef.current && typeof maxPersonalizeTags !== 'undefined') {
      tagifyRef.current.settings.maxTags = maxPersonalizeTags;
    }
  }, [maxPersonalizeTags]);

  /*
   * Need to sync label of the tags if any map attribute is changed to make correct label
   * */
  useDeepCompareEffect(() => {
    if (!_.isEmpty(mapAttributes)) {
      makeValidLabelTags(mapAttributes, mapErrorAttributes);
    }
  }, [mapAttributes, mapErrorAttributes, makeValidLabelTags]);

  // Listen to Tagify events
  useEffect(() => {
    const { current: tagifyInstance } = tagifyRef || {};

    if (tagifyInstance) {
      tagifyInstance.on('click', onTagItemClick);
      tagifyInstance.on('input', onInputTagifyDebounce);
      tagifyInstance.on('change', onTagifyChangedDebounce);
      tagifyInstance.on('keydown', onKeyDown);
    }

    // Off listen to Tagify events
    return () => {
      if (tagifyInstance) {
        tagifyInstance.off('click', onTagItemClick);
        tagifyInstance.off('input', onInputTagifyDebounce);
        tagifyInstance.off('change', onTagifyChangedDebounce);
        tagifyInstance.off('keydown', onKeyDown);
      }
    };
  }, [onTagItemClick, onInputTagifyDebounce, onTagifyChangedDebounce, onKeyDown]);

  // At the first render, need to update URL to tag to show hint in the tooltip with tag
  useEffect(() => {
    detectReplaceURLToTag();
  }, [detectReplaceURLToTag]);

  // CAVEAT: Some case need to "FORCE" sync the original values to Tagify instance
  useDeepCompareEffect(() => {
    if (tagifyRef.current) {
      const { DOM, settings } = tagifyRef.current;
      const { empty } = settings.classNames;
      const { classList } = DOM?.scope;

      // If input is empty, load the original values
      if (classList.contains(empty) && _.isString(initialValue) && initialValue !== '') {
        let textValue = _.cloneDeep(initialValue);

        if (escapeHTML) {
          // Sometimes the input value has html tags, it makes the input rendered incorrectly -> it rendered html
          // -> need to escape the html tags to render correctly
          textValue = htmlEntities.escape(textValue);
        }

        // Convert the string to tagify pattern
        const content = parseTagStringToTagify(textValue, acceptableTagPattern);

        tagifyRef.current.loadOriginalValues(content);

        // Need to sync label of the tags if any map attribute is changed to make correct label
        if (!_.isEmpty(mapAttributes)) {
          makeValidLabelTags(mapAttributes, mapErrorAttributes);
        }
      }
    }
  }, [
    initialValue,
    mapAttributes,
    mapErrorAttributes,
    escapeHTML,
    acceptableTagPattern,
    makeValidLabelTags,
  ]);

  useEffect(() => {
    if (tagifyRef.current && initialValue === '') {
      const { DOM, settings } = tagifyRef.current;
      const { empty } = settings.classNames;
      const { classList } = DOM?.scope;

      if (!classList.contains(empty)) {
        // Removes all tags and resets the original input tag's value property if the initial value back to is empty string
        tagifyRef.current.removeAllTags();
      }
    }
  }, [initialValue]);

  useEffect(() => {
    const tagifyTooltip = document.querySelector('.tagify__tooltip');
    if (!tagifyTooltip) {
      const style = document.createElement('style');
      style.type = 'text/css';

      style.appendChild(
        document.createTextNode(`
          .tagify__tooltip {
              display: none;
              position: fixed;
              background-color: #595959;
              border-radius: 10px;
              color: #ffffff;
              padding: 5px;
              z-index: 10000
          }
          .tagify__tooltip_content {
            width: 100%;
            height: 100%;
            position: relative;
            transition: 0s;
            font-size: 12px;
          }
          .tagify__tooltip_content:before {
            content: '';
            position: absolute;
            top: 100%;
            right: 50%;
            left: auto;
            background-color: #595959;
            width: 10px;
            height: 10px;
            transform: rotate(45deg) translate(50%,-50%);
            border-radius: 1px;
          }
        `),
      );
      document.head.appendChild(style);

      const tooltipWrapper = document.createElement('div');
      tooltipWrapper.classList.add('tagify__tooltip');

      const tooltipContent = document.createElement('div');
      tooltipContent.classList.add('tagify__tooltip_content');
      tooltipContent.innerText = 'This pool is removed';

      tooltipWrapper.appendChild(tooltipContent);

      document.body.appendChild(tooltipWrapper);
    }
  }, []);

  useDeepCompareEffect(() => {
    const showTooltip = (tagElement: HTMLElement) => {
      const tagRect = tagElement.getBoundingClientRect();
      const removeTag = tagElement.getAttribute(REMOVED_TAG);
      const noViewTag = tagElement.getAttribute(NO_VIEW_TAG);
      const invalidTag = tagElement.getAttribute(INVALID_TAG);
      const messageTag = tagElement.getAttribute(MESSAGE_TAG);
      const forceShowTooltipAttr = tagElement.getAttribute(FORCE_SHOW_TOOLTIP);

      if (messageTag) {
        const tooltipContent = document.querySelector('.tagify__tooltip_content');
        if (tooltipContent) {
          tooltipContent.innerHTML = messageTag;
        }
      }

      const tooltip = document.querySelector('.tagify__tooltip');
      tooltip?.setAttribute('style', 'display: block !important;visibility: hidden !important;');
      const tooltipRect = tooltip?.getBoundingClientRect();

      const hasWrongAttribute =
        removeTag === 'true' || noViewTag === 'true' || invalidTag === 'true';

      const isTooltipVisible = tooltipRect && hasWrongAttribute;
      const isForceVisible = tooltipRect && forceShowTooltipAttr === 'true';

      if (isTooltipVisible || isForceVisible) {
        if (tooltipRect?.width > tagRect.width) {
          tooltip?.setAttribute(
            'style',
            `visibility: visible !important;display: block !important;top:${tagRect.y - tagRect.height - 10}px;left:${tagRect.x - (tooltipRect?.width - tagRect.width) / 2}px;`,
          );
        } else {
          tooltip?.setAttribute(
            'style',
            `visibility: visible !important;display: block !important;top:${tagRect.y - tagRect.height - 10}px;left:${tagRect.x + (tagRect?.width - tooltipRect.width) / 2}px;`,
          );
        }
      }
    };

    const hideTooltip = () => {
      const tooltip = document.querySelector('.tagify__tooltip');
      tooltip?.setAttribute('style', `display: none !important;visibility: hidden !important;`);
    };

    if (tagifyRef.current && tooltipRefresher) {
      const tagElements = tagifyRef.current.getTagElms();
      tagElements.forEach(tagElement => {
        tagElement.addEventListener('mouseenter', () => showTooltip(tagElement));
        tagElement.addEventListener('mouseleave', () => hideTooltip());
      });
    }

    return () => {
      if (tagifyRef.current && tooltipRefresher) {
        const tagElements = tagifyRef.current.getTagElms();
        tagElements.forEach(tagElement => {
          tagElement.removeEventListener('mouseenter', () => showTooltip(tagElement));
          tagElement.removeEventListener('mouseleave', () => hideTooltip());
        });
      }
    };
  }, [tooltipRefresher]);

  return (
    <TagifyWrapper
      ref={tagifyWrapperRef}
      $cssTagifyVariables={cssVariablesMemoized}
      className="tagify-container"
      id="tagify-container"
      data-test="tagify-wrapper"
      $status={status}
      $maxHeight={maxHeight}
      $minWidth={minWidth}
      $minWidthPlaceholder={minWidthPlaceholder}
      $tagifyFullWidth={isLineBreak}
      $placeholder={placeholder}
      $isSingleLineText={isSingleLineText}
      $tagInvalidIcon={iconWarning}
      $tagErrorIcon={iconError}
      onClick={onTagifyWrapperClick}
    >
      <TagTextArea
        id="tagify-textarea"
        ref={inputRef}
        name={name}
        defaultValue={parsedDefaultValue}
        data-test="tagify-input"
      />
      <WrapperPlaceHolder data-test="wrapper-placeholder" ref={placeholderRef} $isShow={!!children}>
        {children}
      </WrapperPlaceHolder>
    </TagifyWrapper>
  );
});

TagifyInput.defaultProps = tagifyDefaultProps;
export default memo(TagifyInput);
