// Constants
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

// Types
import { TagifyInputProps } from './types';

export const DIMENSIONS = {
  MIN_H_WRAPPER: 34,
  TAG_H: 24,
} as const;

export const { MIN_H_WRAPPER, TAG_H } = DIMENSIONS;

export const TAG_CUSTOM_ATTRIBUTES = {
  READONLY_TAG: 'readonly-tag',
  INVALID_TAG: 'invalid-tag',
  REMOVED_TAG: 'removed-tag',
  NO_VIEW_TAG: 'no-view-tag',
  MESSAGE_TAG: 'message-tag',
  FORCE_SHOW_TOOLTIP: 'force-show-tooltip',
} as const;

export const {
  READONLY_TAG,
  INVALID_TAG,
  REMOVED_TAG,
  NO_VIEW_TAG,
  MESSAGE_TAG,
  FORCE_SHOW_TOOLTIP,
} = TAG_CUSTOM_ATTRIBUTES;

export const defaultCssVariables = {
  '--input-color': globalToken?.colorText,
  '--input-font-size': `${globalToken?.fontSize}px`,
  '--tag--max-width': '170px',
  '--tag-border-radius': '999px',
  '--tag-bg': 'transparent',
  '--tag-hover': 'transparent',
  '--tags-hover-border-color': 'transparent',
  '--tags-focus-border-color': 'transparent',
  '--tags-border-color': 'transparent',
  '--tag-remove-bg': 'transparent',
  '--tag-remove-btn-color': globalToken?.colorPrimary,
  '--tag-remove-btn-bg': globalToken?.bw0,
  '--tag-remove-btn-bg--hover': globalToken?.bw0,
  '--tag-text-color': globalToken?.colorText,
};

export const PATTERN_CACHE_TYPE = {
  SHORT_LINK_PTN: 'shortlink_pattern',
  SHORT_LINK_INDIVIDUAL_PTN: 'shortlink_individual_pattern',
  SHORT_LINK_GENERAL_PTN: 'shortlink_general_pattern',
  SHORT_LINK_V2_INDIVIDUAL_PTN: 'shortlink_v2_individual_pattern',
  SHORT_LINK_V2_GENERAL_PTN: 'shortlink_v2_general_pattern',
  LINE_EMOJI_PTN: 'line_emoji_pattern',
  PERSONALIZE_PTN: 'personalize_pattern',
  VIBER_EMOJI_PTN: 'viber_emoji_pattern',
} as const;

export const {
  SHORT_LINK_PTN,
  SHORT_LINK_INDIVIDUAL_PTN,
  SHORT_LINK_GENERAL_PTN,
  SHORT_LINK_V2_INDIVIDUAL_PTN,
  SHORT_LINK_V2_GENERAL_PTN,
  LINE_EMOJI_PTN,
  PERSONALIZE_PTN,
  VIBER_EMOJI_PTN,
} = PATTERN_CACHE_TYPE;

export const DEFAULT_ACCEPT_TAGS = [
  SHORT_LINK_PTN,
  PERSONALIZE_PTN,
  VIBER_EMOJI_PTN,
  LINE_EMOJI_PTN,
];

export const tagifyDefaultProps: TagifyInputProps = {
  initialValue: '',
  name: 'tagifyInput',
  realtime: true,
  maxHeight: 266,
  placeholder: undefined,
  minWidthPlaceholder: 130,
  acceptableTagPattern: DEFAULT_ACCEPT_TAGS,
  onChange: () => {},
};

export const TAG_TYPE = {
  CUSTOMER: 'customer',
  VISITOR: 'visitor',
  EVENT: 'event',
  JOURNEY: 'story',
  CAMPAIGN: 'campaign',
  VARIANT: 'variant',
  OBJECT_WIDGET: 'objectWidget',
  PROMOTION_CODE: 'promotion_code',
  CUSTOM_FN: 'custom',
  EMOJI: 'emoji',
  SHORT_LINK: 'shortlink',
  SHORT_LINK_V2: 'shortlink_v2',
  DETECT_LINK: 'detect_link',
  CONTENT_SOURCE_GROUP: 'groups',
} as const;

export const {
  CUSTOMER,
  VISITOR,
  EVENT,
  JOURNEY,
  CAMPAIGN,
  VARIANT,
  PROMOTION_CODE,
  CUSTOM_FN,
  EMOJI,
  DETECT_LINK,
  SHORT_LINK,
  SHORT_LINK_V2,
  OBJECT_WIDGET,
  CONTENT_SOURCE_GROUP,
} = TAG_TYPE;

export const SHORT_LINK_TYPE = {
  INDIVIDUAL: 'shortlink', // Individual link
  GENERAL: 'shortlink_static', // General link
} as const;
export const SHORT_LINK_TYPE_V2 = {
  INDIVIDUAL: 'shortlink_v2', // Individual link
  GENERAL: 'shortlink_v2_static', // General link
} as const;

export const TAG_COLOR = {
  [CUSTOMER]: globalToken?.blue2,
  [DETECT_LINK]: '#e8feca',
  [SHORT_LINK]: {
    [SHORT_LINK_TYPE.INDIVIDUAL]: '#CAFEDD',
    [SHORT_LINK_TYPE.GENERAL]: '#EFE2D9',
  },
  [SHORT_LINK_V2]: {
    [SHORT_LINK_TYPE.INDIVIDUAL]: '#CAFEDD',
    [SHORT_LINK_TYPE.GENERAL]: '#EFE2D9',
  },
  [VISITOR]: '#e8feca',
  [EVENT]: '#fecaca',
  [JOURNEY]: '#FAFAAF',
  [CAMPAIGN]: '#FBCBE8',
  [VARIANT]: '#FFD8DB',
  [PROMOTION_CODE]: '#d8cafe',
  [OBJECT_WIDGET]: '#cafedd',
  [CUSTOM_FN]: '#bbefbe',
  [CONTENT_SOURCE_GROUP]: '#ffdd9f',
  [EMOJI]: 'transparent',
} as const;

export const EMOJI_COLLECTIONS = {
  COMMON: 'common',
  VIBER: 'viber',
  LINE: 'line',
} as const;

export const PREFIX_PATTERN_LINE_MESSAGE = 'line';

export const DATA_ACCESS_OBJECT = {
  DASHBOARD: 1,
  JOURNEY_OVERVIEW: 2,
  SEGMENT: 3,
  BO: 4,
  VIEW: 5,
  DESTINATION: 6,
  PROMOTION_POOL: 14,
};
