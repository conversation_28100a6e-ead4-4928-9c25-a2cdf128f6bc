// Libraries
import Tagify, { ClassNameSettings, TagData, TagifySettings } from '@yaireo/tagify';

// Constants
import { EMOJI_COLLECTIONS, PATTERN_CACHE_TYPE, SHORT_LINK_TYPE, TAG_TYPE } from './constants';

// Types
import { type CSSProperties } from 'react';

export type MapAttributesProps = Record<string, Record<string, any>>;

export type StatusType = 'error' | 'success' | 'warning';

/**
 * Interface defining the properties for the Tagify input component.
 * Provides configuration options and event handlers to manage Tagify's behavior and appearance.
 */
export interface TagifyInputProps {
  /**
   * Initial value for the Tagify input field.
   * Only used when the component is first rendered.
   *
   * @default ''
   */
  initialValue: string;

  /**
   * Defines whether the input should escape HTML characters.
   */
  escapeHTML?: boolean;

  /**
   * Defines whether the input should be in *realtime* mode.
   * In *realtime* mode, the input is updated as the user stops typing, otherwise only on blur types.
   *
   * @default true
   */
  realtime?: boolean;

  /**
   * Optional mapping configuration for custom attributes associated with tags.
   * Defines how tag attributes are mapped and processed internally.
   */
  mapAttributes?: MapAttributesProps;

  mapErrorAttributes?: MapAttributesProps;

  /**
   * Optional name attribute for the Tagify input field, useful for integration with forms.
   * Helps link the input with form submissions and data management.
   *
   * @default 'tagifyInput'
   */
  name?: string;

  /**
   * Allows customization of CSS class names applied to different parts of the Tagify component.
   * Useful for applying custom styling or theming to the Tagify input.
   */
  classNames?: ClassNameSettings;

  /**
   * Defines the placeholder text shown when the input is empty.
   *
   * @default undefined
   */
  placeholder?: TagifySettings['placeholder'];

  /**
   * Defines whether the input is readonly.
   */
  readonly?: TagifySettings['readonly'];

  /**
   * Defines whether the input tags is read only not whole input.
   */
  readonlyTag?: boolean;

  /**
   * Defines whether the input is disabled.
   */
  disabled?: boolean;

  /**
   * Defines the maximum length of the input value.
   */
  maxLength?: number;

  /**
   * Specifies the maximum number of tags that can be added to the input.
   * Helps enforce constraints by limiting the number of tags a user can input.
   */
  maxPersonalizeTags?: TagifySettings['maxTags'];

  /**
   * Defines the maximum height of the input.
   *
   * @default 266
   */
  maxHeight?: number;

  /**
   * Defines the minimum width of the input.
   */
  minWidth?: CSSProperties['minWidth'];

  /**
   * Defines the minimum width of the placeholder.
   * Used for the placeholder when the input is empty.
   *
   * @default 130px
   */
  minWidthPlaceholder?: CSSProperties['minWidth'];

  /**
   * Defines the status of the input, used for styling and validation.
   */
  status?: StatusType;

  /**
   * Defines whether the input should be rendered as a single line of text.
   */
  isSingleLineText?: boolean;

  /**
   * Defines acceptable patterns or validation rules for tags.
   * Ensures only tags matching specified patterns can be added, enforcing content rules.
   *
   * @default DEFAULT_ACCEPT_TAGS
   */
  acceptableTagPattern: Array<AcceptablePattern>;

  /**
   * Optional CSS variables specific to Tagify for customizing styles dynamically.
   * Allows the user to set custom styles directly through CSS variables, enhancing flexibility.
   */
  cssTagifyVariables?: Record<string, string | undefined>;

  /**
   * React children, allowing for additional elements or components to be passed inside the Tagify component.
   */
  children?: React.ReactNode;

  /**
   * Event handler triggered when a tag is clicked.
   * Receives the tag detail as parameter, allowing custom actions on tag click.
   */
  onTagClick?: (tagDetail: Tagify.ClickEventData<TagDataCustomize>) => void;

  /**
   * Event handler triggered when the input value changes.
   * Receives the updated input value as a string, allowing for controlled component updates.
   *
   * @default () => {}
   */
  onChange: (inputValue: string) => void;
}

export interface TagifyInputRef {
  onAddNewTag: (newTag: TagDataCustomize | string) => void;
  onReplaceTag: (currentTagEle: HTMLElement, newTag: TagDataCustomize) => void;
  onReload: (newValue: string) => void;
}

export type EmojiCollection = (typeof EMOJI_COLLECTIONS)[keyof typeof EMOJI_COLLECTIONS];

export type TagType = (typeof TAG_TYPE)[keyof typeof TAG_TYPE];
export type ShortLinkType = (typeof SHORT_LINK_TYPE)[keyof typeof SHORT_LINK_TYPE];
export type PatternCacheType = (typeof PATTERN_CACHE_TYPE)[keyof typeof PATTERN_CACHE_TYPE];
export type PatterTagName = Exclude<PatternCacheType, 'shortlink_pattern'>;
export type AcceptablePattern = Exclude<
  PatternCacheType,
  'shortlink_individual_pattern' | 'shortlink_general_pattern'
>;

export interface TagDataText extends TagData {
  value: string;
  label: string;
  type: TagType;
}

export interface TagDataEmoji extends Omit<TagDataText, 'type'> {
  value: string;
  type: Extract<TagType, 'emoji'>;
  collection: EmojiCollection;
}

export interface TagDataShortLink extends Omit<TagDataText, 'type'> {
  value: string;
  type: Extract<TagType, 'shortlink' | 'shortlink_v2'>;
  shortlinkType: ShortLinkType;
}

export type TagDataCustomize = TagDataEmoji | TagDataText | TagDataShortLink;

export interface EmojiTag {
  src?: string;
  emoji: string;
  code: string;
}

export type PatternHandler = (match: RegExpExecArray) => {
  isValid: boolean;
  tag: string;
  tagData: TagDataCustomize | undefined;
};

export interface PatternHandlerWrapper {
  pattern: string;
  name: PatterTagName;
  acceptablePattern: AcceptablePattern;
  handler: PatternHandler;
}

export interface URLOptions {
  protocols?: string[]; // Allowed protocols like 'http:', 'https:'
  requireTld?: boolean; // Whether to require a top-level domain (TLD)
  allowUnderscores?: boolean; // Whether to allow underscores in the URL
  allowTrailingDot?: boolean; // Whether to allow trailing dots
  allowNumericTld?: boolean; // Whether to allow numeric TLDs (e.g. '.123')
  allowIp?: boolean; // Whether to allow IP addresses
  maxLength?: number; // Maximum length of the URL
  minLength?: number; // Minimum length of the URL
  requireValidPath?: boolean; // Whether to require a valid path in the URL
  allowDataUrl?: boolean; // Whether to allow Data URLs (e.g., data:image/png;base64,...)
  allowFragment?: boolean; // Allow fragments in the URL
  requireFragment?: boolean; // Require a fragment in the URL
  fragmentRegex?: RegExp; // Validate the fragment with a custom regex
}

export interface TagifyWrapperProps {
  $cssTagifyVariables?: TagifyInputProps['cssTagifyVariables'];
  $status?: StatusType;
  $maxHeight?: number;
  $minWidth?: CSSProperties['minWidth'];
  $minWidthPlaceholder?: TagifyInputProps['minWidthPlaceholder'];
  $tagifyFullWidth?: boolean;
  $tagInvalidIcon?: any;
  $tagErrorIcon?: any;
  $placeholder?: string;
  $isSingleLineText?: TagifyInputProps['isSingleLineText'];
}

export type CustomEventTagify =
  | CustomEvent<Tagify.InputEventData<TagDataCustomize>>
  | CustomEvent<Tagify.ChangeEventData<TagDataCustomize>>;
