// Libraries
import { css } from 'styled-components';
import { isString } from 'lodash';

// Types
import type { StatusType, TagDataCustomize, TagifyWrapperProps } from './types';

// Constants
import { globalToken } from '@antscorp/antsomi-ui/es/constants';
import {
  DETECT_LINK,
  EMOJI,
  INVALID_TAG,
  MIN_H_WRAPPER,
  NO_VIEW_TAG,
  READONLY_TAG,
  REMOVED_TAG,
  SHORT_LINK,
  SHORT_LINK_TYPE,
  SHORT_LINK_V2,
  TAG_COLOR,
  TAG_H,
  TAG_TYPE,
} from './constants';

export const getTagifyStyled = ({ $maxHeight, $tagifyFullWidth }: TagifyWrapperProps) => css`
  ${$maxHeight &&
  css`
    max-height: ${$maxHeight}px;
    overflow-y: auto;
  `}

  &:not(.tagify--empty) {
    ${$tagifyFullWidth &&
    css`
      width: 100%;
    `}
  }

  /* Setting for readonly */
  &[readonly] {
    .tagify__tag {
      &__removeBtn {
        display: none !important;
      }
    }
  }

  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-height: ${MIN_H_WRAPPER}px;
  float: left;
  border: none;
  font-size: var(--input-font-size);
  background: transparent;
`;

export const getTagifyInputStyled = ({
  $minWidth,
  $placeholder,
  $minWidthPlaceholder,
  $isSingleLineText,
}: TagifyWrapperProps) => css`
  // Because $placeholder depends on $minWidth to display
  ${css`
    min-width: ${(() => {
      // Primary check for $minWidth
      if ($minWidth) {
        return isString($minWidth) ? $minWidth : `${$minWidth}px`;
      }

      // Secondary check for $placeholder
      if ($placeholder) {
        return isString($minWidthPlaceholder) ? $minWidthPlaceholder : `${$minWidthPlaceholder}px`;
      }

      // Fallback value
      return 'unset';
    })()} !important;
  `}

  ${$isSingleLineText &&
  css`
      white-space: nowrap;
      overflow: auto;

      br {
        display: none;
      }

      div, p {
        display: inline-block;
        white-space: nowrap;
        overflow: auto
        overflow-y: hidden;
      }
`}
`;

export const getStyledTags = (): string => {
  const getBackgroundColor = (tag: Partial<TagDataCustomize>) => {
    const { type } = tag;

    switch (type) {
      case TAG_TYPE.SHORT_LINK: {
        const { shortlinkType } = tag;

        if (!shortlinkType) break;

        return TAG_COLOR[SHORT_LINK][shortlinkType];
      }

      case TAG_TYPE.SHORT_LINK_V2: {
        const { shortlinkType } = tag;

        if (!shortlinkType) break;

        return TAG_COLOR[SHORT_LINK_V2][shortlinkType];
      }
      default: {
        if (!type) return 'white';

        return TAG_COLOR[type];
      }
    }

    return null;
  };

  const tagTypes = Object.values(TAG_TYPE);

  const backgroundStyles = tagTypes
    .map(type => {
      const background = getBackgroundColor({ type });

      if (!background) return '';

      return `
        &[data-tag-type~='${type}'] {
          background: ${getBackgroundColor({ type })};
        };
      `;
    })
    .join(' ');

  return `
    // For case unknown tag
    &[data-tag-type]:not(:is(
      ${tagTypes.map(tagType => `[data-tag-type~='${tagType}']`).join(',')}
    )) {
      background: ${globalToken?.accent7};
    };

    ${backgroundStyles}

    &[data-tag-type~='${EMOJI}'],
    &[data-tag-type~='${DETECT_LINK}'] {
      padding: 0;
    };

    &[data-tag-type~='${TAG_TYPE.SHORT_LINK}'] {
      &[data-tag-${TAG_TYPE.SHORT_LINK}-type~='${SHORT_LINK_TYPE.INDIVIDUAL}'] {
        background: ${getBackgroundColor({
          type: TAG_TYPE.SHORT_LINK,
          shortlinkType: SHORT_LINK_TYPE.INDIVIDUAL,
        })};
      };

      &[data-tag-${TAG_TYPE.SHORT_LINK}-type~='${SHORT_LINK_TYPE.GENERAL}'] {
        background: ${getBackgroundColor({
          type: TAG_TYPE.SHORT_LINK,
          shortlinkType: SHORT_LINK_TYPE.GENERAL,
        })};
      };
    }
    
    &[data-tag-type~='${TAG_TYPE.SHORT_LINK_V2}'] {
      &[data-tag-${TAG_TYPE.SHORT_LINK_V2}-type~='${SHORT_LINK_TYPE.INDIVIDUAL}'] {
        background: ${getBackgroundColor({
          type: TAG_TYPE.SHORT_LINK_V2,
          shortlinkType: SHORT_LINK_TYPE.INDIVIDUAL,
        })};
      };

      &[data-tag-${TAG_TYPE.SHORT_LINK_V2}-type~='${SHORT_LINK_TYPE.GENERAL}'] {
        background: ${getBackgroundColor({
          type: TAG_TYPE.SHORT_LINK_V2,
          shortlinkType: SHORT_LINK_TYPE.GENERAL,
        })};
      };
    }
  `;
};

export const getTagifyTagStyled = (_wrapperProps: TagifyWrapperProps) => css`
  &[${READONLY_TAG}='true'] {
    cursor: not-allowed;
  }

  &[${REMOVED_TAG}='true'] {
    ${({ $tagErrorIcon }: TagifyWrapperProps) =>
      $tagErrorIcon &&
      css`
        > div {
          padding-right: 22px;
          position: relative;

          > span {
            &::after {
              content: '';
              position: absolute;
              top: 50%;
              right: 5px;
              transform: translateY(calc(-50%));
              width: 14px;
              height: 14px;
              display: inline-block;
              background-image: url(${$tagErrorIcon});
              background-size: contain;
              background-repeat: no-repeat;
            }
          }
        }
        > div[data-tag-type] {
          background-color: white !important;
          border: 1px solid red;
        }
      `}
  }
  &[${NO_VIEW_TAG}='true'] {
    ${({ $tagInvalidIcon }: TagifyWrapperProps) =>
      $tagInvalidIcon &&
      css`
        > div {
          padding-right: 22px;
          position: relative;

          > span {
            &::after {
              content: '';
              position: absolute;
              top: 50%;
              right: 5px;
              transform: translateY(calc(-50%));
              width: 14px;
              height: 14px;
              display: inline-block;
              background-image: url(${$tagInvalidIcon});
              background-size: contain;
              background-repeat: no-repeat;
            }
          }
        }
        > div[data-tag-type] {
          background-color: white !important;
          border: 1px solid red;
        }
      `}
  }

  &[${INVALID_TAG}='true'] {
    ${({ $tagInvalidIcon }: TagifyWrapperProps) =>
      $tagInvalidIcon &&
      css`
        > div {
          padding-right: 22px;
          position: relative;

          > span {
            &::after {
              content: '';
              position: absolute;
              top: 50%;
              right: 5px;
              transform: translateY(calc(-50% - 1px));
              width: 14px;
              height: 14px;
              display: inline-block;
              background-image: url(${$tagInvalidIcon});
              background-size: contain;
              background-repeat: no-repeat;
            }
          }
        }
        > div[data-tag-type] {
          background-color: white !important;
          border: 1px solid red;
        }
      `}
  }

  height: ${TAG_H}px;
  cursor: pointer;

  > div {
    max-height: ${TAG_H}px;
    align-items: center;

    /* Set background color for selected tags */
    ${getStyledTags()}

    > * {
      white-space: nowrap;
    }
  }

  &__removeBtn {
    transition: all 0.2s ease;
    position: absolute;
    top: 50%;
    right: 0px;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    font-size: 16px;
    line-height: 16px;
    font-family: ${globalToken?.fontFamily};

    display: none;

    &:hover {
      color: ${globalToken?.colorPrimary};
    }
  }

  &:hover .tagify__tag__removeBtn {
    display: flex;
    z-index: 100;
  }
`;

export const getBackgroundInputStatus = (status?: StatusType) => {
  switch (status) {
    case 'error':
      return globalToken?.colorError || '#EF3340';
    case 'warning':
      return globalToken?.colorWarning || '#faad14';
    case 'success':
      return globalToken?.colorSuccess || '#12B800';
    default: {
      return globalToken?.blue1 || '#B8CFE6';
    }
  }
};
