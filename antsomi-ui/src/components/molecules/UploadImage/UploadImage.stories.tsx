// Libraries
import { useCallback, useState } from 'react';
import { Meta, StoryObj } from '@storybook/react/*';

// Components
import { UploadImage, UploadImageProps } from '.';
import { Flex, Typography, Image, Radio } from 'antd';

const DOMAIN_MEDIA_SANDBOX = 'https://sandbox-media-template.antsomi.com/cdp';

export default {
  title: 'Molecules/UploadImage',
  component: UploadImage,
  argTypes: {},
  parameters: {
    docs: {
      description: {
        component: 'UploadImage for media.',
      },
    },
  },
} as Meta<typeof UploadImage>;

export const Default = (args: UploadImageProps) => <UploadImage {...args} />;

const mockProps: UploadImageProps = {
  isInputMode: true,
  domainMedia: DOMAIN_MEDIA_SANDBOX,
  slug: 'api/v1',
  width: '100%', // Adjust based on conditions if needed
  paramConfigs: {
    token: '5474r2x214z254a4u2a4y4x5n4m5r2a4s2o4e4v4u5',
    userId: '**********',
    accountId: '**********',
  },
  placeholder: 'Upload or input URL',
  maxSize: 10, // Example value; adjust as needed
};

export const BasicUsage: StoryObj<UploadImageProps> = {
  render: () => {
    const [imageUrl, setImageUrl] = useState(
      'https://sandbox-st-media-template.antsomi.com/upload/2024/05/13/93df2a41-41fa-4dac-813a-c4f92eadd2ab.png',
    );

    const onChangeImage = image => {
      console.log('Image changed:', image?.url || '');
      setImageUrl(image?.url || '');
    };
    const onRemoveImage = useCallback(() => {
      console.log('Image removed');
      setImageUrl('');
    }, []);
    return (
      <Flex vertical gap={20}>
        <UploadImage
          {...mockProps}
          selectedImage={{ url: imageUrl }}
          onChangeImage={onChangeImage}
          onRemoveImage={onRemoveImage}
        />

        <Flex vertical gap={10}>
          <Typography.Text strong>Preview Image:</Typography.Text>
          <div style={{ margin: '0 auto' }}>
            <Image.PreviewGroup items={[imageUrl]}>
              <Image width={200} src={imageUrl} />
            </Image.PreviewGroup>
          </div>
        </Flex>
      </Flex>
    );
  },
};

export const RenderMode: StoryObj<UploadImageProps> = {
  render: () => {
    const [imageUrl, setImageUrl] = useState(
      'https://sandbox-st-media-template.antsomi.com/upload/2024/05/13/93df2a41-41fa-4dac-813a-c4f92eadd2ab.png',
    );
    const [isInputMode, setIsInputMode] = useState(false);

    const onChangeImage = image => {
      console.log('Image changed:', image?.url || '');
      setImageUrl(image?.url || '');
    };
    const onRemoveImage = useCallback(() => {
      console.log('Image removed');
      setImageUrl('');
    }, []);

    const handleChangeRadioGroup = event => {
      setIsInputMode(event?.target?.value !== 'block');
    };

    return (
      <Flex vertical gap={20}>
        <Flex vertical gap={10}>
          <Typography.Text>Render Mode:</Typography.Text>
          <Radio.Group
            options={[
              { label: 'Block', value: 'block' },
              { label: 'Inline', value: 'inline' },
            ]}
            defaultValue="block"
            onChange={handleChangeRadioGroup}
          />
        </Flex>

        <UploadImage
          {...mockProps}
          isInputMode={isInputMode}
          selectedImage={{ url: imageUrl }}
          onChangeImage={onChangeImage}
          onRemoveImage={onRemoveImage}
        />

        <Flex vertical gap={10}>
          <Typography.Text strong>Preview Image:</Typography.Text>
          <div style={{ margin: '0 auto' }}>
            <Image.PreviewGroup items={[imageUrl]}>
              <Image width={200} src={imageUrl} />
            </Image.PreviewGroup>
          </div>
        </Flex>
      </Flex>
    );
  },
};
