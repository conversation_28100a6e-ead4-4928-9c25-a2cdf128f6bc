// FontWeightSelect.stories.tsx

import { Meta } from '@storybook/react';

import { FontWeightSelect } from '.';

export default {
  title: 'Molecules/FontWeightSelect',
  component: FontWeightSelect,
} as Meta;

export const Default = {
  args: {
    defaultValue: '400',
    value: '400',
    label: 'Font Weight',
    onChange: (select: string) => {
      console.log(`Selected font weight: ${select}`);
    },
  },
};

export const LimitFonts = {
  args: {
    defaultValue: '400',
    value: '400',
    label: 'Font Weight',
    showOptions: ['Thin', 'Extra Light', 'Light', 'Regular', 'Medium'],
    onChange: (select: string) => {
      console.log(`Selected font weight: ${select}`);
    },
  },

  parameters: {
    docs: {
      description: {
        story: 'Limit font options by props "showOptions".',
      },
    },
  },
};
