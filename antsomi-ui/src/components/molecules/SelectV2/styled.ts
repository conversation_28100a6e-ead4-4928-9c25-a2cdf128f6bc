// Libraries
import styled from 'styled-components';

// Antd components
import { Tag, Select as AntdSelect } from 'antd';

// Constants
import { THEME } from '@antscorp/antsomi-ui/es/constants';

export const SelectWrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
`;

export const StyledSelect = styled(AntdSelect)`
  font-family: ${THEME.token?.fontFamily};

  .antsomi-select-arrow {
    display: flex;
    align-items: center;
    color: ${THEME.token?.colorTextBase} !important;
  }

  &.antsomi-select-open {
    .antsomi-select-selector {
      background-color: ${THEME.token?.blue0};
      --tw-shadow: 0 1px 0 0 ${THEME.token?.colorPrimary} !important;
      box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
        var(--tw-shadow) !important;
    }
  }

  &.antsomi-select-single:not(.antsomi-select-customize-input) .antsomi-select-selector {
    height: 30px;
  }

  .antsomi-select-selector {
    justify-content: space-between;
    min-height: 30px;
    padding-left: 5px;
    padding-right: 5px;
    font-weight: 400;
    font-size: ${THEME.token?.fontSize}px;
    color: ${THEME.token?.colorTextBase};
    transition: all 0.3s ease-in;

    &:hover {
      background: ${THEME.token?.blue0};
    }

    height: ${props => props.className?.includes('select-auto-height') && 'auto'};
    border-radius: 0px !important;
    border-style: none !important;
    --tw-shadow: 0 1px 0 0 ${THEME.token?.blue1} !important;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
      var(--tw-shadow) !important;

    &:active,
    &:focus {
      background: ${THEME.token?.blue0};
      --tw-shadow: 0 1px 0 0 ${THEME.token?.colorPrimary} !important;
      box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
        var(--tw-shadow) !important;
    }

    &:disabled {
      background: ${THEME.token?.accent};
      color: ${THEME.token?.accent5};
      box-shadow: 0 1px 0 0 ${THEME.token?.accent1};
    }

    .antsomi-select-selection-overflow {
      row-gap: 5px;
    }
  }

  &.antsomi-select-disabled {
    .antsomi-select-arrow {
      color: ${THEME.token?.colorTextDisabled} !important;
    }

    .antsomi-tag {
      opacity: 0.5 !important;
    }
  }
  &.antsomi-select-status-error {
    border-bottom: solid 1px #ff4d4f !important;

    ::placeholder {
      color: #ff4d4f !important;
    }
  }

  &.left-12 .antsomi-select-selection-placeholder {
    left: 12px;
  }

  .antsomi-select-clear {
    background: transparent;
  }
`;

export const StyledTag = styled(Tag)`
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  width: fit-content !important;
  height: 24px !important;
  margin-right: 5px !important;
  padding: 5px 10px !important;
  border-radius: 15px !important;
  border: none !important;
  background-color: ${THEME.token?.blue2} !important;
  cursor: pointer !important;

  .antsomi-tag-close-icon {
    opacity: 0 !important;
    position: absolute !important;
    right: 2px !important;
    transition: all 0.3s !important;
  }

  &:hover {
    .antsomi-tag-close-icon {
      opacity: 1 !important;
    }
  }
`;

export const CloseButton = styled.div<{ borderColor?: any }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 26px;
  border: 2px solid ${p => (p.borderColor ? p.borderColor : THEME.token?.blue2)};
  background-color: ${THEME.token?.bw0};
`;
