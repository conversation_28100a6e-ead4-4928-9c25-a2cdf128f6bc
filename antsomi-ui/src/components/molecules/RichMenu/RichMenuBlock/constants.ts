import { RichMenu } from '@antscorp/antsomi-ui/es/types';

export const SAMPLE_RICHMENU: RichMenu = {
  id: '7eqstz6ez3',
  menuName: 'Untitled Menu 2024-01-17 10:55:34',
  aliasId: '',
  isDefault: true,
  cells: [
    {
      rowEnd: 2,
      colEnd: 2,
      action: {
        type: 'message',
        text: 'Hello',
      },
      image: {
        imageUrl: '',
        imageStyles: {
          borderTopLeftRadius: '0px',
          borderTopRightRadius: '0px',
          borderBottomRightRadius: '0px',
          borderBottomLeftRadius: '0px',
          borderTopWidth: '0px',
          borderRightWidth: '0px',
          borderBottomWidth: '0px',
          borderLeftWidth: '0px',
          borderStyle: 'solid',
          borderColor: '#ffffff',
          boxShadow: 'none',
          opacity: 1,
          width: '100%',
          height: 'auto',
          objectPosition: 'center center',
          objectFit: 'unset',
          paddingBottom: '0px',
          paddingLeft: '0px',
          paddingRight: '0px',
          paddingTop: '0px',
        },
        imageSettings: {
          borderRadiusStyle: 'none',
          borderRadiusSuffix: 'px',
          linkedBorderRadiusInput: true,
          linkedBorderWidthInput: true,
          boxShadowStyle: 'none',
          boxShadowColor: 'rgba(0, 0, 0, 0.5)',
          boxShadowBlur: '0px',
          boxShadowSpread: '0px',
          boxShadowHorizontal: '0px',
          boxShadowVertical: '0px',
          boxShadowInset: false,
          widthSuffix: '%',
          heightSuffix: 'auto',
          linkedPaddingInput: false,
          paddingSuffix: 'px',
        },
      },
      outerContainerStyles: {
        textAlign: 'center',
      },
      areaStyles: {
        borderTopLeftRadius: '0px',
        borderTopRightRadius: '0px',
        borderBottomRightRadius: '0px',
        borderBottomLeftRadius: '0px',
        borderTopWidth: '0px',
        borderRightWidth: '0px',
        borderBottomWidth: '0px',
        borderLeftWidth: '0px',
        paddingTop: '0px',
        paddingRight: '0px',
        paddingBottom: '0px',
        paddingLeft: '0px',
        marginTop: '0px',
        marginRight: '0px',
        marginBottom: '0px',
        marginLeft: '0px',
        background: 'transparent',
        borderStyle: 'none',
        borderColor: '#000000',
        boxShadow: 'none',
        position: 'relative',
      },
      areaStylesSettings: {
        borderRadiusStyle: 'none',
        borderRadiusSuffix: 'px',
        linkedBorderRadiusInput: true,
        linkedBorderWidthInput: true,
        linkedPaddingInput: false,
        paddingSuffix: 'px',
        linkedMarginInput: true,
        marginSuffix: 'px',
        boxShadowStyle: 'none',
        boxShadowColor: '#ffffff',
        boxShadowBlur: '0px',
        boxShadowSpread: '0px',
        boxShadowHorizontal: '0px',
        boxShadowVertical: '0px',
        boxShadowInset: false,
        backgroundColor: 'transparent',
        backgroundColorStyle: 'color',
        backgroundPosition: 'left top',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        gradientType: 'linear-gradient',
        radialPosition: 'center center',
        linearAngle: 45,
        gradients: [
          {
            gradientColor: '#ffffff',
            gradientColorLocation: 0,
          },
          {
            gradientColor: '#000000',
            gradientColorLocation: 50,
          },
        ],
      },
      rowStart: 1,
      colStart: 1,
      active: false,
      x: 0,
      y: 0,
      width: 111.984375,
      height: 112,
    },
    {
      rowEnd: 2,
      colEnd: 3,
      action: {
        type: 'message',
        text: '',
      },
      image: {
        imageUrl: '',
        imageStyles: {
          borderTopLeftRadius: '0px',
          borderTopRightRadius: '0px',
          borderBottomRightRadius: '0px',
          borderBottomLeftRadius: '0px',
          borderTopWidth: '0px',
          borderRightWidth: '0px',
          borderBottomWidth: '0px',
          borderLeftWidth: '0px',
          borderStyle: 'solid',
          borderColor: '#ffffff',
          boxShadow: 'none',
          opacity: 1,
          width: '100%',
          height: 'auto',
          objectPosition: 'center center',
          objectFit: 'unset',
          paddingBottom: '0px',
          paddingLeft: '0px',
          paddingRight: '0px',
          paddingTop: '0px',
        },
        imageSettings: {
          borderRadiusStyle: 'none',
          borderRadiusSuffix: 'px',
          linkedBorderRadiusInput: true,
          linkedBorderWidthInput: true,
          boxShadowStyle: 'none',
          boxShadowColor: 'rgba(0, 0, 0, 0.5)',
          boxShadowBlur: '0px',
          boxShadowSpread: '0px',
          boxShadowHorizontal: '0px',
          boxShadowVertical: '0px',
          boxShadowInset: false,
          widthSuffix: '%',
          heightSuffix: 'auto',
          linkedPaddingInput: false,
          paddingSuffix: 'px',
        },
      },
      outerContainerStyles: {
        textAlign: 'center',
      },
      areaStyles: {
        borderTopLeftRadius: '0px',
        borderTopRightRadius: '0px',
        borderBottomRightRadius: '0px',
        borderBottomLeftRadius: '0px',
        borderTopWidth: '0px',
        borderRightWidth: '0px',
        borderBottomWidth: '0px',
        borderLeftWidth: '0px',
        paddingTop: '0px',
        paddingRight: '0px',
        paddingBottom: '0px',
        paddingLeft: '0px',
        marginTop: '0px',
        marginRight: '0px',
        marginBottom: '0px',
        marginLeft: '0px',
        background: 'transparent',
        borderStyle: 'none',
        borderColor: '#000000',
        boxShadow: 'none',
        position: 'relative',
      },
      areaStylesSettings: {
        borderRadiusStyle: 'none',
        borderRadiusSuffix: 'px',
        linkedBorderRadiusInput: true,
        linkedBorderWidthInput: true,
        linkedPaddingInput: false,
        paddingSuffix: 'px',
        linkedMarginInput: true,
        marginSuffix: 'px',
        boxShadowStyle: 'none',
        boxShadowColor: '#ffffff',
        boxShadowBlur: '0px',
        boxShadowSpread: '0px',
        boxShadowHorizontal: '0px',
        boxShadowVertical: '0px',
        boxShadowInset: false,
        backgroundColor: 'transparent',
        backgroundColorStyle: 'color',
        backgroundPosition: 'left top',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        gradientType: 'linear-gradient',
        radialPosition: 'center center',
        linearAngle: 45,
        gradients: [
          {
            gradientColor: '#ffffff',
            gradientColorLocation: 0,
          },
          {
            gradientColor: '#000000',
            gradientColorLocation: 50,
          },
        ],
      },
      rowStart: 1,
      colStart: 2,
      active: false,
      x: 111.984375,
      y: 0,
      width: 111.984375,
      height: 112,
    },
    {
      rowEnd: 2,
      colEnd: 4,
      action: {
        type: 'none',
      },
      image: {
        imageUrl: '',
        imageStyles: {
          borderTopLeftRadius: '0px',
          borderTopRightRadius: '0px',
          borderBottomRightRadius: '0px',
          borderBottomLeftRadius: '0px',
          borderTopWidth: '0px',
          borderRightWidth: '0px',
          borderBottomWidth: '0px',
          borderLeftWidth: '0px',
          borderStyle: 'solid',
          borderColor: '#ffffff',
          boxShadow: 'none',
          opacity: 1,
          width: '100%',
          height: 'auto',
          objectPosition: 'center center',
          objectFit: 'unset',
          paddingBottom: '0px',
          paddingLeft: '0px',
          paddingRight: '0px',
          paddingTop: '0px',
        },
        imageSettings: {
          borderRadiusStyle: 'none',
          borderRadiusSuffix: 'px',
          linkedBorderRadiusInput: true,
          linkedBorderWidthInput: true,
          boxShadowStyle: 'none',
          boxShadowColor: 'rgba(0, 0, 0, 0.5)',
          boxShadowBlur: '0px',
          boxShadowSpread: '0px',
          boxShadowHorizontal: '0px',
          boxShadowVertical: '0px',
          boxShadowInset: false,
          widthSuffix: '%',
          heightSuffix: 'auto',
          linkedPaddingInput: false,
          paddingSuffix: 'px',
        },
      },
      outerContainerStyles: {
        textAlign: 'center',
      },
      areaStyles: {
        borderTopLeftRadius: '0px',
        borderTopRightRadius: '0px',
        borderBottomRightRadius: '0px',
        borderBottomLeftRadius: '0px',
        borderTopWidth: '0px',
        borderRightWidth: '0px',
        borderBottomWidth: '0px',
        borderLeftWidth: '0px',
        paddingTop: '0px',
        paddingRight: '0px',
        paddingBottom: '0px',
        paddingLeft: '0px',
        marginTop: '0px',
        marginRight: '0px',
        marginBottom: '0px',
        marginLeft: '0px',
        background: 'transparent',
        borderStyle: 'none',
        borderColor: '#000000',
        boxShadow: 'none',
        position: 'relative',
      },
      areaStylesSettings: {
        borderRadiusStyle: 'none',
        borderRadiusSuffix: 'px',
        linkedBorderRadiusInput: true,
        linkedBorderWidthInput: true,
        linkedPaddingInput: false,
        paddingSuffix: 'px',
        linkedMarginInput: true,
        marginSuffix: 'px',
        boxShadowStyle: 'none',
        boxShadowColor: '#ffffff',
        boxShadowBlur: '0px',
        boxShadowSpread: '0px',
        boxShadowHorizontal: '0px',
        boxShadowVertical: '0px',
        boxShadowInset: false,
        backgroundColor: 'transparent',
        backgroundColorStyle: 'color',
        backgroundPosition: 'left top',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        gradientType: 'linear-gradient',
        radialPosition: 'center center',
        linearAngle: 45,
        gradients: [
          {
            gradientColor: '#ffffff',
            gradientColorLocation: 0,
          },
          {
            gradientColor: '#000000',
            gradientColorLocation: 50,
          },
        ],
      },
      rowStart: 1,
      colStart: 3,
      active: false,
      x: 223.96875,
      y: 0,
      width: 111.984375,
      height: 112,
    },
    {
      rowEnd: 3,
      colEnd: 2,
      action: {
        type: 'none',
      },
      image: {
        imageUrl: '',
        imageStyles: {
          borderTopLeftRadius: '0px',
          borderTopRightRadius: '0px',
          borderBottomRightRadius: '0px',
          borderBottomLeftRadius: '0px',
          borderTopWidth: '0px',
          borderRightWidth: '0px',
          borderBottomWidth: '0px',
          borderLeftWidth: '0px',
          borderStyle: 'solid',
          borderColor: '#ffffff',
          boxShadow: 'none',
          opacity: 1,
          width: '100%',
          height: 'auto',
          objectPosition: 'center center',
          objectFit: 'unset',
          paddingBottom: '0px',
          paddingLeft: '0px',
          paddingRight: '0px',
          paddingTop: '0px',
        },
        imageSettings: {
          borderRadiusStyle: 'none',
          borderRadiusSuffix: 'px',
          linkedBorderRadiusInput: true,
          linkedBorderWidthInput: true,
          boxShadowStyle: 'none',
          boxShadowColor: 'rgba(0, 0, 0, 0.5)',
          boxShadowBlur: '0px',
          boxShadowSpread: '0px',
          boxShadowHorizontal: '0px',
          boxShadowVertical: '0px',
          boxShadowInset: false,
          widthSuffix: '%',
          heightSuffix: 'auto',
          linkedPaddingInput: false,
          paddingSuffix: 'px',
        },
      },
      outerContainerStyles: {
        textAlign: 'center',
      },
      areaStyles: {
        borderTopLeftRadius: '0px',
        borderTopRightRadius: '0px',
        borderBottomRightRadius: '0px',
        borderBottomLeftRadius: '0px',
        borderTopWidth: '0px',
        borderRightWidth: '0px',
        borderBottomWidth: '0px',
        borderLeftWidth: '0px',
        paddingTop: '0px',
        paddingRight: '0px',
        paddingBottom: '0px',
        paddingLeft: '0px',
        marginTop: '0px',
        marginRight: '0px',
        marginBottom: '0px',
        marginLeft: '0px',
        background: 'transparent',
        borderStyle: 'none',
        borderColor: '#000000',
        boxShadow: 'none',
        position: 'relative',
      },
      areaStylesSettings: {
        borderRadiusStyle: 'none',
        borderRadiusSuffix: 'px',
        linkedBorderRadiusInput: true,
        linkedBorderWidthInput: true,
        linkedPaddingInput: false,
        paddingSuffix: 'px',
        linkedMarginInput: true,
        marginSuffix: 'px',
        boxShadowStyle: 'none',
        boxShadowColor: '#ffffff',
        boxShadowBlur: '0px',
        boxShadowSpread: '0px',
        boxShadowHorizontal: '0px',
        boxShadowVertical: '0px',
        boxShadowInset: false,
        backgroundColor: 'transparent',
        backgroundColorStyle: 'color',
        backgroundPosition: 'left top',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        gradientType: 'linear-gradient',
        radialPosition: 'center center',
        linearAngle: 45,
        gradients: [
          {
            gradientColor: '#ffffff',
            gradientColorLocation: 0,
          },
          {
            gradientColor: '#000000',
            gradientColorLocation: 50,
          },
        ],
      },
      rowStart: 2,
      colStart: 1,
      active: false,
      x: 0,
      y: 112,
      width: 111.984375,
      height: 112,
    },
    {
      rowEnd: 3,
      colEnd: 3,
      action: {
        type: 'none',
      },
      image: {
        imageUrl: '',
        imageStyles: {
          borderTopLeftRadius: '0px',
          borderTopRightRadius: '0px',
          borderBottomRightRadius: '0px',
          borderBottomLeftRadius: '0px',
          borderTopWidth: '0px',
          borderRightWidth: '0px',
          borderBottomWidth: '0px',
          borderLeftWidth: '0px',
          borderStyle: 'solid',
          borderColor: '#ffffff',
          boxShadow: 'none',
          opacity: 1,
          width: '100%',
          height: 'auto',
          objectPosition: 'center center',
          objectFit: 'unset',
          paddingBottom: '0px',
          paddingLeft: '0px',
          paddingRight: '0px',
          paddingTop: '0px',
        },
        imageSettings: {
          borderRadiusStyle: 'none',
          borderRadiusSuffix: 'px',
          linkedBorderRadiusInput: true,
          linkedBorderWidthInput: true,
          boxShadowStyle: 'none',
          boxShadowColor: 'rgba(0, 0, 0, 0.5)',
          boxShadowBlur: '0px',
          boxShadowSpread: '0px',
          boxShadowHorizontal: '0px',
          boxShadowVertical: '0px',
          boxShadowInset: false,
          widthSuffix: '%',
          heightSuffix: 'auto',
          linkedPaddingInput: false,
          paddingSuffix: 'px',
        },
      },
      outerContainerStyles: {
        textAlign: 'center',
      },
      areaStyles: {
        borderTopLeftRadius: '0px',
        borderTopRightRadius: '0px',
        borderBottomRightRadius: '0px',
        borderBottomLeftRadius: '0px',
        borderTopWidth: '0px',
        borderRightWidth: '0px',
        borderBottomWidth: '0px',
        borderLeftWidth: '0px',
        paddingTop: '0px',
        paddingRight: '0px',
        paddingBottom: '0px',
        paddingLeft: '0px',
        marginTop: '0px',
        marginRight: '0px',
        marginBottom: '0px',
        marginLeft: '0px',
        background: 'transparent',
        borderStyle: 'none',
        borderColor: '#000000',
        boxShadow: 'none',
        position: 'relative',
      },
      areaStylesSettings: {
        borderRadiusStyle: 'none',
        borderRadiusSuffix: 'px',
        linkedBorderRadiusInput: true,
        linkedBorderWidthInput: true,
        linkedPaddingInput: false,
        paddingSuffix: 'px',
        linkedMarginInput: true,
        marginSuffix: 'px',
        boxShadowStyle: 'none',
        boxShadowColor: '#ffffff',
        boxShadowBlur: '0px',
        boxShadowSpread: '0px',
        boxShadowHorizontal: '0px',
        boxShadowVertical: '0px',
        boxShadowInset: false,
        backgroundColor: 'transparent',
        backgroundColorStyle: 'color',
        backgroundPosition: 'left top',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        gradientType: 'linear-gradient',
        radialPosition: 'center center',
        linearAngle: 45,
        gradients: [
          {
            gradientColor: '#ffffff',
            gradientColorLocation: 0,
          },
          {
            gradientColor: '#000000',
            gradientColorLocation: 50,
          },
        ],
      },
      rowStart: 2,
      colStart: 2,
      active: false,
      x: 111.984375,
      y: 112,
      width: 111.984375,
      height: 112,
    },
    {
      rowEnd: 3,
      colEnd: 4,
      action: {
        type: 'none',
      },
      image: {
        imageUrl: '',
        imageStyles: {
          borderTopLeftRadius: '0px',
          borderTopRightRadius: '0px',
          borderBottomRightRadius: '0px',
          borderBottomLeftRadius: '0px',
          borderTopWidth: '0px',
          borderRightWidth: '0px',
          borderBottomWidth: '0px',
          borderLeftWidth: '0px',
          borderStyle: 'solid',
          borderColor: '#ffffff',
          boxShadow: 'none',
          opacity: 1,
          width: '100%',
          height: 'auto',
          objectPosition: 'center center',
          objectFit: 'unset',
          paddingBottom: '0px',
          paddingLeft: '0px',
          paddingRight: '0px',
          paddingTop: '0px',
        },
        imageSettings: {
          borderRadiusStyle: 'none',
          borderRadiusSuffix: 'px',
          linkedBorderRadiusInput: true,
          linkedBorderWidthInput: true,
          boxShadowStyle: 'none',
          boxShadowColor: 'rgba(0, 0, 0, 0.5)',
          boxShadowBlur: '0px',
          boxShadowSpread: '0px',
          boxShadowHorizontal: '0px',
          boxShadowVertical: '0px',
          boxShadowInset: false,
          widthSuffix: '%',
          heightSuffix: 'auto',
          linkedPaddingInput: false,
          paddingSuffix: 'px',
        },
      },
      outerContainerStyles: {
        textAlign: 'center',
      },
      areaStyles: {
        borderTopLeftRadius: '0px',
        borderTopRightRadius: '0px',
        borderBottomRightRadius: '0px',
        borderBottomLeftRadius: '0px',
        borderTopWidth: '0px',
        borderRightWidth: '0px',
        borderBottomWidth: '0px',
        borderLeftWidth: '0px',
        paddingTop: '0px',
        paddingRight: '0px',
        paddingBottom: '0px',
        paddingLeft: '0px',
        marginTop: '0px',
        marginRight: '0px',
        marginBottom: '0px',
        marginLeft: '0px',
        background: 'transparent',
        borderStyle: 'none',
        borderColor: '#000000',
        boxShadow: 'none',
        position: 'relative',
      },
      areaStylesSettings: {
        borderRadiusStyle: 'none',
        borderRadiusSuffix: 'px',
        linkedBorderRadiusInput: true,
        linkedBorderWidthInput: true,
        linkedPaddingInput: false,
        paddingSuffix: 'px',
        linkedMarginInput: true,
        marginSuffix: 'px',
        boxShadowStyle: 'none',
        boxShadowColor: '#ffffff',
        boxShadowBlur: '0px',
        boxShadowSpread: '0px',
        boxShadowHorizontal: '0px',
        boxShadowVertical: '0px',
        boxShadowInset: false,
        backgroundColor: 'transparent',
        backgroundColorStyle: 'color',
        backgroundPosition: 'left top',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        gradientType: 'linear-gradient',
        radialPosition: 'center center',
        linearAngle: 45,
        gradients: [
          {
            gradientColor: '#ffffff',
            gradientColorLocation: 0,
          },
          {
            gradientColor: '#000000',
            gradientColorLocation: 50,
          },
        ],
      },
      rowStart: 2,
      colStart: 3,
      active: false,
      x: 223.96875,
      y: 112,
      width: 111.984375,
      height: 112,
    },
  ],
  cols: 3,
  rows: 2,
  areaLayoutId: 'LAYOUT_1',
  gridTemplateRows: ['50%', '50%'],
  gridTemplateCols: ['33.33%', '33.33%', '33.33%'],
  layoutType: 'large',
  uploadMode: 'single',
  chatBar: {
    label: 'Menu',
    displayDefault: true,
  },
  image: {
    imageUrl:
      'https://sandbox-st-media-template.antsomi.com/upload/2024/01/04/8d8cf39a-748f-435c-b6c6-b71b5facc739.gif',
    imageStyles: {
      borderTopLeftRadius: '0px',
      borderTopRightRadius: '0px',
      borderBottomRightRadius: '0px',
      borderBottomLeftRadius: '0px',
      borderTopWidth: '10px',
      borderRightWidth: '10px',
      borderBottomWidth: '10px',
      borderLeftWidth: '10px',
      borderStyle: 'solid',
      borderColor: '#df3030',
      boxShadow: 'rgba(0, 0, 0, 0.5) 0px 0px 20px 0px ',
      opacity: 1,
      width: '100%',
      height: 'auto',
      objectPosition: 'center center',
      objectFit: 'cover',
      paddingBottom: '0px',
      paddingLeft: '0px',
      paddingRight: '0px',
      paddingTop: '0px',
    },
    imageSettings: {
      borderRadiusStyle: 'none',
      borderRadiusSuffix: 'px',
      linkedBorderRadiusInput: true,
      linkedBorderWidthInput: true,
      boxShadowStyle: 'light',
      boxShadowColor: 'rgba(0, 0, 0, 0.5)',
      boxShadowBlur: '20px',
      boxShadowSpread: '0px',
      boxShadowHorizontal: '0px',
      boxShadowVertical: '0px',
      boxShadowInset: false,
      widthSuffix: '%',
      heightSuffix: 'auto',
      linkedPaddingInput: false,
      paddingSuffix: 'px',
    },
  },
};
