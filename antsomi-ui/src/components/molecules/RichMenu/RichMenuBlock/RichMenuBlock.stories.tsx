// Libraries
import React, { useState } from 'react';

// Components
import { StoryObj, Meta, StoryFn } from '@storybook/react';
import { RichMenuBlock } from './RichMenuBlock';

import { SAMPLE_RICHMENU } from './constants';

export default {
  title: 'Molecules/RichMenu/RichMenuBlock',
  component: RichMenuBlock,
  argTypes: {},
  parameters: {
    docs: {
      description: {
        component: 'RichMenuBlock Component',
      },
    },
  },
} as Meta<typeof RichMenuBlock>;

const Template: StoryFn<typeof RichMenuBlock> = args => (
  <div style={{ width: 336 }}>
    <RichMenuBlock {...args} />
  </div>
);

export const Default = {
  render: Template,

  args: {
    richMenu: SAMPLE_RICHMENU,
  },
};

export const Basic: StoryObj<any> = {
  render: () => {
    const [richMenu, setRichMenu] = useState(SAMPLE_RICHMENU);

    return (
      <div style={{ width: 336 }}>
        <RichMenuBlock
          richMenu={richMenu}
          onChange={richMenu => {
            console.log(richMenu);
            setRichMenu(richMenu);
          }}
        />
      </div>
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'This is a basic example of RichMenuBlock',
      },
    },
  },
};
