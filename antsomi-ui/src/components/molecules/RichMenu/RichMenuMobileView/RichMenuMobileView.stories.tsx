// Libraries
import React, { useState } from 'react';
import { StoryObj, Meta } from '@storybook/react';

// Components
import { Flex } from '@antscorp/antsomi-ui/es/components/atoms';
import { SAMPLE_RICHMENU } from '../RichMenuBlock';
import { RichMenuMobileView } from './RichMenuMobileView';

export default {
  title: 'Molecules/RichMenu/RichMenuMobileView',
  component: RichMenuMobileView,
  argTypes: {},
  parameters: {
    docs: {
      description: {
        component: 'RichMenuMobileView Component',
      },
    },
  },
} as Meta<typeof RichMenuMobileView>;

export const Default = {
  args: {
    richMenu: SAMPLE_RICHMENU,
    isPreview: true,
  },
};

export const Basic: StoryObj<any> = {
  render: () => {
    const [richMenu] = useState(SAMPLE_RICHMENU);

    return (
      <Flex>
        <RichMenuMobileView richMenu={richMenu} isPreview />
      </Flex>
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'This is a basic example of RichMenuMobileView',
      },
    },
  },
};
