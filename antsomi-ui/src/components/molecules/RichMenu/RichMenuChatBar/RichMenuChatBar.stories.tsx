// Libraries
import React, { useState } from 'react';

// Components
import { StoryObj, Meta } from '@storybook/react';
import { RichMenuChatBar } from './RichMenuChatBar';

export default {
  title: 'Molecules/RichMenu/RichMenuChatBar',
  component: RichMenuChatBar,
  argTypes: {},
  parameters: {
    docs: {
      description: {
        component: 'RichMenuChatBar Component',
      },
    },
  },
} as Meta<typeof RichMenuChatBar>;

export const Default = {
  args: {},
};

export const Basic: StoryObj<any> = {
  render: () => {
    const [state, setState] = useState({
      showRichMenu: true,
      showTypingChat: false,
    });

    const { showRichMenu, showTypingChat } = state;

    return (
      <RichMenuChatBar
        style={{ background: '#f8f8f8' }}
        showRichMenu={showRichMenu}
        showTypingChat={showTypingChat}
        onToggleRichMenu={toggle =>
          setState(prev => ({
            ...prev,
            showRichMenu: toggle,
          }))
        }
        onToggleTypingChat={toggle =>
          setState(prev => ({
            ...prev,
            showTypingChat: toggle,
          }))
        }
      />
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'This is a basic example of RichMenuChatBar',
      },
    },
  },
};
