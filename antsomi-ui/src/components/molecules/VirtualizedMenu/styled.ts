import styled from 'styled-components';
import { VariableSizeList } from 'react-window';
import { THEME, globalToken } from '@antscorp/antsomi-ui/es/constants';
import AutoSizer from 'react-virtualized-auto-sizer';
import { CLS } from './utils';
import tinycolor from 'tinycolor2';
import { MemoryData } from './types';

export const MenuItemWrapper = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  gap: 8px;
  justify-content: space-between;
  padding: 5px;
  align-items: center;
  overflow: hidden;

  .icon-expand {
    font-size: 14px;
    flex-shrink: 0;
  }

  .title-wrapper {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
`;

export const MenuItemRoot = styled.li`
  &.${CLS.Item.default} {
    cursor: pointer;
    display: flex;
    align-items: center;
    overflow: hidden;

    &:focus {
      outline: none;
    }

    > .${CLS.ItemLabel.default} {
      display: flex;
      flex: 1;
      overflow: hidden;
      align-self: stretch;
      align-items: center;
    }

    > .${CLS.ItemAction.default} {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &.${CLS.Item.disabled} {
      color: ${THEME.components?.Menu?.itemDisabledColor};
      cursor: not-allowed;
    }

    &.${CLS.Item.selected} {
      background-color: ${THEME.components?.Menu?.itemActiveBg};
    }

    &.${CLS.Item.expanded} {
      > .${CLS.ItemAction.default} {
        > .${CLS.IconExpand.default} {
          rotate: 180deg;
          transition: rotate 0.4s;
        }
      }
    }

    &.${CLS.Item.selectedDescendant} {
      background-color: ${tinycolor(THEME.components?.Menu?.itemActiveBg)
        .lighten(4)
        .toString('rgb')};
    }

    &.${CLS.Item.displayOnly} {
      cursor: default;
      color: ${THEME.components?.Menu?.itemColor || globalToken?.colorText};

      &:focus,
      &:hover {
        background-color: transparent;
      }
    }

    &:focus:not(.${CLS.Item.disabled}, .${CLS.Item.selected}, .${CLS.Item.selectedDescendant}, .${CLS.Item.displayOnly}),
    &:hover:not(.${CLS.Item.disabled}, .${CLS.Item.selected}, .${CLS.Item.selectedDescendant}, .${CLS.Item.displayOnly}) {
      background-color: ${THEME.components?.Menu?.itemHoverBg};
    }
  }
`;

export const VirtualizedMenuContainer = styled(VariableSizeList<MemoryData>)`
  font-size: ${globalToken?.fontSize}px;
  scrollbar-gutter: stable;

  ul {
    padding: 0;
    margin: 0;
  }
`;

export const StyledAutoSizer = styled(AutoSizer)`
  flex: 1;
`;
