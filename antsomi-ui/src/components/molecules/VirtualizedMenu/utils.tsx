import { antsomiClsx } from '@antscorp/antsomi-ui/es/utils';
import { ItemType } from './types';
import { COMPONENT_NAME, ITEM_SIZE, ITEM_SPACING, MODE } from './config';
import { memoize } from 'lodash';
import { NormalizedTreeState, normalizeTreeData } from '@antscorp/antsomi-ui/es/utils/tree';

export const itemToTreeNode = (item: ItemType) => ({
  id: item.key,
  data: item,
  children: item.children?.map(itemToTreeNode),
});

export const serializeItems = (args: { items: ItemType[] }): NormalizedTreeState<ItemType> => {
  const { items } = args;

  const nodes = items.map(itemToTreeNode);

  return normalizeTreeData(nodes);
};

const componentCls = antsomiClsx(COMPONENT_NAME);

export const CLS = {
  Root: {
    default: componentCls('menu-root'),
  },

  Menu: {
    default: componentCls('menu'),
  },

  MenuInline: {
    default: componentCls(`menu-${MODE.Inline}`),
  },

  ItemLabel: {
    default: componentCls('item-label'),
  },

  ItemAction: {
    default: componentCls('item-action'),
  },

  IconExpand: {
    default: componentCls('icon-expand'),
  },

  Item: {
    default: componentCls('item'),
    disabled: componentCls('item-disabled'),
    selected: componentCls('item-selected'),
    expanded: componentCls('item-expanded'),
    selectedDescendant: componentCls('item-selected-descendant'),
    displayOnly: componentCls('item-display-only'),
  },
} as const;

export const getItemSize = memoize(
  (itemSize: number = ITEM_SIZE, itemSpacing: number = ITEM_SPACING) =>
    (index: number) => {
      if (index === 0) {
        return itemSize;
      }

      return itemSize + itemSpacing;
    },
);

export const calInlineListSize = (
  itemLength: number,
  itemSize: number = ITEM_SIZE,
  itemSpacing: number = ITEM_SPACING,
) => {
  let result: number = 0;

  const calItemSize = getItemSize(itemSize, itemSpacing);

  for (let i = 0; i < itemLength; i++) {
    result += calItemSize(i);
  }

  return result;
};
