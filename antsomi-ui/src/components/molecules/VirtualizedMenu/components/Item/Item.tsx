import React, { memo, useMemo, useRef, useEffect, useCallback } from 'react';
import { Typography } from '../../../../atoms/Typography';
import { MenuItemRoot } from '../../styled';
import clsx from 'clsx';
import { ListChildComponentProps, areEqual } from 'react-window';
import { isEmpty } from 'lodash';
import { CLS } from '../../utils';
import { ExpandMoreIcon } from '@antscorp/antsomi-ui/es/components/icons';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';
import { isDescendant } from '@antscorp/antsomi-ui/es/utils/tree';
import { MemoryData } from '../../types';

export const Item = memo((props: ListChildComponentProps<MemoryData>) => {
  const { style = {}, data, index = 0 } = props;

  const {
    inlineIndent,
    itemSpacing,
    inlinePadding,
    items,
    selectedKeys,
    expandedKeys,
    normalizeTreeItems,
    action,
    focusId,
    onClick,
  } = data || {};

  const itemRef = useRef<HTMLLIElement | null>(null);

  const item = items[index];
  const hasChildren = !isEmpty(item.data.children);

  const isFocused = item.id === focusId;
  const expanded = hasChildren && expandedKeys.has(item.data.key);
  const selected = selectedKeys.has(item.data.key);
  const disabled = item.data?.disabled;
  const displayOnly = item.data?.displayOnly;

  const indentSize = item.level * inlineIndent;

  const itemStyle: React.CSSProperties = {
    ...style,
    paddingLeft: indentSize + inlinePadding,
    paddingRight: inlinePadding,
    top: index === 0 ? Number(style.top) : Number(style.top) + itemSpacing,
    height: index === 0 ? Number(style.height) : Number(style.height) - itemSpacing,
  };

  const itemClassName = clsx(
    CLS.Item.default,
    {
      [CLS.Item.selected]: selected,
      [CLS.Item.expanded]: expanded,
      [CLS.Item.disabled]: disabled,
      [CLS.Item.displayOnly]: displayOnly,
    },
    item.data.className,
  );

  const handleItemRef = useCallback(
    (el: HTMLLIElement | null) => {
      if (!el) return;

      itemRef.current = el;

      item.data.ref(el);
    },
    [item.data],
  );

  const handleOnClick = (event: React.MouseEvent, elKey: 'item' | 'item-expand-el') => {
    if (!onClick || disabled || displayOnly) return;

    onClick({ item, index, elKey, domEvent: event });
  };

  const labelContent: React.ReactNode =
    typeof item.data?.label === 'string' ? (
      <Typography.Text
        ellipsis={{
          tooltip: {
            destroyTooltipOnHide: true,
          },
        }}
      >
        {item.data.label}
      </Typography.Text>
    ) : (
      item.data?.label
    );

  const expandEl = (
    <ExpandMoreIcon
      size={20}
      aria-hidden="true"
      color={globalToken?.colorIcon}
      className={CLS.IconExpand.default}
      onClick={e => {
        e.stopPropagation();

        handleOnClick(e, 'item-expand-el');
      }}
    />
  );

  let actionContent = hasChildren ? <div className={CLS.ItemAction.default}>{expandEl}</div> : null;

  if (action) {
    actionContent = (
      <div className={CLS.ItemAction.default}>
        {typeof action === 'function' ? (
          action(item.data, { expandEl })
        ) : (
          <>
            {expandEl}
            {action}
          </>
        )}
      </div>
    );
  }

  const isSelectedDescendant = useMemo(() => {
    let result = false;

    for (const selectedKey of selectedKeys) {
      if (result) break;

      result = isDescendant(item.id, selectedKey, normalizeTreeItems);
    }

    return result;
  }, [item.id, normalizeTreeItems, selectedKeys]);

  const className = clsx(itemClassName, {
    [CLS.Item.selectedDescendant]: isSelectedDescendant,
  });

  useEffect(() => {
    if (isFocused) {
      itemRef.current?.focus({ preventScroll: true });
    }
  }, [isFocused]);

  return (
    <MenuItemRoot
      ref={handleItemRef}
      role={displayOnly ? 'none' : 'treeitem'}
      tabIndex={displayOnly ? -1 : isFocused ? 0 : -1}
      aria-selected={displayOnly ? undefined : selected}
      aria-disabled={displayOnly ? undefined : disabled}
      aria-expanded={displayOnly ? undefined : expanded}
      aria-level={displayOnly ? undefined : item.level}
      aria-posinset={displayOnly ? undefined : index + 1}
      aria-setsize={displayOnly ? undefined : items.length}
      className={className}
      style={itemStyle}
      onClick={e => handleOnClick(e, 'item')}
    >
      <div className={CLS.ItemLabel.default}>{labelContent}</div>

      {actionContent}
    </MenuItemRoot>
  );
}, areEqual);
