import React, {
  ComponentPropsWithoutRef,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
  useCallback,
  useRef,
  memo,
} from 'react';
import { VariableSizeList } from 'react-window';
import { StyledAutoSizer, VirtualizedMenuContainer } from '../../styled';
import { INLINE_INDENT, INLINE_PADDING, ITEM_SIZE, ITEM_SPACING } from '../../config';
import { ItemType, MemoryData, MenuInlineHandle, MenuInlineProps } from '../../types';
import { CLS, serializeItems, getItemSize } from '../../utils';
import { Item } from '../Item';
import { useDeepCompareEffect } from '@antscorp/antsomi-ui/es/hooks';
import clsx from 'clsx';
import {
  NormalizedNode,
  NormalizedTreeState,
  findNodes,
  getChildrenRecursively,
  getPathToNode,
} from '@antscorp/antsomi-ui/es/utils/tree';

const innerElementType = forwardRef<HTMLUListElement, ComponentPropsWithoutRef<'ul'>>(
  ({ style, ...rest }, ref) => (
    <ul role="tree" aria-label="Virtualized Menu" ref={ref} style={style} {...rest} />
  ),
);

/**
 * Optimized function to compute visible items using breadth-first traversal
 * instead of filtering all items
 */
const computeVisibleItemsOptimized = (
  normalizeTreeItems: Map<string, NormalizedNode<ItemType>>,
  rootItemIds: Set<string>,
  expandedKeys: Set<string>,
): NormalizedNode<ItemType>[] => {
  const visibleItems: NormalizedNode<ItemType>[] = [];
  const queue: string[] = Array.from(rootItemIds);

  // Process queue in breadth-first manner
  let index = 0;
  while (index < queue.length) {
    const itemId = queue[index++];
    const item = normalizeTreeItems.get(itemId);

    if (!item) continue;

    visibleItems.push(item);

    // Add children to queue only if this item is expanded
    if (expandedKeys.has(item.id) && item.childIds.length > 0) {
      queue.push(...item.childIds);
    }
  }

  return visibleItems;
};

/**
 * Alternative: Path-based optimization with memoization
 * Caches path calculations to avoid redundant work
 */
const computeVisibleItemsWithPathCache = (
  normalizeTreeItems: Map<string, NormalizedNode<ItemType>>,
  expandedKeys: Set<string>,
  pathCache: Map<string, NormalizedNode<ItemType>[]>,
): NormalizedNode<ItemType>[] => {
  const visibleItems: NormalizedNode<ItemType>[] = [];

  for (const item of normalizeTreeItems.values()) {
    const isRootItem = item.level === 0;

    if (isRootItem) {
      visibleItems.push(item);
      continue;
    }

    if (!item.parentId) continue;

    // Use cached path or compute and cache
    let pathToParent = pathCache.get(item.parentId);

    if (!pathToParent) {
      pathToParent = getPathToNode(item.parentId, normalizeTreeItems);

      pathCache.set(item.parentId, pathToParent);
    }

    // Check if all parents are expanded
    const allParentExpanded = pathToParent.every(parent => expandedKeys.has(parent.id));

    if (allParentExpanded) {
      visibleItems.push(item);
    }
  }

  return visibleItems;
};

/**
 * Level-based optimization: Process items level by level
 * More efficient for deep trees with many collapsed branches
 */
const computeVisibleItemsByLevel = (
  normalizeTreeItems: Map<string, NormalizedNode<ItemType>>,
  rootItemIds: Set<string>,
  expandedKeys: Set<string>,
): NormalizedNode<ItemType>[] => {
  const visibleItems: NormalizedNode<ItemType>[] = [];
  const expandedParents = new Set<string>();

  // Add root items and track expanded ones
  for (const rootId of rootItemIds) {
    const rootItem = normalizeTreeItems.get(rootId);

    if (rootItem) {
      visibleItems.push(rootItem);

      if (expandedKeys.has(rootId)) {
        expandedParents.add(rootId);
      }
    }
  }

  // Process each level
  let currentLevelParents = expandedParents;
  let currentLevel = 1;

  while (currentLevelParents.size > 0) {
    const nextLevelParents = new Set<string>();

    // Process all items at current level
    for (const parentId of currentLevelParents) {
      const parent = normalizeTreeItems.get(parentId);

      if (!parent) continue;

      // Add all children of this expanded parent
      for (const childId of parent.childIds) {
        const child = normalizeTreeItems.get(childId);

        if (child && child.level === currentLevel) {
          visibleItems.push(child);

          // Track if this child is expanded for next level
          if (expandedKeys.has(childId)) {
            nextLevelParents.add(childId);
          }
        }
      }
    }

    currentLevelParents = nextLevelParents;

    currentLevel++;
  }

  return visibleItems;
};

const MenuInlineInner = forwardRef<MenuInlineHandle, MenuInlineProps>((props, ref) => {
  const {
    items: itemsProp = [],
    expanded: expandedProp = [],
    selected: selectedProp = [],
    inlineIndent = INLINE_INDENT,
    inlinePadding = INLINE_PADDING,
    itemSize = ITEM_SIZE,
    itemSpacing = ITEM_SPACING,
    selectable = false,
    action,
    className,
    onClick,
  } = props;

  const [focusId, setFocusId] = useState<string | null>(null);
  const [expandedKeys, setExpandedKeys] = useState<Set<string>>(new Set());
  const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set());

  const listRef = useRef<VariableSizeList | null>(null);
  const itemRefs = useRef<Map<string, HTMLLIElement>>(new Map());

  const [{ items: normalizeTreeItems, rootItemIds }, setTreeState] = useState<
    NormalizedTreeState<ItemType>
  >({
    items: new Map(),
    rootItemIds: new Set(),
  });

  // Cache for path calculations to avoid redundant work
  const pathCache = useMemo(() => new Map<string, NormalizedNode<ItemType>[]>(), []);

  const rootNodes = useMemo(
    () => findNodes(node => rootItemIds.has(node.id), normalizeTreeItems),
    [normalizeTreeItems, rootItemIds],
  );

  useDeepCompareEffect(() => {
    setSelectedKeys(new Set(selectedProp));
  }, [selectedProp]);

  useDeepCompareEffect(() => {
    setExpandedKeys(new Set(expandedProp));
  }, [expandedProp]);

  useEffect(() => {
    setTreeState(
      serializeItems({
        items: itemsProp,
      }),
    );
  }, [itemsProp]);

  // Clear path cache when tree structure changes
  useEffect(() => {
    pathCache.clear();
  }, [normalizeTreeItems, pathCache]);

  // Optimized visible items calculation
  const visibleItems = useMemo(() => {
    if (normalizeTreeItems.size === 0) return [];

    // Choose optimization strategy based on tree characteristics
    const treeDepth = Math.max(...Array.from(normalizeTreeItems.values()).map(item => item.level));
    const totalItems = normalizeTreeItems.size;
    const expandedCount = expandedKeys.size;

    // Strategy selection heuristics:
    // - For shallow trees with many expanded items: use breadth-first
    // - For deep trees with few expanded items: use level-based
    // - For medium trees: use path-cache optimization

    if (treeDepth <= 3 && expandedCount > totalItems * 0.3) {
      // Shallow tree with many expanded items
      return computeVisibleItemsOptimized(normalizeTreeItems, rootItemIds, expandedKeys);
    }

    if (treeDepth > 5 && expandedCount < totalItems * 0.2) {
      // Deep tree with few expanded items
      return computeVisibleItemsByLevel(normalizeTreeItems, rootItemIds, expandedKeys);
    }

    // Medium complexity - use cached path approach
    return computeVisibleItemsWithPathCache(normalizeTreeItems, expandedKeys, pathCache);
  }, [expandedKeys, normalizeTreeItems, rootItemIds, pathCache]);

  const handleCollapseItems = useCallback((items: NormalizedNode<ItemType>[]) => {
    setExpandedKeys(prev => {
      const newOpenKeys = new Set(prev);

      items.forEach(({ data }) => {
        if (prev.has(data.key)) {
          newOpenKeys.delete(data.key);
        }
      });

      return newOpenKeys;
    });
  }, []);

  useEffect(() => {
    if (focusId && listRef.current) {
      const index = visibleItems.findIndex(item => item.id === focusId);

      if (index !== -1) {
        listRef.current.scrollToItem(index, 'auto');
      }
    }
  }, [focusId, visibleItems]);

  const handleExpandItems = useCallback(
    (items: NormalizedNode<ItemType>[], deep = 0) => {
      setExpandedKeys(prev => {
        const updatedExpandedKeys = new Set(prev);

        items.forEach(item => {
          const normalizeItem = normalizeTreeItems.get(item.data.key);

          if (normalizeItem) {
            updatedExpandedKeys.add(normalizeItem.id);

            if (deep > 0) {
              getChildrenRecursively(
                normalizeItem.id,
                normalizeTreeItems,
                normalizeItem.level + deep,
              ).forEach(v => updatedExpandedKeys.add(v.id));
            }
          }
        });

        return updatedExpandedKeys;
      });
    },
    [normalizeTreeItems],
  );

  useImperativeHandle(ref, () => ({
    expandAll: () => {
      handleExpandItems(rootNodes, Infinity);
    },
  }));

  const handleSelectItem = useCallback(
    (item: NormalizedNode<ItemType>) => {
      const { key } = item.data;

      if (!selectable || selectedKeys.has(key) || item.data.displayOnly) return;

      setSelectedKeys(new Set([key]));
    },
    [selectable, selectedKeys],
  );

  const handleToggleExpandItem = useCallback(
    (item: NormalizedNode<ItemType>) => {
      const { key } = item.data;
      const isExpanded = expandedKeys.has(key);

      if (isExpanded) {
        handleCollapseItems([item]);
      } else {
        handleExpandItems([item]);
      }
    },
    [expandedKeys, handleCollapseItems, handleExpandItems],
  );

  const handleClickItem = useCallback(
    (args: {
      item: NormalizedNode<ItemType>;
      elKey: 'item' | 'item-expand-el';
      domEvent: React.MouseEvent;
    }) => {
      const { item, elKey, domEvent } = args;

      const node = normalizeTreeItems.get(item.id);

      if (!node || node.data.displayOnly) return;

      const hasChilren = node.childIds.length > 0;

      handleSelectItem(node);

      if (hasChilren) {
        handleToggleExpandItem(node);
      }

      setFocusId(node.id);

      onClick?.({ item: node.data, elKey, domEvent });
    },
    [normalizeTreeItems, handleSelectItem, handleToggleExpandItem, onClick],
  );

  // Helper function to find next focusable item (skip displayOnly items)
  const findNextFocusableItem = useCallback(
    (startIndex: number, direction: 'next' | 'prev'): number => {
      const increment = direction === 'next' ? 1 : -1;

      let index = startIndex + increment;

      while (index >= 0 && index < visibleItems.length) {
        const item = visibleItems[index];

        if (!item.data.displayOnly) {
          return index;
        }

        index += increment;
      }

      return startIndex;
    },
    [visibleItems],
  );

  const findFirstFocusableItem = useCallback((): number => {
    for (let i = 0; i < visibleItems.length; i++) {
      if (!visibleItems[i].data.displayOnly) {
        return i;
      }
    }

    return 0;
  }, [visibleItems]);

  const findLastFocusableItem = useCallback((): number => {
    for (let i = visibleItems.length - 1; i >= 0; i--) {
      if (!visibleItems[i].data.displayOnly) {
        return i;
      }
    }

    return visibleItems.length - 1;
  }, [visibleItems]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (visibleItems.length === 0) return;

      e.preventDefault();

      const currentFocusIndex = visibleItems.findIndex(item => item.id === focusId);

      if (currentFocusIndex === -1) {
        const firstFocusableIndex = findFirstFocusableItem();
        setFocusId(visibleItems[firstFocusableIndex]?.id ?? null);
        return;
      }

      const currentFocusItem = visibleItems[currentFocusIndex];

      switch (e.key) {
        case 'ArrowDown': {
          const nextFocusIndex = findNextFocusableItem(currentFocusIndex, 'next');
          setFocusId(visibleItems[nextFocusIndex].id);
          break;
        }
        case 'ArrowUp': {
          const nextFocusIndex = findNextFocusableItem(currentFocusIndex, 'prev');
          setFocusId(visibleItems[nextFocusIndex].id);
          break;
        }
        case 'ArrowRight': {
          if (currentFocusItem.childIds.length > 0 && !expandedKeys.has(currentFocusItem.id)) {
            handleExpandItems([currentFocusItem]);
          }
          break;
        }
        case 'ArrowLeft': {
          if (currentFocusItem.childIds.length > 0 && expandedKeys.has(currentFocusItem.id)) {
            handleCollapseItems([currentFocusItem]);
          } else if (currentFocusItem?.parentId) {
            // Find parent and ensure it's focusable
            const parentItem = normalizeTreeItems.get(currentFocusItem.parentId);

            if (parentItem && !parentItem.data.displayOnly) {
              setFocusId(currentFocusItem.parentId);
            }
          }
          break;
        }
        case 'Home': {
          const firstFocusableIndex = findFirstFocusableItem();
          setFocusId(visibleItems[firstFocusableIndex].id);
          break;
        }
        case 'End': {
          const lastFocusableIndex = findLastFocusableItem();
          setFocusId(visibleItems[lastFocusableIndex].id);
          break;
        }
        case 'Enter': {
          if (currentFocusItem && !currentFocusItem.data.displayOnly) {
            handleSelectItem(currentFocusItem);

            if (currentFocusItem.childIds.length > 0) {
              handleToggleExpandItem(currentFocusItem);
            }

            onClick?.({
              item: currentFocusItem.data,
              elKey: 'item',
            });
          }
          break;
        }
        default:
          break;
      }
    },
    [
      visibleItems,
      focusId,
      expandedKeys,
      onClick,
      handleExpandItems,
      handleCollapseItems,
      handleSelectItem,
      handleToggleExpandItem,
      findNextFocusableItem,
      findFirstFocusableItem,
      findLastFocusableItem,
      normalizeTreeItems,
    ],
  );

  const handleFocus = (e: React.FocusEvent) => {
    let isClickFont = false;

    for (const itemEl of itemRefs.current.values()) {
      if (isClickFont) break;

      isClickFont = itemEl.isSameNode(e.target) || itemEl.contains(e.target);
    }

    if (!isClickFont && !focusId && visibleItems.length > 0) {
      const firstFocusableIndex = findFirstFocusableItem();
      setFocusId(visibleItems[firstFocusableIndex]?.id || null);
    }
  };

  const itemData: MemoryData = {
    itemSpacing,
    inlineIndent,
    inlinePadding,
    selectedKeys,
    expandedKeys,
    action,
    focusId,
    normalizeTreeItems,
    items: visibleItems.map(item => ({
      ...item,
      data: {
        ...item.data,
        ref: el => itemRefs.current.set(item.id, el),
      },
    })),
    onClick: handleClickItem,
  };

  return (
    <StyledAutoSizer
      className={clsx(CLS.Root.default, className)}
      tabIndex={0}
      onFocus={handleFocus}
      onKeyDown={handleKeyDown}
    >
      {autoSize => (
        <VirtualizedMenuContainer
          ref={listRef}
          className={clsx(CLS.MenuInline.default)}
          itemSize={getItemSize(itemSize, itemSpacing)}
          itemCount={visibleItems.length}
          height={autoSize.height}
          width={autoSize.width}
          innerElementType={innerElementType}
          itemData={itemData}
          useIsScrolling
        >
          {Item}
        </VirtualizedMenuContainer>
      )}
    </StyledAutoSizer>
  );
});

export const MenuInline = memo(MenuInlineInner);
