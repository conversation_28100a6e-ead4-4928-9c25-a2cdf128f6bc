import { ReactNode } from 'react';
import type { MODE } from './config';
import type { NormalizedNode } from '@antscorp/antsomi-ui/es/utils/tree';

export type Mode = (typeof MODE)[keyof typeof MODE];

export type ItemKey = string;

export type ItemType = {
  key: ItemKey;
  className?: string;
  scrollingLabel?: string;
  label?: ReactNode;
  children?: ItemType[];
  errorMessage?: string | undefined;
  disabled?: boolean;
  displayOnly?: boolean;
};

type VirtualizedMenuBaseProps = Partial<{
  items: ItemType[];
  inlineIndent: number;
  inlinePadding: number;
  itemSpacing: number;
  selectable: boolean;
  className: string;
  itemSize: number;
  selected: string | string[];
  expanded: string[];
  onClick: (args: {
    item: ItemType;
    domEvent?: React.MouseEvent;
    elKey: 'item' | 'item-expand-el';
  }) => void;
  action:
    | React.ReactNode
    | ((
        item: ItemType,
        others: {
          expandEl: React.ReactNode;
        },
      ) => React.ReactNode);
}>;

export type VirtualizedMenuBaseHandle = {
  expandAll: () => void;
};

// Menu inline
export type MenuInlineHandle = VirtualizedMenuBaseHandle;

export type MenuInlineProps = VirtualizedMenuBaseProps & {
  mode?: typeof MODE.Inline;
};

// Union
export type VirtualizedMenuHandle = MenuInlineHandle;

export type VirtualizedMenuProps = MenuInlineProps;

export type MemoryData = {
  itemSpacing: number;
  inlineIndent: number;
  inlinePadding: number;
  selectedKeys: Set<string>;
  expandedKeys: Set<string>;
  items: NormalizedNode<
    ItemType & {
      ref: (el: HTMLLIElement) => void;
    }
  >[];
  normalizeTreeItems: Map<string, NormalizedNode<ItemType>>;
  action: MenuInlineProps['action'];
  focusId: string | null;
  onClick: (args: {
    item: NormalizedNode<ItemType>;
    index: number;
    elKey: 'item' | 'item-expand-el';
    domEvent: React.MouseEvent;
  }) => void;
};
