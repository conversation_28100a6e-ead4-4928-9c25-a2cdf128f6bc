import React, { useCallback, useState } from 'react';
import { Space, Typography } from 'antd';
import Icon from '@antscorp/icons';
import { StyledInputDropdownRoot } from './styled';
import clsx from 'clsx';
import { InputDropdownProps } from './types';
import './style.scss';

export { Dropdown } from 'antd';

const DefaultExpandIcon = <Icon type="icon-ants-expand-more" />;

export const InputDropdown = (props: InputDropdownProps) => {
  const {
    children,
    disabled = false,
    className,
    trigger = ['click'],
    menu = { items: [] },
    width = '100%',
    expandIcon = DefaultExpandIcon,
    onOpenChange = () => {},
    overlayClassName,
    ...rest
  } = props;

  const [enableTooltip, setEnableTooltip] = useState(true);

  const renderChildren = useCallback(() => {
    if (typeof children === 'string') {
      return (
        <Typography.Text
          ellipsis={
            enableTooltip
              ? {
                  tooltip: {
                    arrow: false,
                  },
                }
              : true
          }
        >
          {children}
        </Typography.Text>
      );
    }

    return children;
  }, [children, enableTooltip]);

  const handleOpen = (...args: Parameters<typeof onOpenChange>) => {
    const [open] = args;

    setEnableTooltip(!open);

    onOpenChange(...args);
  };

  return (
    <StyledInputDropdownRoot
      menu={menu}
      trigger={trigger}
      disabled={disabled}
      {...rest}
      className={clsx({ disabled }, className, 'antsomi-input-dropdown')}
      overlayClassName={clsx(overlayClassName, 'antsomi-input-dropdown-overlay')}
      onOpenChange={handleOpen}
    >
      <Space style={{ width }}>
        {renderChildren()}

        {expandIcon}
      </Space>
    </StyledInputDropdownRoot>
  );
};
