// Libraries
import styled from 'styled-components';

// Components
import { Flex } from '@antscorp/antsomi-ui/es/components/atoms/Flex';
import { globalToken, THEME } from '@antscorp/antsomi-ui/es/constants';

export const TemplateSaveAsStyled = styled(Flex)<{ $skeleton?: boolean }>`
  /* max-width: 1177px; */
  height: 100%;

  position: relative;

  /* &:has(canvas),
  &:has(.image-upload-zone) { */
  &:has(.is-editing) {
    &:after {
      content: '';
      position: absolute;
      inset: 0;
      background-color: #fff;
      opacity: 0.5;
      z-index: 20;
    }
  }

  .save-options-container {
    padding: 15px 0;
    margin-bottom: 20px;
    border-bottom: 1px dashed #e5e5e5;
  }

  div:has(> .template-container) {
    height: 100%;
  }

  .template-container {
    flex: 1 0 680px;
    /* flex: 1 1 60%; */
    max-height: 60vh;
    padding-right: 28px;
    overflow-y: auto;
  }

  .template-container:has(+ .share-access-container) {
    flex-grow: 0;
  }

  .categories-container {
    margin-bottom: 20px;
  }

  /* &:not(:has(.share-access-container)) .categories-container {
    width: 60%;
  } */

  .share-access-container {
    flex: 1 1 40%;
    padding-left: 20px;
    margin-left: 5px;
    /* max-height: 60vh; */
    overflow-y: auto;
    border-left: 1px solid #e5e5e5;

    & > div {
      max-width: 400px;
    }
  }

  /* .antsomi-form-item .antsomi-form-item-row */
  .field-container {
    gap: 10px;
  }

  /* NOTE: Style for ant form */
  .antsomi-form-item {
    /* margin: 0; */
  }
  .antsomi-form-item-row {
    gap: 10px;
    .antsomi-form-item-label {
      flex: 0 0 150px;
    }
    .antsomi-form-item-control-input {
      width: 330px;
    }
  }

  .field-title {
    font-size: 12px;
    flex: 0 0 150px;
    span {
      color: ${THEME.token?.red6};
    }

    &--start {
      padding-top: 6px;
    }
    &--middle {
      display: flex;
      align-items: center;
    }
  }

  div:has(> .field-input) {
    width: 330px;
  }

  .field-input.antsomi-select {
    margin-bottom: 24px;
  }

  .category-container {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    min-width: 510px;
    max-width: 510px;
    & > button {
      height: 36px;
      box-sizing: border-box;
      padding: 0 10px;
    }
    /* display: grid;
    grid-template-columns: repeat(2, minmax(100px, 250px)); */
  }

  .selected-category {
    /* width: 240px; */
    overflow: hidden;
    box-sizing: border-box;
    width: 250px;
    height: 36px;
    border: 1px solid #b8cfe6;
    padding: 0 5px;
    border-radius: ${globalToken?.borderRadiusXL}px;

    & > .remove-btn {
      cursor: pointer;
    }
  }
`;

// export const FormStyled = styled(Form)`
//   position: relative;

//   &::after {
//     position: absolute;
//     inset: 0;
//     background-color: rgba(255, 255, 255, 0.5);
//   }
// `;
