/* eslint-disable no-console */
// Libraries
import { Meta, StoryObj } from '@storybook/react';
import { useMemo, useState } from 'react';

// Components
import dayjs from 'dayjs';
import { VALUE_TYPES } from '../AdvancedPicker/constants';
import { AdvancedRangePicker } from './AdvancedRangePicker';
import { TTimeRange } from './types';

export default {
  title: 'Molecules/DatePicker/AdvancedRangePicker',
  component: AdvancedRangePicker,
  argTypes: {
    valueType: {
      name: 'valueType',
      defaultValue: 'YEAR_MONTH_DAY',
      table: {
        type: { summary: 'ValueTypes' },
      },
      control: {
        type: 'select',
      },
      options: Object.keys(VALUE_TYPES),
    },
  },
  parameters: {
    docs: {
      description: {
        component: 'To select a time range dynamic date.',
      },
    },
  },
} as Meta<typeof AdvancedRangePicker>;

export const Template: StoryObj<typeof AdvancedRangePicker> = {
  args: {
    useFormatMapping: true,
    showCalculationTypeCondition: {
      dateType: {
        today: ['minus'],
      },
    },
  },
};

export const Basic: StoryObj<typeof AdvancedRangePicker> = {
  render: () => {
    // State
    const [timeRange, setTimeRange] = useState<TTimeRange>({
      startDate: {
        date: '',
        calculationDate: 'years',
        value: 1,
        calculationType: 'minus',
        dateType: 'today',
      },
      endDate: {
        date: '',
        calculationDate: 'days',
        value: 1,
        calculationType: 'minus',
        dateType: 'today',
      },
    });

    console.log('🚀 ~ timeRange:', timeRange);

    // Memo
    const errorMessage = useMemo(() => {
      const { date: startDate } = timeRange.startDate;
      const { date: endDate } = timeRange.endDate;

      if (dayjs(endDate).diff(dayjs(startDate)) < 0) {
        return 'End date is earlier than start date';
      }

      return '';
    }, [timeRange.startDate, timeRange.endDate]);

    // Handlers
    const onChangeAdvancedRangePicker = ({ timeRange, mode: _mode }) => {
      try {
        setTimeRange(previousTimeRange => ({ ...previousTimeRange, ...timeRange }));
      } catch (error) {
        // Handle Error
      }
    };

    return (
      <AdvancedRangePicker
        timeRange={timeRange}
        errorMessage={errorMessage}
        onChange={onChangeAdvancedRangePicker}
      />
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'The basic example about `AdvancedRangePicker` Component',
      },
      source: {
        code: `
        // Libraries
        import { React } from 'react';

        // Components
        import { DatePicker, TAdvancedRangePickerTimeRange } from '@antscorp/antsomi-ui';

        const { AdvancedRangePicker } = DatePicker;

        export const App = () => {
          // State
          const [timeRange, setTimeRange] = useState<TAdvancedRangePickerTimeRange>({
            startDate: {
              date: '',
              calculationDate: 'years',
              value: 1,
              calculationType: 'minus',
              dateType: 'today',
            },
            endDate: {
              date: '',
              calculationDate: 'days',
              value: 1,
              calculationType: 'minus',
              dateType: 'today',
            },
          });

          // Memo
          const errorMessage = useMemo(() => {
            const { date: startDate } = timeRange.startDate;
            const { date: endDate } = timeRange.endDate;

            if (dayjs(endDate).diff(dayjs(startDate)) < 0) {
              return 'End date is earlier than start date';
            }

            return '';
          }, [timeRange.startDate, timeRange.endDate]);

          // Handlers
          const onChangeAdvancedRangePicker = ({ timeRange, mode: _mode }) => {
            try {
              setTimeRange((previousTimeRange) => ({ ...previousTimeRange, ...timeRange }));
            } catch (error) {
              // Handle Error
            }
          };

          return (
            <AdvancedRangePicker
              timeRange={timeRange}
              errorMessage={errorMessage}
              onChange={onChangeAdvancedRangePicker}
            />
          );
        };
        `,
        type: 'auto',
      },
    },
  },
};

export const HideTimeLabel: StoryObj<typeof AdvancedRangePicker> = {
  render: () => {
    // State
    const [timeRange, setTimeRange] = useState<TTimeRange>({
      startDate: {
        date: '',
        calculationDate: 'years',
        value: 1,
        calculationType: 'minus',
        dateType: 'today',
      },
      endDate: {
        date: '',
        calculationDate: 'days',
        value: 1,
        calculationType: 'minus',
        dateType: 'today',
      },
    });

    // Handlers
    const onChangeAdvancedRangePicker = ({ timeRange, mode: _mode }) => {
      try {
        setTimeRange(previousTimeRange => ({ ...previousTimeRange, ...timeRange }));
      } catch (error) {
        // Handle Error
      }
    };

    return (
      <AdvancedRangePicker
        showTime={false}
        timeRange={timeRange}
        onChange={onChangeAdvancedRangePicker}
      />
    );
  },

  parameters: {
    docs: {
      description: {
        story: "Don't want to show time in popup",
      },
      source: {
        code: `
        // Libraries
        import { React } from 'react';

        // Components
        import { DatePicker, TAdvancedRangePickerTimeRange } from '@antscorp/antsomi-ui';

        const { AdvancedRangePicker } = DatePicker;

        export const App = () => {
          // State
          const [timeRange, setTimeRange] = useState<TAdvancedRangePickerTimeRange>({
            startDate: {
              date: '',
              calculationDate: 'years',
              value: 1,
              calculationType: 'minus',
              dateType: 'today',
            },
            endDate: {
              date: '',
              calculationDate: 'days',
              value: 1,
              calculationType: 'minus',
              dateType: 'today',
            },
          });

          // Handlers
          const onChangeAdvancedRangePicker = ({ timeRange, mode: _mode }) => {
            try {
              setTimeRange((previousTimeRange) => ({ ...previousTimeRange, ...timeRange }));
            } catch (error) {
              // Handle Error
            }
          };

          return (
            <AdvancedRangePicker
              showTime={false}
              timeRange={timeRange}
              onChange={onChangeAdvancedRangePicker}
            />
          );
        };
        `,
        type: 'auto',
      },
    },
  },
};

export const ShowCalculationTypeCondition: StoryObj<typeof AdvancedRangePicker> = {
  render: () => {
    // State
    const [timeRange, setTimeRange] = useState<TTimeRange>({
      startDate: {
        date: '',
        calculationDate: 'years',
        value: 1,
        calculationType: 'minus',
        dateType: 'today',
      },
      endDate: {
        date: '',
        calculationDate: 'days',
        value: 1,
        calculationType: 'minus',
        dateType: 'today',
      },
    });

    // Handlers
    const onChangeAdvancedRangePicker = ({ timeRange, mode: _mode }) => {
      try {
        setTimeRange(previousTimeRange => ({ ...previousTimeRange, ...timeRange }));
      } catch (error) {
        // Handle Error
      }
    };

    return (
      <AdvancedRangePicker
        showCalculationTypeCondition={{
          dateType: {
            today: ['minus'],
          },
        }}
        showTime={false}
        timeRange={timeRange}
        onChange={onChangeAdvancedRangePicker}
      />
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Here is an example to show the calculation type by dateType',
      },
    },
  },
};
