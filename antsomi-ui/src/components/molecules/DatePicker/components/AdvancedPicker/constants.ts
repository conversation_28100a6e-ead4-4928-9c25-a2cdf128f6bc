// Types
// import { t } from 'i18next';
import { TAdvancedType, TCalculationDate, TDateType, ValueTypes } from './types';
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';

const { t } = i18nInstance;

export const DATE_TYPES: Array<{ value: TDateType; label: string }> = [
  { value: 'today', label: t('datePicker.dateTypes.today') },
  { value: 'first_day_of_week_mon_sun', label: t('datePicker.dateTypes.firstDayOfWeekMonSun') },
  { value: 'first_day_of_week_sun_sat', label: t('datePicker.dateTypes.firstDayOfWeekSunSat') },
  { value: 'first_day_of_month', label: t('datePicker.dateTypes.firstDayOfMonth') },
  { value: 'first_day_of_quarter', label: t('datePicker.dateTypes.firstDayOfQuarter') },
  { value: 'first_day_of_year', label: t('datePicker.dateTypes.firstDayOfYear') },
  { value: 'last_day_of_week_mon_sun', label: t('datePicker.dateTypes.lastDayOfWeekMonSun') },
  { value: 'last_day_of_week_sun_sat', label: t('datePicker.dateTypes.lastDayOfWeekSunSat') },
  { value: 'last_day_of_month', label: t('datePicker.dateTypes.lastDayOfMonth') },
  { value: 'last_day_of_quarter', label: t('datePicker.dateTypes.lastDayOfQuarter') },
  { value: 'last_day_of_year', label: t('datePicker.dateTypes.lastDayOfYear') },
  { value: 'fixed', label: t('datePicker.dateTypes.fixed') },
];

export const DATE_TYPE_MAPPING = {
  first_day_of_week_mon_sun: 'isoWeek',
  first_day_of_week_sun_sat: 'week',
  first_day_of_month: 'month',
  first_day_of_quarter: 'quarter',
  first_day_of_year: 'year',
  last_day_of_week_mon_sun: 'isoWeek',
  last_day_of_week_sun_sat: 'week',
  last_day_of_month: 'month',
  last_day_of_quarter: 'quarter',
  last_day_of_year: 'year',
};

export const CALCULATION_TYPES: Array<{ value: 'minus' | 'plus'; label: string }> = [
  { value: 'minus', label: t('global.minus') },
  { value: 'plus', label: t('global.plus') },
];

export const CALCULATION_DATES: Array<{ value: TCalculationDate; label: string }> = [
  { value: 'seconds', label: t('datePicker.seconds') },
  { value: 'minutes', label: t('datePicker.minutes') },
  { value: 'hours', label: t('datePicker.hours') },
  { value: 'days', label: t('datePicker.days') },
  { value: 'weeks', label: t('datePicker.weeks') },
  { value: 'months', label: t('datePicker.months') },
  { value: 'quarters', label: t('datePicker.quarters') },
  { value: 'years', label: t('datePicker.years') },
] as const;

// type CalculationDateValues = (typeof CALCULATION_DATES)[number]['value'];

export const VALUE_TYPES = {
  INPUT_TEXT: 'INPUT_TEXT',
  INPUT_NUMBER: 'INPUT_NUMBER',
  MATCHES_ANY: 'MATCHES_ANY',
  YEAR: 'YEAR',
  YEAR_QUARTER: 'YEAR_QUARTER',
  YEAR_MONTH: 'YEAR_MONTH',
  YEAR_WEEK: 'YEAR_WEEK',
  YEAR_MONTH_DAY: 'YEAR_MONTH_DAY',
  YEAR_MONTH_DAY_HOUR: 'YEAR_MONTH_DAY_HOUR',
  YEAR_MONTH_DAY_MINUTE: 'YEAR_MONTH_DAY_MINUTE',
  YEAR_MONTH_DAY_SECOND: 'YEAR_MONTH_DAY_SECOND',
  QUARTER: 'QUARTER',
  MONTH: 'MONTH',
  WEEK: 'WEEK',
  MONTH_DAY: 'MONTH_DAY',
  DAY_OF_WEEK: 'DAY_OF_WEEK',
  DAY: 'DAY',
  HOUR: 'HOUR',
  MINUTE: 'MINUTE',
  INPUT_TEXT_NO_FIELD: 'INPUT_TEXT_NO_FIELD',
} as const;

export const DEFAULT_DATE_FORMAT = 'YYYYMMDDHHmmss';
export const DEFAULT_TIME_FORMAT = 'HH:mm:ss';
export const DEFAULT_DATE_DISPLAY_FORMAT = 'DD/MM/YYYY HH:mm:ss';

export const TIME_PICKER_TYPE = {
  DATE_TIME: 'DateTime',
  DATE_HOUR: 'DateHour',
  DATE_HOUR_MINUTE: 'DateHourMinute',
  DATE: 'Date',
};

export const YEAR_PICKER_TYPE = {
  YEAR: 'year',
  MONTH: 'month',
  QUARTER: 'quarter',
};

export const ADVANCED_PICKER_TYPE: Record<string, { value: TAdvancedType; label: string }> = {
  START_DATE: {
    value: 'startDate',
    label: 'datePicker.startDate',
  },
  END_DATE: {
    value: 'endDate',
    label: 'datePicker.endDate',
  },
};

export const MONTH_LABEL_FULL = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];
export const DAY_LABEL_SHORT = ['Sun', 'Mon', 'Tue', 'Wed', 'Thur', 'Fri', 'Sat'];

export const WEEK_ARR = Array.from({ length: 53 }, (_, i) => i + 1);

export const MONTH_DAY_31 = Array.from({ length: 31 }, (_, i) => i + 1);
export const MONTH_DAY_30 = Array.from({ length: 30 }, (_, i) => i + 1);
export const MONTH_DAY_FEB = Array.from({ length: 29 }, (_, i) => i + 1);
export const MONTH_31 = [0, 2, 4, 6, 7, 9, 11];
export const MONTH_30 = [3, 5, 8, 10];
export const DAY_OF_MONTH = [
  MONTH_DAY_31,
  MONTH_DAY_FEB,
  MONTH_DAY_31,
  MONTH_DAY_30,
  MONTH_DAY_31,
  MONTH_DAY_30,
  MONTH_DAY_31,
  MONTH_DAY_31,
  MONTH_DAY_30,
  MONTH_DAY_31,
  MONTH_DAY_30,
  MONTH_DAY_31,
];

/** The year with full 366 days of year, use for select day/day of month with include edge case (February 29th) */
export const ANCHOR_LEAP_YEAR = 2024;

export const HIDE_TIME_LABEL_ARR = [
  'YEAR',
  'YEAR_MONTH',
  'YEAR_MONTH_DAY',
  'QUARTER',
  'MONTH',
  'WEEK',
  'HOUR',
  'MINUTE',
  'MONTH_DAY',
  'DAY_OF_WEEK',
  'DAY',
];

// export const CALCULATION_DATES = [
//   { value: 'seconds', label: 'datePicker.seconds' },
//   { value: 'minutes', label: 'datePicker.minutes' },
//   { value: 'hours', label: 'datePicker.hours' },
//   { value: 'days', label: 'datePicker.days' },
//   { value: 'weeks', label: 'datePicker.weeks' },
//   { value: 'months', label: 'datePicker.months' },
//   { value: 'quarters', label: 'datePicker.quarters' },
//   { value: 'years', label: 'datePicker.years' },
// ];

// Granularity according to value type (semantic)
export const GRANULARITY_MAPPING: { [key in ValueTypes]: TCalculationDate[] } = {
  YEAR: ['years'],
  YEAR_QUARTER: ['years', 'quarters'],
  YEAR_MONTH: ['years', 'quarters', 'months'],
  YEAR_WEEK: ['years', 'quarters', 'months', 'weeks'],
  YEAR_MONTH_DAY: ['years', 'quarters', 'months', 'weeks', 'days'],
  YEAR_MONTH_DAY_HOUR: ['years', 'quarters', 'months', 'weeks', 'days', 'hours'],
  YEAR_MONTH_DAY_MINUTE: ['years', 'quarters', 'months', 'weeks', 'days', 'hours', 'minutes'],
  YEAR_MONTH_DAY_SECOND: [
    'years',
    'quarters',
    'months',
    'weeks',
    'days',
    'hours',
    'minutes',
    'seconds',
  ],
  QUARTER: ['years', 'quarters'],
  MONTH: ['years', 'quarters', 'months'],
  WEEK: ['years', 'quarters', 'months', 'weeks'],
  MONTH_DAY: ['years', 'quarters', 'months', 'weeks', 'days'],
  DAY_OF_WEEK: ['years', 'quarters', 'months', 'weeks', 'days'],
  /** Day of Month */
  DAY: ['years', 'quarters', 'months', 'weeks', 'days'],
  HOUR: ['years', 'quarters', 'months', 'weeks', 'days', 'hours'],
  MINUTE: ['years', 'quarters', 'months', 'weeks', 'days', 'hours', 'minutes'],
  INPUT_NUMBER: ['years', 'months', 'weeks', 'days'],
  INPUT_TEXT: ['years', 'months', 'weeks', 'days'],
  INPUT_TEXT_NO_FIELD: ['years', 'months', 'weeks', 'days'],
  MATCHES_ANY: ['years', 'months', 'weeks', 'days'],
};
