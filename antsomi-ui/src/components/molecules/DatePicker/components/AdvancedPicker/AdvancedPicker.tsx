/* eslint-disable react/no-unused-prop-types */
// Libraries
import React, { useEffect, useMemo, useCallback, useState, useImperativeHandle } from 'react';
import { Tooltip, theme, DatePicker } from 'antd';
import dayjs from 'dayjs';
import clsx from 'clsx';
import type { Dayjs } from 'dayjs';
import type { CellRenderInfo } from 'rc-picker/es/interface';

// Hooks
import { useDeepCompareEffect } from '../../../../../hooks';

// Icons
import Icon from '@antscorp/icons';
import { EventIcon } from '../../../../icons';

// Components
import {
  InputNumber,
  Divider,
  Space,
  Button,
  Typography,
} from '@antscorp/antsomi-ui/es/components/atoms';
import { Select, Dropdown } from '@antscorp/antsomi-ui/es/components';
import { ErrorMessage } from '../ErrorMessage/ErrorMessage';
import { DropdownLabel } from '../DropdownLabel/DropdownLabel';

// Utils
import { handleError, reorder } from '@antscorp/antsomi-ui/es/utils';
import {
  calculationDateAdvanced,
  getCellRender,
  getFormatDisplay,
  getPickerRender,
  getTimePickerRender,
} from './utils';

// Styled
import {
  DatePickerCustomInput,
  DatePickerHeader,
  DropdownContent,
  DropdownHeader,
  DatePickerFooter,
  DropdownFooter,
  CalendarIconWrapper,
  InputStyled,
  TimeLabel,
} from './styled';

// Constants
import {
  CALCULATION_DATES,
  CALCULATION_TYPES,
  DATE_TYPES,
  DEFAULT_DATE_FORMAT,
  VALUE_TYPES,
  ADVANCED_PICKER_TYPE,
  WEEK_ARR,
  DAY_LABEL_SHORT,
  MONTH_LABEL_FULL,
  DAY_OF_MONTH,
  ANCHOR_LEAP_YEAR,
  GRANULARITY_MAPPING,
} from './constants';
import { globalToken, THEME } from '@antscorp/antsomi-ui/es/constants';

// Types
import {
  TAdvancedType,
  TCalculationType,
  TDateType,
  TOperatorKey,
  TOption,
  TShowCalculationTypeCondition,
  ValueTypes,
} from './types';
import { RangePickerProps } from 'antd/es/date-picker';
import { useOutsideClick } from '@antscorp/antsomi-ui/es/hooks/useOutsideClick';

const isDisabledSpecificTime = (
  current: number | Dayjs,
  info: CellRenderInfo<Dayjs>,
  disabledTime?: (date?: Dayjs) => {
    disabledHours: () => number[];
    disabledMinutes: () => number[];
    disabledSeconds: () => number[];
  },
  date?: Dayjs,
) => {
  if (typeof current !== 'number' || !disabledTime) return false;

  switch (info.subType) {
    case 'hour':
      return disabledTime(date).disabledHours().includes(current);
    case 'minute':
      return disabledTime(date).disabledMinutes().includes(current);
    case 'second':
      return disabledTime(date).disabledSeconds().includes(current);
    default:
      return false;
  }
};

export interface AdvancedPickerProps {
  label?: string;
  valueType?: ValueTypes;
  disabled?: boolean;
  dateTypeKeysShow?: string[];
  showCalculationTypeCondition?: TShowCalculationTypeCondition;
  calculationTypeKeysShow?: TCalculationType[];
  defaultDateTypeKey?: string;
  option?: TOption;
  operatorKey?: TOperatorKey;
  type?: TAdvancedType;
  /**
   * This is uncontrolled value (default value) and will not update if change outside
   * only update when re-render component
   * use ref.forceUpdate() to control value inside
   */
  date?: string;
  format?: string;
  /**
   * @deprecated this props has no effect, now input display auto calculate base on prop `valueType`
   */
  formatInputDisplay?: string;
  inputStyle?: React.CSSProperties;
  popupStyle?: React.CSSProperties;
  disableAfterDate?: string;
  disableBeforeDate?: string;
  errorMessage?: string;
  /**
   * @deprecated showTime (DropdownLabel component) now base on valueType
   */
  showTime?: boolean;
  timezone?: string;
  isViewMode?: boolean;
  /**
   * 
   * @example const disabledTime = (date?: Dayjs) => {
      if (date?.get('date') !== 31)
        return {
          disabledHours: () => [],
          disabledMinutes: () => [],
          disabledSeconds: () => [],
        };

      return {
        disabledHours: () => range(0, 24).splice(4, 20),
        disabledMinutes: () => range(30, 60),
        disabledSeconds: () => [55, 56],
      };
    };
   */
  disabledTime?: (date?: Dayjs) => {
    disabledHours: () => number[];
    disabledMinutes: () => number[];
    disabledSeconds: () => number[];
  };
  /**
   * Only show fixed calendar (hide dynamic and )
   */
  onlyShowFixed?: boolean;
  onUpdatedNewDate?: ({ date, option }: { date: string; option: TOption }) => void;
  onApply?: ({ date, option }: { date: string; option: TOption }) => void;
}

const PATH =
  '@antscorp/antsomi-ui/es/components/molecules/DatePicker/components/Advanced/DatePickerAdvanced.tsx';

const { useToken } = theme;
const { Text } = Typography;

export interface AdvancedPickerHandle {
  forceUpdate: (date: string) => void;
}

export const AdvancedPicker = React.forwardRef<AdvancedPickerHandle, AdvancedPickerProps>(
  (props, ref) => {
    // Props
    const {
      label = '',
      inputStyle = { width: 120 },
      popupStyle = { borderRadius: THEME.token?.borderRadiusLG ?? 10 },
      dateTypeKeysShow = [],
      calculationTypeKeysShow,
      showCalculationTypeCondition,
      defaultDateTypeKey,
      valueType = VALUE_TYPES.YEAR_MONTH_DAY,
      option: propsOption,
      operatorKey = 'after',
      type = ADVANCED_PICKER_TYPE.START_DATE.value,
      date: propsDate = dayjs().format(DEFAULT_DATE_FORMAT),
      format = DEFAULT_DATE_FORMAT,
      errorMessage,
      disableAfterDate,
      disableBeforeDate,
      disabled,
      timezone,
      isViewMode,
      onlyShowFixed,
      disabledTime,
      onUpdatedNewDate,
      onApply,
    } = props;

    // Memo
    const newDateTypes = useMemo(() => {
      if (dateTypeKeysShow && dateTypeKeysShow.length) {
        const draftDateTypes = DATE_TYPES.filter(dateType =>
          dateTypeKeysShow.some(key => key === dateType.value),
        );

        return draftDateTypes;
      }

      if (defaultDateTypeKey) {
        const index = DATE_TYPES.findIndex(dateType => dateType.value === defaultDateTypeKey);

        if (index !== -1) {
          return reorder(DATE_TYPES, index, 0);
        }
      }

      return DATE_TYPES;
    }, [dateTypeKeysShow, defaultDateTypeKey]);

    const newCalculationDates = useMemo(
      () =>
        CALCULATION_DATES.filter(calculationDate =>
          GRANULARITY_MAPPING[valueType!].includes(calculationDate.value),
        ),
      [valueType],
    );

    const { formatDisplay } = getFormatDisplay(operatorKey, type, valueType);

    // State
    const [state, setState] = useState({
      isOpen: false,
      option: {
        dateType: newDateTypes[0],
        calculationType: CALCULATION_TYPES[0],
        calculationDate: newCalculationDates[0],
        value: 0,
      },
      optionSelected: {
        dateType: newDateTypes[0],
        calculationType: CALCULATION_TYPES[0],
        calculationDate: newCalculationDates[0],
        value: 0,
      },
      date: dayjs().tz(timezone).format(format),
      dateDisplay: dayjs().tz(timezone).format(format),
    });
    const { isOpen, option, date, dateDisplay } = state;

    useImperativeHandle(
      ref,
      () => ({
        forceUpdate(date) {
          const newDate = dayjs(date, format, true).isValid()
            ? dayjs(date, format).format(format)
            : date;

          setState(state => ({
            ...state,
            date: newDate,
            dateDisplay: newDate,
          }));
        },
      }),
      [format],
    );

    const isShowFixed = option.dateType.value === 'fixed' || onlyShowFixed;

    const currDate = dayjs(date, format);
    const currWeek = currDate.isoWeek();
    const currDay = currDate.day();
    const currDayOfMonth = currDate.date();
    const currMonth = currDate.month();

    const [currMonthSelected, setCurrMonthSelected] = useState(currMonth);

    const newCalculationTypes = useMemo(() => {
      if (calculationTypeKeysShow && calculationTypeKeysShow.length) {
        const draftCalculationTypes = CALCULATION_TYPES.filter(calculationType =>
          calculationTypeKeysShow.some(key => key === calculationType.value),
        );

        return draftCalculationTypes;
      }

      if (showCalculationTypeCondition && option.dateType) {
        if (showCalculationTypeCondition.dateType[option.dateType.value]) {
          const draftCalculationTypes = showCalculationTypeCondition.dateType[
            option.dateType.value
          ]?.map(dateType => CALCULATION_TYPES.find(({ value }) => value === dateType));

          if (!draftCalculationTypes.find(({ value }) => value === option.calculationType.value)) {
            setState(state => ({
              ...state,
              option: {
                ...state.option,
                calculationType: draftCalculationTypes[0],
              },
            }));
          }

          return draftCalculationTypes;
        }
      }

      return CALCULATION_TYPES;
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [calculationTypeKeysShow, showCalculationTypeCondition, option.dateType]);

    // Hooks
    const { token } = useToken();

    // Variables
    const contentStyle = {
      backgroundColor: token.colorBgElevated,
      borderRadius: token.borderRadius,
      boxShadow: token.boxShadowSecondary,
    };

    const selectedDateTitle = useMemo(
      () => dayjs(dateDisplay, format).format(formatDisplay),
      [dateDisplay, format, formatDisplay],
    );

    const onChangeOption = (params: Record<string, any>) => {
      try {
        setState(state => ({
          ...state,
          option: {
            ...state.option,
            ...params,
          },
        }));
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onChangeOption',
          args: {},
        });
      }
    };

    // Effects
    useEffect(() => {
      try {
        if (isOpen) {
          setState(state => ({
            ...state,
            option: state.optionSelected,
            date: state.dateDisplay,
          }));
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'useEffect',
          args: {},
        });
      }
    }, [isOpen]);

    useDeepCompareEffect(() => {
      try {
        if (option.dateType.value !== 'fixed') {
          const newDate = calculationDateAdvanced(
            option.dateType.value,
            option.calculationType.value,
            option.calculationDate.value,
            option.value,
            format,
            timezone,
          );

          setState(state => ({ ...state, date: newDate }));
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'useEffectWithDependencies[state.option, format]',
          args: { option, format },
        });
      }
    }, [option, format, timezone]);

    useDeepCompareEffect(() => {
      const DEFAULT_OPTION = onlyShowFixed
        ? {
            dateType: 'fixed',
            calculationDate: 'days',
            calculationType: 'minus',
            value: 0,
          }
        : {};

      try {
        if (propsOption || onlyShowFixed) {
          const {
            dateType,
            calculationType,
            calculationDate,
            value = 0,
          } = propsOption || DEFAULT_OPTION;
          const newDateTypes = DATE_TYPES;

          const newDateType = newDateTypes.find(dType => dType.value === dateType);
          const newCalculationType = CALCULATION_TYPES.find(
            calType => calType.value === calculationType,
          );
          const newCalculationDate =
            newCalculationDates.find(calDate => calDate.value === calculationDate) ||
            newCalculationDates[0];
          let newDate: any = '';

          if (newDateType) {
            if (newDateType.value === 'fixed') {
              // newDate = dayjs(innerDate, format, true).isValid()
              //   ? dayjs(innerDate, format).format(format)
              //   : innerDate;
              newDate = dayjs(propsDate, format, true).isValid()
                ? dayjs(propsDate, format).format(format)
                : propsDate;

              setState(state => ({
                ...state,
                date: newDate,
                dateDisplay: newDate,
                optionSelected: { ...option, dateType: newDateType },
              }));
            } else {
              if (newCalculationDate && newCalculationType) {
                const date = calculationDateAdvanced(
                  newDateType.value,
                  newCalculationType.value,
                  newCalculationDate.value,
                  value,
                  format,
                  timezone,
                );

                setState(state => ({
                  ...state,
                  dateDisplay: date,
                  optionSelected: {
                    dateType: newDateType,
                    calculationDate: newCalculationDate,
                    calculationType: newCalculationType,
                    value,
                  },
                }));

                newDate = date;
              }
            }
          }

          if (typeof onUpdatedNewDate === 'function' && propsDate !== newDate) {
            onUpdatedNewDate({
              date: newDate,
              option: {
                dateType: newDateType!.value as TDateType,
                calculationDate: newDateType!.value === 'fixed' ? 'days' : newCalculationDate.value,
                calculationType:
                  newDateType!.value === 'fixed'
                    ? ''
                    : (newCalculationType!.value as TCalculationType),
                value: newDateType!.value === 'fixed' ? 0 : value,
              },
            });
          }
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'useEffectPropsOption',
          args: {},
        });
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [propsOption, newCalculationDates, propsDate, format]);

    // Handlers
    const disableDate = useCallback(
      (current: Dayjs | number) =>
        typeof current === 'number'
          ? current > dayjs(disableAfterDate, format).date() ||
            current < dayjs(disableBeforeDate, format).date()
          : current > dayjs(disableAfterDate, format) || current < dayjs(disableBeforeDate, format),
      [disableAfterDate, disableBeforeDate, format],
    );

    const toggleOpenDropdown = (isOpen?: boolean) => {
      try {
        setState(() => ({
          ...state,
          // isOpen: !state.isOpen,
          isOpen: isOpen != null ? isOpen : !state.isOpen,
        }));
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'toggleOpenDropdown',
          args: {},
        });
      }
    };

    const { ref: refDropdown } = useOutsideClick(() => toggleOpenDropdown(false));

    const onClickApply = () => {
      try {
        const draftOption: TOption = {
          dateType: option.dateType.value,
          calculationDate: isShowFixed ? 'days' : option.calculationDate.value,
          calculationType: isShowFixed ? 'minus' : option.calculationType.value,
          value: isShowFixed ? 0 : option.value,
        };

        setState(state => ({
          ...state,
          optionSelected: option,
          dateDisplay: date,
          isOpen: false,
        }));

        if (typeof onApply === 'function') {
          onApply({
            date,
            option: draftOption,
          });
        }
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onClickApply',
          args: {},
        });
      }
    };

    const onClickNow = () => {
      try {
        const currentDate = dayjs();
        setState(state => ({ ...state, date: currentDate.format(format) }));
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'onClickNow',
          args: {},
        });
      }
    };

    const renderDropdownFooter = () => (
      <>
        <Divider />

        <DropdownFooter>
          <Space>
            <Button type="primary" onClick={() => onClickApply()}>
              Apply
            </Button>
            <Button type="default" onClick={() => toggleOpenDropdown(false)}>
              Cancel
            </Button>
          </Space>

          {isShowFixed && <Button onClick={() => onClickNow()}>Now</Button>}
        </DropdownFooter>
      </>
    );

    const renderDateTypeOptions = () =>
      onlyShowFixed ? null : (
        <Select
          getPopupContainer={triggerNode => triggerNode.parentNode}
          options={newDateTypes}
          value={option.dateType.value}
          onChange={value => {
            const dateType = newDateTypes.find(dateType => dateType.value === value);

            onChangeOption({ dateType });
            // toggleOpenDropdown(true);

            // setTimeout(() => {
            //   onChangeOption({ dateType });
            // }, 100);
          }}
        />
      );

    // NOTE: valueType: 'WEEK'; 'DAY_OF_WEEK'; 'MONTH_DAY'; 'DAY'
    const customPickerArr = {
      WEEK: WEEK_ARR,
      DAY_OF_WEEK: DAY_LABEL_SHORT,
      MONTH_DAY: DAY_OF_MONTH[currMonthSelected],
      DAY: DAY_OF_MONTH[0],
    };

    const dropdownRender = () => {
      const getCellSelected = (item, index) => {
        switch (valueType) {
          case 'WEEK':
            return currWeek === item;
          case 'DAY_OF_WEEK':
            return index === currDay;
          case 'MONTH_DAY':
            return item === currDayOfMonth && currMonth === currMonthSelected;
          case 'DAY':
            return item === currDayOfMonth;
          default:
            return false;
        }
      };

      const getNewDate = (item, index) => {
        switch (valueType) {
          case 'WEEK':
            return dayjs(date).isoWeek(item);
          case 'DAY_OF_WEEK':
            return dayjs(date).day(index);
          case 'MONTH_DAY':
            return dayjs(date).year(ANCHOR_LEAP_YEAR).month(currMonthSelected).date(item);
          case 'DAY':
            return dayjs(date).year(ANCHOR_LEAP_YEAR).month(0).date(item);
          default:
            return dayjs(date);
        }
      };

      return (
        <div style={contentStyle} ref={refDropdown}>
          <DropdownHeader className="dropdown-header">
            <DropdownLabel
              valueType={valueType}
              date={date}
              format={format}
              type={type}
              operatorKey={operatorKey}
            />
          </DropdownHeader>

          <DropdownContent className="dropdown-content">
            {renderDateTypeOptions()}

            {isShowFixed ? (
              <>
                {valueType === 'MONTH_DAY' ? (
                  <div className="month-controller">
                    <Icon
                      type="icon-ants-angle-left"
                      style={{ fontSize: '14px', color: '#D2D2D2' }}
                      onClick={() => setCurrMonthSelected(prev => (prev === 0 ? 11 : prev - 1))}
                    />
                    {MONTH_LABEL_FULL[currMonthSelected]}
                    <Icon
                      type="icon-ants-angle-right"
                      style={{ fontSize: '14px', color: '#D2D2D2' }}
                      onClick={() => setCurrMonthSelected(prev => (prev === 11 ? 0 : prev + 1))}
                    />
                  </div>
                ) : null}
                <div
                  className={`custom-picker-content${
                    ['MONTH_DAY', 'DAY'].includes(valueType!) ? ' day-of-month-container' : ''
                  }`}
                >
                  {customPickerArr[valueType!].map((item, index) => (
                    <div
                      key={`day_${item}`}
                      className={clsx('custom-picker-item', {
                        'custom-picker-item-selected': getCellSelected(item, index),
                      })}
                      onClick={() => {
                        const newDate = getNewDate(item, index);

                        setState(state => ({
                          ...state,
                          date: dayjs(newDate).format(format),
                        }));
                      }}
                    >
                      {item}
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <>
                <Select
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                  options={newCalculationTypes}
                  value={option.calculationType.value}
                  onChange={value => {
                    const calculationType = CALCULATION_TYPES.find(
                      calculationType => calculationType.value === value,
                    );

                    onChangeOption({ calculationType });
                  }}
                />
                <InputNumber
                  value={option.value}
                  style={{ width: '100%' }}
                  min={0}
                  showHandler={false}
                  onChange={value => onChangeOption({ value: Math.floor(value as number) })}
                />
                <Select
                  getPopupContainer={triggerNode => triggerNode.parentNode}
                  options={newCalculationDates}
                  value={option.calculationDate.value}
                  onChange={value => {
                    const calculationDate = newCalculationDates.find(
                      calculationDate => calculationDate.value === value,
                    );

                    onChangeOption({ calculationDate });
                  }}
                />
              </>
            )}

            <ErrorMessage errorMessage={errorMessage} />
          </DropdownContent>

          {renderDropdownFooter()}
        </div>
      );
    };

    const cellRender = useCallback(
      (current: number | Dayjs, info: CellRenderInfo<Dayjs>) => {
        const isDisabledTime = isDisabledSpecificTime(current, info, disabledTime, dayjs(date));
        const isDisableDate = disableDate(current) && info.type === 'date';

        return (
          <div
            className="antsomi-picker-cell-inner"
            style={{
              pointerEvents: 'all',
              width: '100%',
              ...(isDisabledTime
                ? {
                    color: globalToken?.colorTextDisabled,
                    opacity: 0.5,
                    background: globalToken?.bw2,
                    cursor: 'default',
                  }
                : {}),
            }}
            onClick={e => {
              if (isDisabledTime || isDisableDate) {
                e.preventDefault();
                e.stopPropagation();
                return;
              }

              if (info.type === 'time' && typeof current === 'number') {
                e.preventDefault();
                e.stopPropagation();

                if (info.subType && info.subType !== 'meridiem') {
                  setState(state => ({
                    ...state,
                    date: dayjs(date)[info.subType!](current).format(format),
                  }));
                }

                return;
              }

              if (
                info.type === 'decade' ||
                (info.type === 'year' && valueType !== 'YEAR') ||
                (info.type === 'month' && !['MONTH', 'YEAR_MONTH'].includes(valueType!))
              )
                return;

              e.preventDefault();
              e.stopPropagation();
              setState(state => ({ ...state, date: dayjs(current).format(format) }));
              // onChangeDatePicker(current);
            }}
          >
            {getCellRender(current, valueType!, info)}
          </div>
        );
      },
      [valueType, format, date, disabledTime, disableDate],
    );

    if (isViewMode)
      return (
        <Space
          direction="vertical"
          size={5}
          className="antsomi-advanced-picker-container"
          ref={refDropdown}
        >
          {!!label && typeof label === 'string' ? (
            <Text style={{ color: '#666666' }}>{label}</Text>
          ) : (
            label
          )}
          <span className="date-picker__title--view-mode">{selectedDateTitle}</span>
        </Space>
      );

    return (
      <Space
        direction="vertical"
        size={5}
        className="antsomi-advanced-picker-container"
        // ref={onlyShowFixed ? undefined : refDropdown}
      >
        {!!label && typeof label === 'string' ? (
          <Text style={{ color: '#666666' }}>{label}</Text>
        ) : (
          label
        )}

        {isShowFixed && !['WEEK', 'DAY_OF_WEEK', 'MONTH_DAY', 'DAY'].includes(valueType!) ? (
          <Tooltip title={selectedDateTitle}>
            <DatePicker
              disabled={disabled}
              open={!disabled && isOpen}
              popupStyle={popupStyle}
              allowClear={false}
              inputReadOnly
              style={{ textOverflow: 'ellipsis', ...inputStyle }}
              status={errorMessage ? 'error' : ''}
              inputRender={() => (
                <DatePickerCustomInput
                  readOnly
                  placeholder="Select Date"
                  value={selectedDateTitle}
                  onClick={() => toggleOpenDropdown()}
                />
              )}
              value={currDate}
              disabledDate={disableDate}
              format={formatDisplay}
              showToday={false}
              popupClassName={clsx('antsomi-picker-dropdown__advanced', {
                '--error': errorMessage,
                'hide-picker-header': ['QUARTER', 'MONTH'].includes(valueType!),
                'only-show-time-picker': ['HOUR', 'MINUTE'].includes(valueType!),
                'is-minutes-picker': valueType === 'MINUTE',
                'is-hours-picker': valueType === 'HOUR',
              })}
              renderExtraFooter={() => {
                const timeFormat = getTimePickerRender(valueType!);

                return (
                  <>
                    <DatePickerHeader>
                      <DropdownLabel
                        valueType={valueType}
                        date={date}
                        format={format}
                        type={type}
                        operatorKey={operatorKey}
                      />

                      {renderDateTypeOptions()}
                    </DatePickerHeader>

                    <DatePickerFooter>
                      <div style={{ padding: '0 10px' }}>
                        <ErrorMessage errorMessage={errorMessage} />
                      </div>

                      {renderDropdownFooter()}
                    </DatePickerFooter>

                    <Divider />

                    {timeFormat && valueType !== 'HOUR' && valueType !== 'MINUTE' ? (
                      <TimeLabel
                        $width={timeFormat.length * 30}
                        $isRangePicker={operatorKey === 'between'}
                        $onlyShowFixed={onlyShowFixed}
                      >
                        {['HH', 'HHmm', 'HHmmss'].includes(timeFormat) ? <span>Hours</span> : null}
                        {['HHmm', 'HHmmss', 'mm'].includes(timeFormat) ? (
                          <span>Minutes</span>
                        ) : null}
                        {timeFormat === 'HHmmss' ? <span>Seconds</span> : null}
                      </TimeLabel>
                    ) : null}
                  </>
                );
              }}
              picker={getPickerRender(valueType!)}
              showTime={
                getTimePickerRender(valueType!)
                  ? {
                      format: getTimePickerRender(valueType!),
                    }
                  : undefined
              }
              suffixIcon={
                <CalendarIconWrapper
                  style={{
                    pointerEvents: disabled ? 'none' : 'all',
                  }}
                  onClick={() => toggleOpenDropdown()}
                >
                  <EventIcon
                    width={20}
                    height={20}
                    fill={errorMessage ? THEME.token?.colorError : THEME.token?.bw10}
                  />
                </CalendarIconWrapper>
              }
              cellRender={cellRender}
              onOpenChange={() => toggleOpenDropdown()}
            />
          </Tooltip>
        ) : (
          <Dropdown
            disabled={disabled}
            open={!disabled && isOpen}
            dropdownRender={dropdownRender}
            trigger={['click']}
            // onOpenChange={(open, info) => {
            //   console.log('🚀🚀🚀 ', 4, { open, info });
            //   toggleOpenDropdown();
            // }}
            overlayStyle={{ width: 'fit-content', minWidth: 'auto' }}
          >
            <Tooltip title={selectedDateTitle}>
              <InputStyled
                disabled={disabled}
                onClick={() => toggleOpenDropdown()}
                readOnly
                suffix={
                  <CalendarIconWrapper
                    style={{
                      pointerEvents: disabled ? 'none' : 'all',
                    }}
                    onClick={() => toggleOpenDropdown()}
                  >
                    <EventIcon width={19} height={19} />
                  </CalendarIconWrapper>
                }
                style={{ textOverflow: 'ellipsis', ...inputStyle }}
                value={selectedDateTitle}
                status={errorMessage ? 'error' : ''}
              />
            </Tooltip>
          </Dropdown>
        )}
      </Space>
    );
  },
);
