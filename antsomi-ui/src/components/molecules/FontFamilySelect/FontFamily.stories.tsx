// FontFamilySelect.stories.tsx

import { Meta } from '@storybook/react';

import { FontFamilySelect } from '.';

export default {
  title: 'Molecules/FontFamilySelect',
  component: FontFamilySelect,
} as Meta;

export const Default = {
  args: {
    defaultValue: 'Arial',
    value: 'Arial',
    label: 'Font Family',
    onChange: (family: string) => {
      console.log(`Selected font family: ${family}`);
    },
  },
};

export const LimitFonts = {
  args: {
    defaultValue: 'Arial',
    value: 'Arial',
    label: 'Font Family',
    showOptions: ['Arial', 'Helvetica', 'Times New Roman'],
    onChange: (family: string) => {
      console.log(`Selected font family: ${family}`);
    },
  },

  parameters: {
    docs: {
      description: {
        story: 'Limit font options by props "showOptions".',
      },
    },
  },
};
