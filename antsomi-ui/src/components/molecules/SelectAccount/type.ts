import React from 'react';

type AccountProps = {
  type: 'default';
};

type GroupAccountProps = {
  type: 'group';
  listAccountGroup: any[];
  groupNameKey: string;
  groupIdKey: string;
  groupText?: string;
  accountText?: string;
};

export type SelectAccountWithUsers = {
  users?: Record<any, string>[];
};

export type SelectAccountWithApi = {
  dataAccountsKey?: string;
  paramsAccounts?: {};
  serviceAccounts?: ({ params, search }) => Promise<any>;
};

export type SelectAccountProps = {
  showOwnerDefault?: boolean;
  initData?: Array<string | number>;
  searchType?: 'client' | 'server';
  isViewMode?: boolean;
  onlyOne?: boolean;
  disabled?: boolean;
  disabledAccount?: any[];
  limit?: number;
  limitShow?: number;
  hideAddButton?: boolean;
  showSearchViewAll?: boolean;

  className?: string;
  styles?: {
    wrapper?: React.CSSProperties;
    accountChip?: React.CSSProperties;
    btnAdd?: React.CSSProperties;
    btnViewAll?: React.CSSProperties;
  };

  nameKey: string;
  userIdKey: string;
  users?: Record<string, any>[];

  addText?: string;
  applyText?: string;
  cancelText?: string;

  onChange?: (idSelected: Array<string | number>, users: Record<string, any>) => void;
} & (SelectAccountWithUsers | SelectAccountWithApi) &
  (AccountProps | GroupAccountProps);

export type TState = SelectAccountProps & {
  isInitDone: boolean;
  searchText: string;
  openAccountList: boolean;
  limitShow: number;
  dataAccountsKey?: string;
  onlyOne: boolean;

  users: any[];
  usersDisplay: any[];
  listAccountGroupDisplay: any[];

  usersSelected: (string | number)[];

  paramsFetchUsers: {};
  paramsAccounts?: {};
  serviceAccounts?: Function;
};

export type TAction =
  | { type: 'INIT' }
  | { type: 'APPLY'; payload: { formRef: React.RefObject<HTMLFormElement> } }
  | { type: 'APPLY_SELECTED'; payload: any[] }
  | { type: 'UPDATE'; payload: SelectAccountProps }
  | { type: 'OPEN_ACCOUNT_LIST' }
  | { type: 'CLOSE_ACCOUNT_LIST' }
  | { type: 'UPDATE_USERS'; payload: any[] }
  | { type: 'UPDATE_USERS_DISPLAY'; payload: any[] }
  | { type: 'UPDATE_USERS_SELECTED'; payload: any[] }
  | { type: 'UPDATE_SEARCH_TEXT'; payload: string }
  | { type: 'UPDATE_USERS'; payload: any[] }
  | { type: 'REMOVE_USER'; payload: { userId: string | number; index: number } };

export type TContext = {
  formListRef: React.RefObject<HTMLFormElement>;
  state: TState;
  dispatch: React.Dispatch<TAction>;
};
