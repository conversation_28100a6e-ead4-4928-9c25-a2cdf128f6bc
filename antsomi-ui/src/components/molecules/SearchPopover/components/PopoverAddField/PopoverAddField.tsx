import { useCallback, useMemo } from 'react';
import clsx from 'clsx';
import { Field, Option, PopoverAddFieldProps } from '../../types';
import { Button, Icon } from '../../../../atoms';
import { PopoverSelect } from '../PopoverSelect';
import { Flex, Typography } from 'antd';
import { IconField } from '@antscorp/antsomi-ui/es/components/atoms/IconField';

export const PopoverAddField = (props: PopoverAddFieldProps) => {
  const { fields, onSearchPredicate, className, children, isAllowEmpty, ...rest } = props;

  const originalFieldByKey = useMemo(
    () => new Map(fields.map(field => [field.key, field])),
    [fields],
  );

  const handleOnSearchPredicate = useCallback(
    (option: Option) => {
      const originalField = originalFieldByKey.get(option.key);

      if (!originalField) return false;

      if (onSearchPredicate && originalField) {
        return onSearchPredicate(originalField);
      }

      return false;
    },
    [onSearchPredicate, originalFieldByKey],
  );

  const options = useCallback(
    (params: { selected: string[] }) =>
      fields.map(field => {
        let search = field.search || '';

        if (typeof field.label === 'string') {
          search = search.concat(field.label);
        }

        return {
          ...field,
          search,
          label: (
            <Flex align="center" gap={8}>
              {field.dataType && <IconField dataType={field.dataType} />}

              {typeof field.label === 'string' && (
                <Typography.Text
                  ellipsis={{
                    tooltip: true,
                  }}
                >
                  {field.label}
                </Typography.Text>
              )}

              {typeof field.label === 'function' &&
                field.label({
                  selected: params.selected.includes(field.key),
                })}
            </Flex>
          ),
        };
      }),
    [fields],
  );

  return (
    <PopoverSelect<Field>
      {...rest}
      options={options}
      onSearchPredicate={handleOnSearchPredicate}
      className={clsx(className, 'ants-popover-add-fields')}
      isAllowEmpty={isAllowEmpty || false}
    >
      {children || (
        <Button type="text">
          <Icon type="icon-ants-plus-slim" size={14} />
          Add fields
        </Button>
      )}
    </PopoverSelect>
  );
};
