import { useEffect, useMemo, useRef, useState } from 'react';
import { SearchPopover } from '../../SearchPopover';
import { Field, Option, PopoverSelectProps } from '../../types';
import './styles.scss';
import { Button, Checkbox, Typography } from '../../../../atoms';
import {
  StyledAction,
  StyledFooter,
  StyledInputValue,
  StyledInputValueWrapper,
  StyledListFieldsWrapper,
} from './styled';
import { EmptyData } from '../../../EmptyData';
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';
import { calDefaultListHeightInPopover, defaultSearch } from '../../utils';
import { VirtualizedMenu } from '../../../VirtualizedMenu';
import { ITEM_SIZE, ITEM_SPACING } from '../../../VirtualizedMenu/config';

const { t } = i18nInstance;

export const PopoverSelect = <TOption extends Option>(props: PopoverSelectProps<TOption>) => {
  const {
    open: openProp,
    selected,
    options: optionsProp = [],
    inputSearchProps = {},
    children,
    showAllLabel = 'Show all',
    showSelectedLabel = 'Show selected',
    selectAllLabel = 'Select all',
    deselectAllLabel = 'Unselect all',
    menuProps = {},
    isAllowEmpty = false,
    inputValue = false,
    onCancel,
    onApply,
    onSearchPredicate,
    onClickInputValue,
    noItemContent,
    ...rest
  } = props;

  const itemSize = menuProps.itemSize || ITEM_SIZE;
  const itemSpacing = menuProps.itemSpacing || ITEM_SPACING;

  const { value: searchValue, searchConfig, ...restInputSearchProps } = inputSearchProps;

  const refOnSearchPredicate = useRef(onSearchPredicate);

  useEffect(() => {
    refOnSearchPredicate.current = onSearchPredicate;
  }, [onSearchPredicate]);

  const [open, setOpen] = useState(openProp);
  const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set());
  const [search, setSearch] = useState<string>('');
  const [showSelected, setShowSelected] = useState(false);

  const applyDisabled = selectedKeys.size === 0 && !isAllowEmpty;

  const options = useMemo(
    () =>
      typeof optionsProp === 'function'
        ? optionsProp({ selected: [...selectedKeys] })
        : optionsProp,
    [optionsProp, selectedKeys],
  );

  useEffect(() => {
    if (openProp !== undefined) {
      setOpen(openProp);
    }
  }, [openProp]);

  useEffect(() => {
    if (searchValue !== undefined) {
      setSearch(searchValue.toString());
    }
  }, [searchValue]);

  useEffect(() => {
    if (selected !== undefined) {
      setSelectedKeys(new Set(selected));
    }
  }, [selected]);

  const filteredOptions = useMemo(() => {
    let result = options.filter(field => {
      if (!refOnSearchPredicate.current) {
        return defaultSearch(field, search, searchConfig);
      }

      return refOnSearchPredicate.current(field) || defaultSearch(field, search, searchConfig);
    });

    if (showSelected) {
      result = result.filter(field => selectedKeys.has(field.key));
    }

    return result;
  }, [options, search, showSelected, selectedKeys, searchConfig]);

  const handleToggleField = (field: Field, checked: boolean) => {
    const newSelectedKeys = new Set(selectedKeys);

    if (checked) {
      newSelectedKeys.add(field.key);
    } else {
      newSelectedKeys.delete(field.key);
    }

    setSelectedKeys(newSelectedKeys);
  };

  const handleCancel = () => {
    setOpen(false);

    if (onCancel) onCancel();
  };

  const handleApply = () => {
    if (onApply) onApply(Array.from(selectedKeys));

    setOpen(false);
  };

  const handleOnSearch = (searchValue: string) => {
    setSearch(searchValue);

    if (inputSearchProps.onAfterChange) {
      inputSearchProps.onAfterChange(searchValue);
    }
  };

  const handleSelectAll = () => {
    setSelectedKeys(new Set(filteredOptions.map(field => field.key)));
  };

  const handleDeselectAll = () => {
    setSelectedKeys(new Set());
  };

  const handleAfterOpenChange = (visible: boolean) => {
    if (!visible) {
      setSearch(searchValue ? searchValue.toString() : '');
      setSelectedKeys(selected ? new Set(selected) : new Set());
    }

    if (rest.afterOpenChange) {
      rest.afterOpenChange(visible);
    }
  };

  const handleOpenChange = (visible: boolean) => {
    setOpen(visible);
  };

  const handleInputValue = () => {
    if (typeof onClickInputValue === 'function') {
      onClickInputValue();
    }
    setOpen(false);
  };

  const renderCheckBoxLabel = (option: Option) => {
    if (typeof option.label === 'function') {
      return option.label({ selected: selectedKeys.has(option.key) });
    }

    return (
      <Typography.Text
        ellipsis={{
          tooltip: option.label,
        }}
        style={{ maxWidth: 310 }}
      >
        {option.label}
      </Typography.Text>
    );
  };

  const items = filteredOptions.map(opt => ({
    key: opt.key,
    label: (
      <Checkbox
        onChange={e => handleToggleField(opt, e.target.checked)}
        checked={selectedKeys.has(opt.key)}
      >
        {renderCheckBoxLabel(opt)}
      </Checkbox>
    ),
  }));

  return (
    <SearchPopover
      destroyTooltipOnHide
      placement="bottomLeft"
      {...rest}
      content={
        <>
          {optionsProp.length > 0 && (
            <StyledAction>
              <Button type="link" size="small" onClick={() => setShowSelected(current => !current)}>
                {showSelected ? showAllLabel : showSelectedLabel}
              </Button>

              {selectedKeys.size === options.length ? (
                <Button type="link" onClick={handleDeselectAll}>
                  {deselectAllLabel}
                </Button>
              ) : (
                <Button type="link" onClick={handleSelectAll}>
                  {selectAllLabel}
                </Button>
              )}
            </StyledAction>
          )}

          {filteredOptions.length ? (
            <StyledListFieldsWrapper
              $minHeight={calDefaultListHeightInPopover({
                listLength: filteredOptions.length,
                itemSize,
                itemSpacing,
              })}
            >
              <VirtualizedMenu {...menuProps} itemSize={itemSize} items={items} />
            </StyledListFieldsWrapper>
          ) : (
            noItemContent || (
              <EmptyData showIcon={false} description={t(translations.noData).toString()} />
            )
          )}

          {inputValue && (
            <StyledInputValueWrapper>
              <StyledInputValue onClick={handleInputValue}>Input value</StyledInputValue>
            </StyledInputValueWrapper>
          )}

          <StyledFooter>
            <Button onClick={handleCancel}>Cancel</Button>

            <Button onClick={handleApply} disabled={applyDisabled} type="primary">
              Apply
            </Button>
          </StyledFooter>
        </>
      }
      trigger={['click']}
      open={open}
      inputSearchProps={{
        ...restInputSearchProps,
        value: search,
        onAfterChange: handleOnSearch,
      }}
      afterOpenChange={handleAfterOpenChange}
      onOpenChange={handleOpenChange}
    >
      {children || <Button>Select</Button>}
    </SearchPopover>
  );
};
