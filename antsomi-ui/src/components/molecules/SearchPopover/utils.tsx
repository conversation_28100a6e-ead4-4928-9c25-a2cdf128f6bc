import { searchStringQuery } from '@antscorp/antsomi-ui/es/utils';
import { Option, SearchConfig } from './types';
import { calInlineListSize } from '../VirtualizedMenu/utils';
import { min } from 'lodash';

export const defaultSearch = (opt: Option, search: string, config?: SearchConfig): boolean => {
  const { searchWithKey = false } = config || {};

  let text = searchWithKey ? opt.key : '';

  if (typeof opt.label === 'string') {
    text = text.concat(opt.label);
  }

  if (opt.search) {
    text = text.concat(opt.search);
  }

  return searchStringQuery(text, search);
};

export const calDefaultListHeightInPopover = (params: {
  listLength: number;
  itemSize: number;
  itemSpacing: number;
}) => {
  const { listLength, itemSpacing, itemSize } = params;

  const DEFAULT_NUM_ITEM_SHOWED = 6;

  const itemLength = min([DEFAULT_NUM_ITEM_SHOWED, listLength]);

  return calInlineListSize(itemLength || 0, itemSize, itemSpacing);
};
