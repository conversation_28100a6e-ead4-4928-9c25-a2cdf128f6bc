import React from 'react';
import { Popover } from 'antd';
import clsx from 'clsx';
import { SearchPopoverProps } from './types';
import { StyledInput } from './styled';
import { translate, translations } from '@antscorp/antsomi-ui/es/locales';
import { SearchIcon } from '../../icons';

export const SearchPopover = (props: SearchPopoverProps) => {
  const { inputSearchProps = {}, ...popoverProps } = props;

  const {
    overlayClassName,
    arrow = false,
    children,
    content,
    trigger = ['click'],
    ...restPopoverProps
  } = popoverProps;

  return (
    <Popover
      trigger={trigger}
      arrow={arrow}
      destroyTooltipOnHide
      {...restPopoverProps}
      overlayClassName={clsx('no-padding-content', overlayClassName)}
      overlay
      content={
        <>
          <StyledInput
            placeholder={
              inputSearchProps?.placeholder || `${translate(translations.global.search)}`
            }
            bordered={false}
            {...inputSearchProps}
            addonAfter={<SearchIcon />}
            withWrapper={false}
          />

          {content}
        </>
      }
    >
      {children}
    </Popover>
  );
};
