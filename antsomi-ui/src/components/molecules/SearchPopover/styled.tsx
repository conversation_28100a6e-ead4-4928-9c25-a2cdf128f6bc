import styled from 'styled-components';
import { Input } from '../../atoms';

export const StyledInput = styled(props => <Input {...props} />)`
  &.antsomi-input-group-wrapper {
    padding: 8px 10px;
    border-bottom: 1px solid var(--divider-1);

    .antsomi-input {
      padding-left: 0;
      padding-right: 0;
    }

    .antsomi-input-group-addon {
      border: none;
      background: transparent;
      font-size: 20px;
      padding: 0;
      min-width: 25px;

      > i {
        font-size: 24px;
        color: #595959;
        cursor: pointer;
      }
    }
  }
`;
