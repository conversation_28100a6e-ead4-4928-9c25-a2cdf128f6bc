// FontFamilySelect.stories.tsx

import { Meta } from '@storybook/react';

import { TextTransformSelect } from '.';

export default {
  title: 'Molecules/TextTransformSelect',
  component: TextTransformSelect,
} as Meta;

export const Default = {
  args: {
    defaultValue: 'none',
    value: 'none',
    label: 'Text Transform',
    onChange: (select: string) => {
      console.log(`Selected text transform: ${select}`);
    },
  },
};

export const LimitTextTransform = {
  args: {
    defaultValue: 'none',
    value: 'none',
    label: 'Text Transform Limit',
    showOptions: ['None', 'Uppercase', 'Lowercase'],
    onChange: (select: string) => {
      console.log(`Selected text transform: ${select}`);
    },
  },

  parameters: {
    docs: {
      description: {
        story: 'Limit text-transform options by props "showOptions".',
      },
    },
  },
};
