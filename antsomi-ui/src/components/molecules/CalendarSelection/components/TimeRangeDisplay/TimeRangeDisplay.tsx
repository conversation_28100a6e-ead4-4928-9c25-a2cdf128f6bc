import cls from 'clsx';
import React, { forwardRef } from 'react';
import { Typography } from '@antscorp/antsomi-ui/es/components/atoms';
import { ACTION_TYPES } from '../../actions';
import { useCalendarSelectionContext } from '../../hooks';
import { getRangeTypeSelectedTitle, getRangeValueSelctedTitle } from '../../utils';
import { StyledBtnDisplayWrapper } from './styled';
import { CalendarSelectionValue } from '../../types';

const { Text } = Typography;

export type TimeRangeDisplayProps = {
  showType?: boolean;
  formatStart?: string;
  formatEnd?: string;
  operator?: string;
  render?: (
    value: CalendarSelectionValue,
    rangeSelectedTitle: string,
    rangeValueTitles: string[],
    handleOpenPopover: () => void,
  ) => React.ReactNode;
};

export const TimeRangeDisplay: React.FC<TimeRangeDisplayProps> = forwardRef<
  HTMLDivElement,
  TimeRangeDisplayProps
>((props, ref) => {
  const { showType = true, formatStart, formatEnd, operator, render, ...rest } = props;
  const { state, action } = useCalendarSelectionContext();

  const { rangeInfo, rangeValue } = state.value.original;

  const handleOpenPopover = () => {
    action.dispatch({ type: ACTION_TYPES.togglePopover });
  };

  const rangeSelectedTitle = getRangeTypeSelectedTitle(rangeInfo);
  const rangeValueTitles = getRangeValueSelctedTitle(rangeValue, {
    formatStart,
    formatEnd,
    operator,
  });

  const renderDateTitle = (title: string) => (
    <Text
      className="range-date-title"
      ellipsis={{
        tooltip: title,
      }}
    >
      {title}
    </Text>
  );

  return render ? (
    render(state.value.original, rangeSelectedTitle, rangeValueTitles, handleOpenPopover)
  ) : (
    <StyledBtnDisplayWrapper ref={ref} {...rest} onClick={handleOpenPopover} role="button">
      {showType && (
        <Text className="range-type" ellipsis={{ tooltip: rangeSelectedTitle }}>
          {rangeSelectedTitle}
        </Text>
      )}

      <div className={cls('range-date')}>
        {rangeValue.length > 0 && renderDateTitle(rangeValueTitles[0])}

        {rangeValue.length > 1 && (
          <div className={cls('collapse-wrapper')}>
            {rangeValueTitles.slice(1).map(title => (
              <React.Fragment key={title}>
                <div className="compare-title">Compare</div>

                {renderDateTitle(title)}
              </React.Fragment>
            ))}
          </div>
        )}
      </div>
    </StyledBtnDisplayWrapper>
  );
});
