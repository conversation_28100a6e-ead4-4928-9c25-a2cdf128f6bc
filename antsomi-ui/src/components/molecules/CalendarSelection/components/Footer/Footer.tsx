import React from 'react';
import { Button } from '@antscorp/antsomi-ui/es/components/atoms/Button';
import { useCalendarSelectionContext } from '../../hooks';
import { apply, togglePopover } from '../../actions';
import { StyledFooterRoot } from './styled';

export const Footer = () => {
  const { action } = useCalendarSelectionContext();

  return (
    <StyledFooterRoot>
      <Button onClick={() => action.dispatch(apply())} type="primary">
        Apply
      </Button>

      <Button onClick={() => action.dispatch(togglePopover())}>Cancel</Button>
    </StyledFooterRoot>
  );
};
