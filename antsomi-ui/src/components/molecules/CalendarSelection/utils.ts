import dayjs from 'dayjs';

import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isoWeek from 'dayjs/plugin/isoWeek';
import weekday from 'dayjs/plugin/weekday';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

import { AutoUpdateTo, RangeInfo, RangeValue, ReducerState } from './types';
import {
  AUTO_UPDATE_TO_KEY,
  DEFAULT_NUM_UP_TO,
  MAP_TITLE,
  RANGE_LIMIT,
  SELECTION_KEY,
} from './constants';
import { last } from 'lodash';
import { asssertCannotReach } from '@antscorp/antsomi-ui/es/utils';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isoWeek);
dayjs.extend(quarterOfYear);
dayjs.extend(weekday);
dayjs.extend(isSameOrBefore);

const dayZero = new Date(0);

export const rangeInfoToStringType = (rangeInfo: RangeInfo) => {
  switch (rangeInfo.type) {
    case SELECTION_KEY.last_week:
    case SELECTION_KEY.this_week: {
      return `${rangeInfo.type}_${rangeInfo.weekStartsOn}`;
    }
    default:
      return rangeInfo.type;
  }
};

export const serializeWeekStartsOn = (rangeType: string) => {
  let startOn: number | string | undefined = last(rangeType.split('_'));

  if (startOn) {
    startOn = +startOn;
  }

  if (typeof startOn === 'number' && startOn >= 1 && startOn <= 7) {
    return startOn;
  }
};

export const generateRangeInfo = (rangeType: string) => {
  const startOn = serializeWeekStartsOn(rangeType);
  let rangeInfo: RangeInfo | null = null;

  switch (true) {
    case rangeType.startsWith(SELECTION_KEY.this_week): {
      if (startOn) {
        rangeInfo = {
          type: SELECTION_KEY.this_week,
          weekStartsOn: startOn,
        };
      }
      break;
    }
    case rangeType.startsWith(SELECTION_KEY.last_week): {
      if (startOn) {
        rangeInfo = {
          type: SELECTION_KEY.last_week,
          weekStartsOn: startOn,
        };
      }
      break;
    }
    default:
      break;
  }

  switch (rangeType) {
    case SELECTION_KEY.custom: {
      rangeInfo = {
        type: rangeType,
        autoUpdateTo: AUTO_UPDATE_TO_KEY.fixed,
      };
      break;
    }
    case SELECTION_KEY.yesterday:
    case SELECTION_KEY.today:
    case SELECTION_KEY.last_7_days:
    case SELECTION_KEY.last_14_days:
    case SELECTION_KEY.last_30_days:
    case SELECTION_KEY.this_month:
    case SELECTION_KEY.last_month:
    case SELECTION_KEY.this_quarter:
    case SELECTION_KEY.last_quarter:
    case SELECTION_KEY.all_time: {
      rangeInfo = { type: rangeType };
      break;
    }

    case SELECTION_KEY.days_up_to_today:
    case SELECTION_KEY.days_up_to_yesterday: {
      rangeInfo = {
        type: rangeType,
        numOfDays: DEFAULT_NUM_UP_TO,
      };
      break;
    }
    default:
      break;
  }

  return rangeInfo;
};

export const getDefaultDate = () => {
  const toDayTz = dayjs().startOf('d');

  return {
    toDay: toDayTz.toISOString(),
    yesterday: toDayTz.subtract(1, 'd').toISOString(),
    startWeekSS: toDayTz.weekday(0).toISOString(),
    startWeekMS: toDayTz.isoWeekday(1).toISOString(),
    last7Days: toDayTz.subtract(1, 'w').toISOString(),
    startLastWeekSS: toDayTz.subtract(1, 'w').weekday(0).toISOString(),
    startLastWeekMS: toDayTz.subtract(1, 'w').isoWeekday(1).toISOString(),
    endLastWeekSS: toDayTz.subtract(1, 'w').weekday(6).toISOString(),
    endLastWeekMS: toDayTz.subtract(1, 'w').isoWeekday(7).toISOString(),
    last14Days: toDayTz.subtract(14, 'd').toISOString(),
    startOfThisMonth: toDayTz.startOf('M').toISOString(),
    endOfThisMonth: toDayTz.endOf('M').toISOString(),
    last30Days: toDayTz.subtract(30, 'd').toISOString(),
    lastMonthFirstDate: toDayTz.subtract(1, 'M').startOf('M').toISOString(),
    lastMonthLastDate: toDayTz.subtract(1, 'M').endOf('M').toISOString(),
    thisQuaterFirstDate: toDayTz.startOf('Q').toISOString(),
    thisQuaterLastDate: toDayTz.endOf('Q').toISOString(),
    lastQuaterFirstDate: toDayTz.subtract(1, 'Q').startOf('Q').toISOString(),
    lastQuaterLastDate: toDayTz.subtract(1, 'Q').endOf('Q').toISOString(),
    maxSub: toDayTz.diff(dayZero, 'd'),
    todayUptoCount: (val: number) => toDayTz.subtract(Math.round(val - 1), 'd').toISOString(),
    yesterdayUptoCount: (val: number) =>
      toDayTz.subtract(Math.round(val - 1) + 1, 'd').toISOString(),
  };
};

export const getRangeValue = ({
  rangeInfo,
  currentRangeValue = [
    {
      start: dayjs(getDefaultDate().last7Days).toISOString(),
      end: dayjs(getDefaultDate().yesterday).toISOString(),
    },
  ],
  limitNumOfDays = RANGE_LIMIT,
}: {
  rangeInfo: RangeInfo;
  currentRangeValue?: RangeValue[];
  limitNumOfDays?: number;
}): RangeValue[] => {
  const defaultDates = getDefaultDate();

  const result = currentRangeValue.map(rangeValue => {
    const temp: RangeValue = {
      start: defaultDates.toDay,
      end: defaultDates.toDay,
    };

    switch (rangeInfo.type) {
      case SELECTION_KEY.custom: {
        temp.start = rangeValue.start;
        temp.end = rangeValue.end;

        switch (rangeInfo.autoUpdateTo) {
          case AUTO_UPDATE_TO_KEY.today: {
            temp.end = defaultDates.toDay;
            break;
          }
          case AUTO_UPDATE_TO_KEY.yesterday: {
            temp.end = defaultDates.yesterday;
            break;
          }
          default:
            break;
        }
        break;
      }
      case SELECTION_KEY.today: {
        temp.start = defaultDates.toDay;
        temp.end = defaultDates.toDay;
        break;
      }
      case SELECTION_KEY.yesterday: {
        temp.start = defaultDates.yesterday;
        temp.end = defaultDates.yesterday;
        break;
      }
      case SELECTION_KEY.this_week: {
        temp.start =
          rangeInfo.weekStartsOn === 1 ? defaultDates.startWeekMS : defaultDates.startWeekSS;
        temp.end = defaultDates.toDay;
        break;
      }
      case SELECTION_KEY.last_week: {
        temp.start =
          rangeInfo.weekStartsOn === 1
            ? defaultDates.startLastWeekMS
            : defaultDates.startLastWeekSS;
        temp.end =
          rangeInfo.weekStartsOn === 1 ? defaultDates.endLastWeekMS : defaultDates.endLastWeekSS;
        break;
      }
      case SELECTION_KEY.last_7_days: {
        temp.start = defaultDates.last7Days;
        temp.end = defaultDates.yesterday;
        break;
      }
      case SELECTION_KEY.last_14_days: {
        temp.start = defaultDates.last14Days;
        temp.end = defaultDates.yesterday;
        break;
      }
      case SELECTION_KEY.last_30_days: {
        temp.start = defaultDates.last30Days;
        temp.end = defaultDates.yesterday;
        break;
      }
      case SELECTION_KEY.this_month: {
        temp.start = defaultDates.startOfThisMonth;
        temp.end = defaultDates.toDay;
        break;
      }
      case SELECTION_KEY.last_month: {
        temp.start = defaultDates.lastMonthFirstDate;
        temp.end = defaultDates.lastMonthLastDate;
        break;
      }
      case SELECTION_KEY.this_quarter: {
        temp.start = defaultDates.thisQuaterFirstDate;
        temp.end = defaultDates.toDay;
        break;
      }
      case SELECTION_KEY.last_quarter: {
        temp.start = defaultDates.lastQuaterFirstDate;
        temp.end = defaultDates.lastQuaterLastDate;
        break;
      }
      case SELECTION_KEY.all_time: {
        temp.start = dayjs(defaultDates.toDay).subtract(limitNumOfDays, 'd').toISOString();
        temp.end = defaultDates.toDay;
        break;
      }
      case SELECTION_KEY.days_up_to_today: {
        temp.start = dayjs(defaultDates.toDay).subtract(rangeInfo.numOfDays, 'd').toISOString();
        temp.end = defaultDates.toDay;
        break;
      }
      case SELECTION_KEY.days_up_to_yesterday: {
        temp.start = dayjs(defaultDates.toDay).subtract(rangeInfo.numOfDays, 'd').toISOString();
        temp.end = defaultDates.yesterday;
        break;
      }
      default:
        asssertCannotReach(rangeInfo);
    }

    return { ...rangeValue, ...temp };
  });

  return result.map(range => ({
    ...range,
    start: dayjs(range.start).startOf('d').toISOString(),
    end: dayjs(range.end).endOf('d').toISOString(),
  }));
};

export const getRangeValueSelctedTitle = (
  rangeValue: RangeValue[],
  options?: { formatStart?: string; formatEnd?: string; minimize?: boolean; operator?: string },
) => {
  const { formatStart, formatEnd, operator = '-' } = options || {};

  const result: string[] = [];

  rangeValue.forEach(range => {
    const { start, end } = range;

    let startFormat = formatStart || 'MMM D';
    const endFormat = formatEnd || 'MMM D YYYY';

    if (dayjs(start)?.isSame(dayjs(end), 'D')) {
      return result.push(dayjs(start)?.format('MMM D YYYY'));
    }

    if (!dayjs(start)?.isSame(dayjs(end), 'y') && !formatStart) {
      startFormat = `${startFormat} YYYY`;
    }

    result.push(
      `${dayjs(start)?.format(startFormat)} ${operator} ${dayjs(end)?.format(endFormat)}`,
    );
  });

  return result;
};

export const getRangeTypeSelectedTitle = (rangeInfo: RangeInfo) => {
  let label = MAP_TITLE[rangeInfo.type];

  switch (rangeInfo.type) {
    case SELECTION_KEY.days_up_to_yesterday:
    case SELECTION_KEY.days_up_to_today: {
      label = `${rangeInfo.numOfDays} ${label}`;
      break;
    }
    default:
      break;
  }

  return label;
};

export const combineCurrentValue = (state: ReducerState) => {
  const { rangeValue, rangeInfo } = state.value.current;

  return { rangeInfo, rangeValue };
};

export const genTriggerOut = (() => {
  let catchedId = 0;

  return function increaseTriggerOut({
    mode,
    id,
  }: {
    mode: ReducerState['triggerOut']['mode'];
    id?: number;
  }) {
    if (!id) catchedId++;

    return { mode, id: id || catchedId };
  };
})();

export const isAutoUpdateToOption = (value: unknown): value is AutoUpdateTo => {
  if (typeof value === 'string' && Object.values(AUTO_UPDATE_TO_KEY).some(v => v === value)) {
    return true;
  }

  return false;
};
