import { Custom, RangeInfo, CalendarSelectionValue, RangeValue } from './types';

export const ACTION_TYPES = {
  initState: 'init_state',
  changeRangeType: 'change_range_type',
  updateRangeInfo: 'update_range_info',
  updateRangeValue: 'update_range_value',
  changeAutoUpdateTo: 'change_auto_update_to',
  togglePopover: 'toggle_popover',
  apply: 'apply',
  resetValue: 'reset_value',
} as const;

export const changeRangeType = (rangeType: string, callback?: (rangeInfo: RangeInfo) => void) => ({
  type: ACTION_TYPES.changeRangeType,
  payload: { rangeType, callback },
});

export const updateRangeInfo = (updateValue: Partial<RangeInfo>) => ({
  type: ACTION_TYPES.updateRangeInfo,
  payload: { updateValue },
});

export const updateRangeValue = (rangeValue: RangeValue[], options?: { isManual: boolean }) => ({
  type: ACTION_TYPES.updateRangeValue,
  payload: { rangeValue, options },
});

export const changeAutoUpdateTo = (value: Custom['autoUpdateTo']) => ({
  type: ACTION_TYPES.changeAutoUpdateTo,
  payload: value,
});

export const initState = (initData: CalendarSelectionValue, needTriggerOut: boolean = false) => ({
  type: ACTION_TYPES.initState,
  payload: { initData, needTriggerOut },
});

export const togglePopover = () => ({
  type: ACTION_TYPES.togglePopover,
});

export const apply = () => ({
  type: ACTION_TYPES.apply,
});

export const resetValue = () => ({
  type: ACTION_TYPES.resetValue,
});
