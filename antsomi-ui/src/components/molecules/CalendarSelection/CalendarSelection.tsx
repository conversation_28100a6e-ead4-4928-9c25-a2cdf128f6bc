/* eslint-disable react/jsx-no-constructed-context-values */
import React, { useLayoutEffect, useReducer, useRef } from 'react';
import { Divider, ConfigProvider as AntdConfigProvider } from 'antd';
import { ActionContext, StateContext } from './context';
import { reducer, INITIAL_STATE } from './reducer';
import { StyledPopover, StyledPopoverBody, StyledPopoverContainer } from './styled';
import { CustomPicker, Footer, RangeSelectOptions, TimeRangeDisplay } from './components';
import { LOCAL_TIMEZONE, RANGE_LIMIT } from './constants';
import { CalendarSelectionProps } from './types';
import { initState, resetValue } from './actions';
import { useTriggerOut } from './hooks/useTriggerOut';

export const CalendarSelection: React.FC<CalendarSelectionProps> = props => {
  const initialed = useRef(false);

  const {
    value,
    rangeLimit = RANGE_LIMIT,
    timezone = LOCAL_TIMEZONE,
    timeDisplayProps,
    popoverProps,
    rangePickerProps,
    onChange,
  } = props;

  const [state, dispatch] = useReducer(reducer, INITIAL_STATE);

  useLayoutEffect(() => {
    if (!value) return;

    const needTriggerOut = !initialed.current;

    // console.log({ needTriggerOut });

    dispatch(initState(value, needTriggerOut));

    initialed.current = true;
  }, [value]);

  useLayoutEffect(() => {
    if (!state.open) {
      return () => {
        dispatch(resetValue());
      };
    }
  }, [state.open]);

  useTriggerOut({ state, dispatch, onChange });

  const popoverContent = (
    <StyledPopoverContainer>
      <StyledPopoverBody>
        <RangeSelectOptions />

        <Divider type="vertical" style={{ margin: 0, height: 'auto' }} />

        <CustomPicker {...rangePickerProps} />
      </StyledPopoverBody>

      <Divider style={{ margin: 0 }} />

      <Footer />
    </StyledPopoverContainer>
  );

  // console.log(state.value.current.rangeValue);

  return (
    <AntdConfigProvider
      theme={{
        components: {
          Popover: {
            borderRadiusLG: 0,
            borderRadius: 0,
            borderRadiusOuter: 0,
          },
        },
      }}
    >
      <StateContext.Provider
        value={{
          ...state,
          rangeLimit,
          timezone,
        }}
      >
        <ActionContext.Provider value={{ dispatch }}>
          <StyledPopover
            {...popoverProps}
            open={state.open}
            arrow={false}
            trigger={['click']}
            content={popoverContent}
            destroyTooltipOnHide
            overlayInnerStyle={{ padding: 0 }}
            onOpenChange={isOpen => {
              if (!isOpen) dispatch({ type: 'toggle_popover' });
            }}
          >
            <TimeRangeDisplay {...timeDisplayProps} />
          </StyledPopover>
        </ActionContext.Provider>
      </StateContext.Provider>
    </AntdConfigProvider>
  );
};
