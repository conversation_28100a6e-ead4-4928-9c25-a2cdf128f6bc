import Icon from '@antscorp/icons';
import React, { useCallback, useEffect, useImperativeHandle, useState } from 'react';

import { Scrollbars, Text } from '@antscorp/antsomi-ui/es/components/atoms';
import { Divider, Input } from 'antd';
import { AccountItem } from './components';
import { AccountSelectionStyled } from './styled';

import { THEME } from '@antscorp/antsomi-ui/es/constants';
import { AccountRecent } from '@antscorp/antsomi-ui/es/models/AccountListing';
import { searchStringQuery } from '@antscorp/antsomi-ui/es/utils';
import { useAccountSelection } from './hooks';

export interface AccountListingProps {
  className?: string;
  apiConfig: {
    domain: string;
    permissionDomain?: string;
    token: string;
    userId: number;
    portalId?: number;
    languageCode?: string;
    appCode?: string;
    menuCode?: string;
  };
  showAllAccount?: boolean;
  currentAccount?: string | 'all';
  onChange?: (userId: number | string) => void;
}

interface AccountData {
  email: any;
  userId: any;
  userName: any;
  status: number;
}

export interface AccountListingHandle {
  getUsers: () => {
    recentData?: AccountRecent;
    accountData: Array<AccountData>;
  };
}

export const AccountListing = React.forwardRef<AccountListingHandle, AccountListingProps>(
  (props, ref) => {
    const { className, apiConfig, showAllAccount = true, currentAccount = '', onChange } = props;

    // States
    const [searchValue, setSearchValue] = useState<string>('');
    const [stateAccountData, setStateAccountData] = useState<Array<AccountData>>([]);

    const { accountData, recentData, updateRecentAccount, refetchRecentAccount } =
      useAccountSelection(apiConfig);

    const isAllAccount = currentAccount === 'all';

    useImperativeHandle(
      ref,
      () => ({
        getUsers() {
          return { recentData, accountData };
        },
      }),
      [recentData, accountData],
    );

    useEffect(
      () => () => {
        // fetch recent data when unmount this component
        refetchRecentAccount();
      },
      [],
    );

    useEffect(() => {
      if (!stateAccountData.length && Array.isArray(accountData) && accountData.length) {
        const newSortAccountData = accountData.sort(
          (a, b) => Number(+currentAccount === b.userId) - Number(+currentAccount === a.userId),
        ); //  isSelected=true to top in list accounts
        setStateAccountData(newSortAccountData);
      }
    }, [accountData, stateAccountData]);

    // Handlers
    const handleSelectAccount = useCallback(
      (userId: number | string) => {
        const currentRecent = recentData?.value.filter(Boolean).slice(0, 2) || [];

        const body =
          userId !== currentRecent[0]
            ? [userId, currentRecent[0]]
            : // Check the second current account is existed
              currentRecent[1]
              ? [userId, currentRecent[1]]
              : [userId, undefined];
        updateRecentAccount(body.map(id => Number(id)) as [number, number]);

        onChange?.(userId);
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [onChange, recentData],
    );

    return (
      <AccountSelectionStyled className={className}>
        <Input
          placeholder="Search"
          suffix={
            <Icon type="icon-ants-search-2" style={{ fontSize: '24px', color: THEME.token?.bw8 }} />
          }
          value={searchValue}
          onChange={event => setSearchValue(event.target.value)}
        />

        <div className="account-list">
          <ul /* className="scroll-content" antsomi-scroll-box */>
            <Scrollbars style={{ width: '100%', height: showAllAccount ? '364px' : '410px' }}>
              {recentData?.value?.length ? (
                <>
                  <Text className="recent-text">Recent</Text>
                  {recentData?.value
                    .map(accountId => accountData?.find(account => account.userId === accountId))
                    .filter(
                      (accountId, index, self) =>
                        Boolean(accountId) && index === self.indexOf(accountId),
                    )
                    .map(account => (
                      <AccountItem
                        key={account!.userId}
                        userName={account!.userName}
                        userId={account!.userId}
                        onClick={handleSelectAccount}
                        isSelected={+currentAccount === account!.userId}
                      />
                    ))}
                  <Divider style={{ margin: '8px 0', color: THEME.token?.bw3 }} />
                </>
              ) : null}
              {stateAccountData
                ?.filter(
                  account =>
                    !recentData?.value.includes(account.userId) &&
                    searchStringQuery(account.userName, searchValue),
                )
                .map(account => (
                  <AccountItem
                    key={account.userId}
                    userName={account.userName}
                    userId={account.userId}
                    onClick={handleSelectAccount}
                    isSelected={+currentAccount === account.userId}
                  />
                ))}
            </Scrollbars>
          </ul>
        </div>

        {showAllAccount ? (
          <Text
            className={`all-account ${isAllAccount ? 'is-selected' : ''}`}
            onClick={() => handleSelectAccount('all')}
          >
            <strong>All accounts</strong>
          </Text>
        ) : null}
      </AccountSelectionStyled>
    );
  },
);
