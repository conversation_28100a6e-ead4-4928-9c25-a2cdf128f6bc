// Libraries
import React, { ReactNode } from 'react';
import type { DrawerProps, MenuProps as AntdProps, FlexProps } from 'antd';
import { ItemType } from 'antd/es/menu/hooks/useItems';

export type TDefaultSize = 'max' | 'min';

export interface DrawerDetailProps extends Omit<DrawerProps, 'closeIcon' | 'title'> {
  name?: string;
  fullScreen?: boolean;
  maxWidth?: string | number;
  minWidth?: string | number;
  defaultSize?: TDefaultSize;

  headerProps?: DrawerHeaderProps;
  menuProps?: DrawerDetailMenuProps;
  closeIconProps?: DrawerDetailCloseIconProps;
}

export interface DrawerDetailCloseIconProps extends React.HtmlHTMLAttributes<HTMLDivElement> {
  show?: boolean;
}

export interface DrawerHeaderProps extends Omit<FlexProps, 'children'> {
  height?: React.CSSProperties['height'];
  showBorderBottom?: boolean;
  children?: ReactNode;
}

export interface DrawerDetailMenuProps extends Omit<AntdProps, 'onClick'> {
  show?: boolean;
  showExpandButton?: boolean;
  showClose?: boolean;
  footer?: ReactNode;
  onClick?: (item: ItemType) => void;
}
