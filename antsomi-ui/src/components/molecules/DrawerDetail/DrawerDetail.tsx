// Libraries
import { isNil } from 'lodash';
import React, { useCallback, useEffect, useMemo } from 'react';

// Components
import { Flex, Icon, Tooltip } from '../../atoms';

// Styled
import {
  CloseBtn,
  Content,
  DrawerHeader,
  Left<PERSON><PERSON>u,
  <PERSON>uFooter,
  <PERSON>uI<PERSON>,
  StyledDrawer,
} from './styled';

// Types
import type { DrawerDetailProps } from './types';

// Constants
import {
  BREAK_POINT,
  DRAWER_DETAIL_LOCAL_STORAGE_KEY,
  DRAWER_LARGE_MAX_WIDTH,
  DRAWER_LARGE_MIN_WIDTH,
  DRAWER_SMALL_MAX_WIDTH,
  DRAWER_SMALL_MIN_WIDTH,
} from './constants';
import { ToggleDrawerSizeButton } from './components';

// Hooks
import { useMediaQuery, useToggle } from '@antscorp/antsomi-ui/es/hooks';

// Utils
import { safeParseJson } from '@antscorp/antsomi-ui/es/utils';
import { POST_MESSAGE_TYPES } from '@antscorp/antsomi-ui/es/constants';

export const DrawerDetail: React.FC<DrawerDetailProps> = props => {
  // Props
  const {
    width,
    fullScreen: fullScreenProp = false,
    children,
    menuProps,
    closeIconProps,
    maxWidth,
    minWidth,
    defaultSize = 'max',
    headerProps,
    name,
    destroyOnClose = true,
    closable = false,
    ...restProps
  } = props;
  const {
    show: showMenu = true,
    showExpandButton = true,
    showClose = true,
    items,
    selectedKeys,
    footer,
    onClick = () => {},
  } = menuProps || {};
  const { children: headerChildren, ...restOfHeaderProps } = headerProps || {};
  const { onClose = () => {} } = props;
  const {
    show: showCloseIcon,
    onClick: onCloseIconClick = () => {},
    ...restOfCloseIcon
  } = closeIconProps || {};

  // Hooks
  const matchesSmallScreen = useMediaQuery(`(max-width: ${BREAK_POINT})`);

  // State
  const [collapseDrawer, toggleCollapseDrawer] = useToggle(false);
  const [fullScreen, toggleFullScreen] = useToggle(fullScreenProp);

  // Handlers
  const handleToggleDrawerDetailFullscreen = useCallback(
    (event: MessageEvent) => {
      const { type, data } = event.data || {};
      const { name: payloadName, value } = data || {};

      if (type === POST_MESSAGE_TYPES.TOGGLE_DRAWER_DETAIL_FULLSCREEN && payloadName === name) {
        toggleFullScreen(isNil(value) ? undefined : value);
      }
    },
    [name, toggleFullScreen],
  );

  // Effects
  useEffect(() => {
    window.addEventListener('message', handleToggleDrawerDetailFullscreen);

    return () => {
      window.removeEventListener('message', handleToggleDrawerDetailFullscreen);
    };
  }, [handleToggleDrawerDetailFullscreen]);

  useEffect(() => {
    toggleFullScreen(fullScreenProp);
  }, [fullScreenProp, toggleFullScreen]);

  useEffect(() => {
    if (matchesSmallScreen) {
      toggleCollapseDrawer(false);
    }

    switch (defaultSize) {
      case 'max':
        toggleCollapseDrawer(false);
        break;
      case 'min':
      default:
        toggleCollapseDrawer(true);
        break;
    }
  }, [matchesSmallScreen, defaultSize, toggleCollapseDrawer]);

  // Get cache collapse from local storage
  useEffect(() => {
    const localStorageValues = safeParseJson(
      localStorage.getItem(DRAWER_DETAIL_LOCAL_STORAGE_KEY),
      {},
    );

    const cachedCollapse = localStorageValues[name || ''];

    if (!isNil(cachedCollapse)) {
      toggleCollapseDrawer(cachedCollapse);
    }
  }, [name, toggleCollapseDrawer]);

  // Memo
  const drawerWidth = useMemo(() => {
    if (fullScreen) return '100vw';

    if (width) return width;

    /**
     * Check if drawer is collapsed
     * Case 1: Large screen (> 1440px)
     * Case 2: Small screen (<= 1440px)
     */
    switch (collapseDrawer) {
      case false: {
        if (maxWidth) return maxWidth;

        if (matchesSmallScreen) return DRAWER_SMALL_MAX_WIDTH;

        return DRAWER_LARGE_MAX_WIDTH;
      }

      case true:
      default: {
        if (minWidth) return minWidth;

        if (matchesSmallScreen) return DRAWER_SMALL_MIN_WIDTH;

        return DRAWER_LARGE_MIN_WIDTH;
      }
    }
  }, [collapseDrawer, fullScreen, matchesSmallScreen, maxWidth, minWidth, width]);

  const handleToggleDrawerSize = () => {
    const localStorageValues = safeParseJson(
      localStorage.getItem(DRAWER_DETAIL_LOCAL_STORAGE_KEY),
      {},
    );

    toggleCollapseDrawer(!collapseDrawer);

    /**
     * Set local storage last collapsed
     */
    if (name) {
      localStorage.setItem(
        DRAWER_DETAIL_LOCAL_STORAGE_KEY,
        JSON.stringify({ ...localStorageValues, [name || '']: !collapseDrawer }),
      );
    }
  };

  return (
    <StyledDrawer
      push={false}
      closable={closable}
      width={drawerWidth}
      motion={{
        visible: false,
        motionAppear: false,
        motionEnter: false,
        motionLeave: false,
      }}
      maskMotion={{
        visible: false,
        motionAppear: false,
        motionEnter: false,
        motionLeave: false,
      }}
      classNames={{
        body: 'drawer-detail-body',
      }}
      contentWrapperStyle={{
        transition: 'none',
      }}
      maskStyle={{
        transition: 'none',
      }}
      destroyOnClose={destroyOnClose}
      {...restProps}
    >
      {showCloseIcon && (
        <div
          className="close-btn"
          {...restOfCloseIcon}
          onClick={e => {
            onCloseIconClick(e);
            onClose(e);
          }}
        >
          <Icon type="icon-ants-remove-slim" size={14} />
        </div>
      )}

      {showExpandButton && !fullScreen && (
        <ToggleDrawerSizeButton
          style={{
            ...(!showMenu && { background: '#ffffff' }),
          }}
          onClick={() => handleToggleDrawerSize()}
          collapse={collapseDrawer}
        />
      )}

      {showMenu && (
        <LeftMenu data-test="left-menu" className="animate__animated animate__fadeIn">
          <div>
            {showClose && (
              <CloseBtn data-test="close-btn" {...restOfCloseIcon} onClick={onClose}>
                <Icon type="icon-ants-remove-slim" size={14} />
              </CloseBtn>
            )}

            <Flex vertical gap={10} align="center" justify="center">
              {items?.map((item: any) => (
                <Tooltip
                  key={item?.key}
                  title={item?.label}
                  mouseEnterDelay={0.3}
                  placement="right"
                >
                  <MenuItem
                    key={item?.key}
                    className={selectedKeys?.includes(item.key) ? 'active' : ''}
                    onClick={() => onClick && onClick(item)}
                  >
                    {item.icon}
                  </MenuItem>
                </Tooltip>
              ))}
            </Flex>
          </div>

          <MenuFooter>{footer}</MenuFooter>
        </LeftMenu>
      )}
      <Content className="drawer-detail-content">
        {headerProps?.children && (
          <DrawerHeader align="center" {...restOfHeaderProps}>
            {headerChildren}
          </DrawerHeader>
        )}
        {children}
      </Content>
    </StyledDrawer>
  );
};
