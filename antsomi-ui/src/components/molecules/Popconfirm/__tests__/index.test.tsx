import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Components
import { Button } from '../../../atoms';
import { Popconfirm } from '../Popconfirm';

describe('<Popconfirm />', () => {
  it('should display a Popconfirm ', async () => {
    render(
      <Popconfirm title="Are you sure?" okText="Yes" cancelText="No">
        <Button>Click me</Button>
      </Popconfirm>,
    );

    const button = screen.getByText('Click me');
    const user = userEvent.setup();
    await user.click(button);

    expect(await screen.findByText('Are you sure?')).toBeInTheDocument();
  });
  it('should not render Popconfirm when disabled', () => {
    render(
      <Popconfirm title="Are you sure?" okText="Yes" cancelText="No" disabled>
        <Button>Click me</Button>
      </Popconfirm>,
    );

    expect(screen.queryByText('Are you sure?')).not.toBeInTheDocument();
  });
  it('should close the Popconfirm when clicking outside', async () => {
    render(
      <div>
        <Popconfirm title="Are you sure?" okText="Yes" cancelText="No">
          <Button>Click me</Button>
        </Popconfirm>
      </div>,
    );

    const button = screen.getByText('Click me');
    const user = userEvent.setup();
    await user.click(button);

    expect(await screen.findByText('Are you sure?')).toBeInTheDocument();

    const outside = document.body;
    await user.click(outside);

    await waitFor(() => {
      expect(document.querySelector('.ant-popover-hidden')).toBeInTheDocument();
    });
  });
  it('should match snapshot', () => {
    const loadingIndicator = render(<Popconfirm title="Are you sure?" />);
    expect(loadingIndicator.container.firstChild).toMatchSnapshot();
  });
});
