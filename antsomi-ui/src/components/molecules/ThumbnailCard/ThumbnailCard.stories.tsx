// Libraries
import React from 'react';
import { <PERSON><PERSON>bj, <PERSON>a, <PERSON>Fn } from '@storybook/react';

// Components
import { ThumbnailCard } from '.';
import { Button, Flex, Typography } from '../../atoms';

// Constants
import {
  THUMBNAIL_CARD_DEFAULT_WIDTH,
  THUMBNAIL_CARD_DEFAULT_HEIGHT,
  THUMBNAIL_URL,
} from './constants';

// Types
import { TThumbnailCardId } from './types';

export default {
  title: 'Molecules/ThumbnailCard',
  component: ThumbnailCard,
  argTypes: {
    name: {
      name: 'name',
      defaultValue: undefined,
      description: "Thumbnail card's name",
      table: {
        type: { summary: 'text' },
        defaultValue: { summary: `-` },
      },
      control: 'text',
    },
    id: {
      name: 'id',
      defaultValue: undefined,
      description: 'Thumbnail card id',
      table: {
        type: { summary: 'text' },
        defaultValue: { summary: `-` },
      },
      control: 'text',
    },
    width: {
      name: 'width',
      defaultValue: THUMBNAIL_CARD_DEFAULT_WIDTH,
      description: "Customize thumbnail card's width",
      table: {
        type: { summary: 'number' },
        defaultValue: { summary: `${THUMBNAIL_CARD_DEFAULT_WIDTH}` },
      },
      control: 'number',
    },
    height: {
      name: 'height',
      defaultValue: THUMBNAIL_CARD_DEFAULT_HEIGHT,
      description: "Customize thumbnail card's height",
      table: {
        type: { summary: 'number' },
        defaultValue: { summary: `${THUMBNAIL_CARD_DEFAULT_HEIGHT}` },
      },
      control: 'number',
    },
    thumbnail: {
      name: 'thumbnail',
      defaultValue: THUMBNAIL_URL,
      description: 'Customize thumbnail url',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: THUMBNAIL_URL },
      },
      control: null,
    },
    removable: {
      name: 'removable',
      defaultValue: true,
      description: 'To active remove feature',
      table: {
        type: { summary: 'boolean' },
        defaultValue: {
          summary: `true`,
        },
      },
      control: 'boolean',
    },
    actionAvailable: {
      name: 'actionAvailable',
      defaultValue: true,
      description: 'To show/hide edit/preview buttons with all their actions',
      table: {
        type: { summary: 'boolean' },
        defaultValue: {
          summary: `true`,
        },
      },
      control: 'boolean',
    },
    editText: {
      name: 'editText',
      defaultValue: 'Edit thumbnail',
      description: 'Customize edit text',
      table: {
        type: { summary: 'text' },
        defaultValue: { summary: `Edit thumbnail` },
      },
      control: 'text',
    },
    previewText: {
      name: 'previewText',
      defaultValue: 'Preview',
      description: 'Customize preview text',
      table: {
        type: { summary: 'text' },
        defaultValue: { summary: `Preview` },
      },
      control: 'text',
    },
    onClickEdit: {
      name: 'onClickEdit',
      defaultValue: undefined,
      description: 'To handle click on edit button',
      table: {
        type: { summary: '(id: TThumbnailCardId) => void' },
        defaultValue: {
          summary: `-`,
        },
      },
      control: null,
    },
    onClickPreview: {
      name: 'onClickPreview',
      defaultValue: undefined,
      description: 'To handle click on preview button',
      table: {
        type: { summary: '(id: TThumbnailCardId) => void' },
        defaultValue: {
          summary: `-`,
        },
      },
      control: null,
    },
    onClickWrapper: {
      name: 'onClickWrapper',
      defaultValue: undefined,
      description: 'To handle click on thumbnail card wrapper',
      table: {
        type: { summary: '(id: TThumbnailCardId) => void' },
        defaultValue: {
          summary: `-`,
        },
      },
      control: null,
    },
    removeModalProps: {
      name: 'removeModalProps',
      defaultValue: undefined,
      description: 'To customize remove modal',
      table: {
        type: { summary: 'TRemoveModalProps' },
        defaultValue: { summary: `-` },
      },
      control: 'object',
    },
  },
  parameters: {
    docs: {
      description: {
        component: 'A Thumbnail Card. \n',
      },
    },
  },
} as Meta<typeof ThumbnailCard>;

export const Default = {
  args: {},
};

export const BasicUsage: StoryObj<any> = {
  render: () => {
    // Handlers
    const onClickEdit = (id: TThumbnailCardId) => {
      console.log('Click edit: ', id);
    };

    const onClickPreview = (id: TThumbnailCardId) => {
      console.log('Click preview: ', id);
    };

    const onConfirmRemove = async (id: TThumbnailCardId) => {
      console.log('Click remove: ', id);
    };

    const onClickWrapper = async (id: TThumbnailCardId) => {
      console.log('Click wrapper: ', id);
    };

    return (
      <ThumbnailCard
        id="thumbnail-card-id"
        name="Thumbnail Card"
        width={250}
        height={250}
        thumbnail={THUMBNAIL_URL}
        removable
        editBtnProps={{
          text: 'Edit Thumbnail Card',
          onClick: onClickEdit,
        }}
        previewBtnProps={{
          text: 'Preview',
          onClick: onClickPreview,
        }}
        onClickWrapper={onClickWrapper}
        removeModalProps={{
          onOk: onConfirmRemove,
        }}
      />
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Simple Thumbnail Card with actions.',
      },
    },
  },
};

export const WithoutButtonActions: StoryObj<any> = {
  render: () => {
    // Handlers
    const onClickWrapper = async (id: TThumbnailCardId) => {
      console.log('Click wrapper: ', id);
    };

    return (
      <ThumbnailCard
        id="thumbnail-card-id"
        name="Thumbnail Card"
        thumbnail={THUMBNAIL_URL}
        actionAvailable={false}
        onClickWrapper={onClickWrapper}
      />
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Thumbnail Card without button actions.',
      },
    },
  },
};

export const WithActionButtons: StoryObj<any> = {
  render: args => (
    <Flex align="center" gap={10}>
      <Flex vertical gap={5}>
        <Typography.Text>With default Action Buttons</Typography.Text>
        <ThumbnailCard
          id="thumbnail-card-id"
          name="Thumbnail Card"
          thumbnail={THUMBNAIL_URL}
          removable={false}
          actionButtons={{
            EDIT: {
              buttonProps: {
                onClick: () => console.log('Click Edit'),
              },
            },
            DUPLICATE: {
              buttonProps: {
                onClick: () => console.log('Click Duplicate'),
              },
              buttonProps: {},
              onClick: () => console.log('Click Edit'),
            },
            DUPLICATE: {
              buttonProps: {},
              onClick: () => console.log('Click Duplicate'),
            },
          }}
        />
      </Flex>
      <Flex vertical gap={5}>
        <Typography.Text>With custom Action Buttons</Typography.Text>
        <ThumbnailCard<['MORE', 'CUSTOM']>
          id="thumbnail-card-id"
          name="Thumbnail Card"
          thumbnail={THUMBNAIL_URL}
          removable={false}
          actionButtons={{
            MORE: {
              label: 'More',
              icon: 'icon-ants-plus-circle',
            },
            CUSTOM: {
              customRender: () => <div style={{ backgroundColor: '#fff', padding: 5 }}>Custom</div>,
            },
          }}
        />
      </Flex>
    </Flex>
  ),
  args: {},
  parameters: {
    docs: {
      description: {
        story: 'Thumbnail Card with action buttons.',
      },
    },
  },
};
