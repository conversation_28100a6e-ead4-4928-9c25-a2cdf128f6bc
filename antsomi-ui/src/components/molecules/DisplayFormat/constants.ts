// Libraries
import dayjs from 'dayjs';

// Locales
import { translate, translations } from '@antscorp/antsomi-locales';

// Types
import { TDataType } from './types';
import { TDatetimeFormatSetting, TNumberFormatSettings } from './components';

export const DYNAMIC_CONTENT_SETTING_KEY = {
  EVENT: 'event',
  INDEX: 'index',
  ATTRIBUTE: 'attribute',
  DYNAMIC_CONTENT_NUMBERIC_ATTR_FORMAT_SETTINGS: 'numberFormatSettings',
  DYNAMIC_CONTENT_DATETIME_ATTR_FORMAT_SETTINGS: 'datetimeFormatSettings',
  DISPLAY_FORMAT_TYPE: 'dfType',
  DYNAMIC_CONTENT_TYPE: 'type',
  PROMOTION_POOL: 'pool',
  SOURCE: 'source',
  FUNCTION: 'customFunction',
  PERNAME: 'templateName',
  SAVE_AS_TEMPLATE: 'saveAsTemplate',
  DATA_TYPE: 'dataType',
};

export const DYNAMIC_CONTENT_TYPE_KEY = {
  BO_SETTINGS: 'bo-settings',
  CUSTOMER_ATTRIBUTE: 'customer-attribute',
  EVENT_ATTRIBUTE: 'event-attribute',
  PROMOTION_CODE: 'promotion-code',
  VISITOR_ATTRIBUTE: 'visitor-attribute',
  CUSTOM_FUNCTION: 'custom',
} as const;

export const DYNAMIC_CONTENT_TYPE: {
  [key: string]: {
    index: number;
    label: string;
    value: string;
  };
} = {
  BO_SETTINGS: {
    index: 4,
    label: '',
    value: DYNAMIC_CONTENT_TYPE_KEY.BO_SETTINGS,
  },
  CUSTOMER_ATTRIBUTE: {
    index: 1,
    label: 'Customer Attribute',
    value: DYNAMIC_CONTENT_TYPE_KEY.CUSTOMER_ATTRIBUTE,
  },
  EVENT_ATTRIBUTE: {
    index: 2,
    label: 'Event Attribute',
    value: DYNAMIC_CONTENT_TYPE_KEY.EVENT_ATTRIBUTE,
  },
  PROMOTION_CODE: {
    index: 3,
    label: 'Promotion Code',
    value: DYNAMIC_CONTENT_TYPE_KEY.PROMOTION_CODE,
  },
  VISITOR_ATTRIBUTE: {
    index: 0,
    label: 'Visitor Attribute',
    value: DYNAMIC_CONTENT_TYPE_KEY.VISITOR_ATTRIBUTE,
  },
};

export const DYNAMIC_CONTENT_ATTR_DF_TYPE: { label: string; value: string; dataType: TDataType }[] =
  [
    {
      label: translate(translations._TITL_NUMBER),
      value: 'number',
      dataType: 'number',
    },
    {
      label: translate(translations._TITL_PERCENTAGE),
      value: 'percentage',
      dataType: 'number',
    },
    {
      label: translate(translations._TITL_CURRENCY),
      value: 'currency',
      dataType: 'number',
    },
    {
      label: translate(translations._TITL_DATE_TIME_DEFAULT_FORMAT),
      value: 'datetime',
      dataType: 'datetime',
    },
  ];

export const DATA_TYPE: {
  [key: string]: {
    index: number;
    label: string;
    value: string;
  };
} = {
  NUMBER: {
    index: 0,
    label: 'Number',
    value: 'number',
  },
  DATE_TIME: {
    index: 1,
    label: 'Datetime',
    value: 'datetime',
  },
  STRING: {
    index: 2,
    label: 'String',
    value: 'string',
  },
};

export const ATTRIBUTE_STATUS = {
  4: {
    value: 4,
    label: 'Archive',
    errorMessage: 'This attribute is not available',
  },
  DELETE: {
    value: 3,
    label: 'Delete',
    errorMessage: 'This attribute does not exist',
  },
};

export const ATTRIBUTE_TYPE = {
  ITEM: 2,
};

export const BO_STATUS = {
  4: {
    value: 4,
    label: 'Archive',
    errorMessage: 'This BO is archive',
  },
  DELETE: {
    value: 3,
    label: 'Delete',
    errorMessage: 'This BO does not exist',
  },
};

export const ATTRIBUTE_NUMBERIC_FORMAT_OPTION = {
  DECIMAL_SEPARATOR: {
    COMMA: {
      index: 0,
      label: translate(translations._PERSONALIZE_PROCESS_CODE_DELIMETER_1),
      value: ',',
    },
    DOT: {
      index: 1,
      label: translate(translations._PERSONALIZE_PROCESS_CODE_DELIMETER_2),
      value: '.',
    },
  },
  GROUPING_SEPARATOR: {
    COMMA: {
      index: 0,
      label: translate(translations._PERSONALIZE_PROCESS_CODE_DELIMETER_1),
      value: ',',
    },
    DOT: {
      index: 1,
      label: translate(translations._PERSONALIZE_PROCESS_CODE_DELIMETER_2),
      value: '.',
    },
    NONE: {
      index: 2,
      label: translate(translations._TITL_NONE),
      value: 'none',
    },
  },
} as const;

export const COLLECTION_STATUS = {
  2: {
    value: 2,
    label: 'Disable',
    errorMessage: 'This collection is disabled',
  },
  4: {
    value: 4,
    label: 'Archive',
    errorMessage: 'This collection is not available',
  },
  DELETE: {
    value: 3,
    label: 'Delete',
    errorMessage: 'This collection does not exist',
  },
};

export const NUMBER_EXAMPLE = 1234.56789;

export const DATE_TIME_EXAMPLE = dayjs('2020-04-15 18:23:11');

export const DATE_FORMAT_OPTIONS = [
  {
    label: 'MM/DD/YYYY',
    value: 'MM/DD/YYYY',
  },
  {
    label: 'DD/MM/YYYY',
    value: 'DD/MM/YYYY',
  },
  // {
  //   label: 'YYYY/MM/DD',
  //   value: 'YYYY/MM/DD',
  // },
];

export const TIME_FORMAT_OPTIONS = [
  {
    label: 'AM/PM',
    value: '12hour',
  },
  {
    label: translate(translations._TIME_24_HOUR_CLOCK),
    value: '24hour',
  },
];

export const DATE_FORMAT_LIST = {
  [DATE_FORMAT_OPTIONS[0].value]: {
    SHORT: translate(translations._DATE_SHORT_FORMAT1),
    MEDIUM: translate(translations._DATE_MEDIUM_FORMAT1),
    LONG: translate(translations._DATE_LONG_FORMAT1),
  },
  [DATE_FORMAT_OPTIONS[1].value]: {
    SHORT: translate(translations._DATE_SHORT_FORMAT2),
    MEDIUM: translate(translations._DATE_MEDIUM_FORMAT2),
    LONG: translate(translations._DATE_LONG_FORMAT2),
  },
};

export const TIME_FORMAT_LIST = {
  [TIME_FORMAT_OPTIONS[0].value]: {
    SHORT: translate(translations._TIME_SHORT_FOTMAT1),
    MEDIUM: translate(translations._TIME_MEDIUM_FORMAT1),
    LONG: translate(translations._TIME_LONG_FORMAT1),
  },
  [TIME_FORMAT_OPTIONS[1].value]: {
    SHORT: translate(translations._TIME_SHORT_FOTMAT2),
    MEDIUM: translate(translations._TIME_MEDIUM_FORMAT2),
    LONG: translate(translations._TIME_LONG_FORMAT2),
  },
};

export const GROUPING_OPTIONS = {};

export const NUMBER_SETTINGS_DEFAULT: TNumberFormatSettings = {
  grouping: ',',
  decimal: '.',
  decimalPlaces: 2,
  isCompact: false,
  currencyCode: 'USD',
  prefixType: 'code',
  type: 'number',
};

export const DATETIME_SETTINGS_DEFAULT: TDatetimeFormatSetting = {
  hasDateFormat: true,
  hasTimeFormat: true,
  dateParseOption: 'medium',
  timeParseOption: 'medium',
  dateParseFormat: 'MM/DD/YYYY',
  timeParseFormat: '12hour',
  type: 'datetime',
};

export const DYNAMIC_CONTENT_DF_FORMAT_TYPE = {
  number: 'number',
  datetime: 'datetime',
};

export const TYPE_FORMAT_OPTIONS = {
  SHORT: 'short',
  MEDIUM: 'medium',
  LONG: 'long',
};

export const DATE_FORMAT_LIST_TEST = {
  [DATE_FORMAT_OPTIONS[0].value]: {
    [TYPE_FORMAT_OPTIONS.SHORT]: {
      label: translate(translations._DATE_SHORT_FORMAT1),
      value: 'MM/DD/YYYY',
    },
    [TYPE_FORMAT_OPTIONS.MEDIUM]: {
      label: translate(translations._DATE_MEDIUM_FORMAT1),
      value: 'MMM DD, YYYY',
    },
    [TYPE_FORMAT_OPTIONS.LONG]: {
      label: translate(translations._DATE_LONG_FORMAT1),
      value: 'MMMM DD, YYYY',
    },
  },
  [DATE_FORMAT_OPTIONS[1].value]: {
    [TYPE_FORMAT_OPTIONS.SHORT]: {
      label: translate(translations._DATE_SHORT_FORMAT2),
      value: 'DD/MM/YYYY',
    },
    [TYPE_FORMAT_OPTIONS.MEDIUM]: {
      label: translate(translations._DATE_MEDIUM_FORMAT2),
      value: 'DD MMM YYYY',
    },
    [TYPE_FORMAT_OPTIONS.LONG]: {
      label: translate(translations._DATE_LONG_FORMAT2),
      value: 'DD MMMM YYYY',
    },
  },
};

export const TIME_FORMAT_LIST_TEST = {
  [TIME_FORMAT_OPTIONS[0].value]: {
    [TYPE_FORMAT_OPTIONS.SHORT]: {
      label: translate(translations._TIME_SHORT_FOTMAT1),
      value: 'h:mm A',
    },
    [TYPE_FORMAT_OPTIONS.MEDIUM]: {
      label: translate(translations._TIME_MEDIUM_FORMAT1),
      value: 'h:mm:ss A',
    },
    [TYPE_FORMAT_OPTIONS.LONG]: {
      label: translate(translations._TIME_LONG_FORMAT1),
      value: 'h:mm:ss A [GMT]',
    },
  },
  [TIME_FORMAT_OPTIONS[1].value]: {
    [TYPE_FORMAT_OPTIONS.SHORT]: {
      label: translate(translations._TIME_SHORT_FOTMAT2),
      value: 'H:mm',
    },
    [TYPE_FORMAT_OPTIONS.MEDIUM]: {
      label: translate(translations._TIME_MEDIUM_FORMAT2),
      value: 'H:mm:ss',
    },
    [TYPE_FORMAT_OPTIONS.LONG]: {
      label: translate(translations._TIME_LONG_FORMAT2),
      value: 'H:mm:ss [GMT]',
    },
  },
};
