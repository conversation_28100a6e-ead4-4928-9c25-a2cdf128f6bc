// Libraries
import React, { useEffect, useMemo, useState } from 'react';

// Components
import { TDatetimeFormatSetting, TNumberFormatSettings } from './components';
import { DisplayFormatSettings, TDisplayFormat } from './DisplayFormatSettings';

// Types

import { Select } from '../Select';

// Constants
import {
  DYNAMIC_CONTENT_ATTR_DF_TYPE,
  NUMBER_SETTINGS_DEFAULT,
  DATETIME_SETTINGS_DEFAULT,
  DYNAMIC_CONTENT_DF_FORMAT_TYPE,
} from './constants';

// Types
import { PayloadInfo } from '@antscorp/antsomi-ui/es/types';
import { useUpdateEffect } from '@antscorp/antsomi-ui/es/hooks';

export interface DisplayFormatSelectProps extends React.HTMLAttributes<HTMLDivElement> {
  onSettingChange: (settings: TDatetimeFormatSetting | TNumberFormatSettings) => void;
  setting: TDatetimeFormatSetting | TNumberFormatSettings | undefined;
  formatType: string;
  disabled?: boolean;
  infos?: PayloadInfo;
  url?: string;
}

export const DisplayFormatSelect: React.FC<DisplayFormatSelectProps> = props => {
  const {
    onSettingChange,
    formatType,
    setting = formatType === 'datetime' ? DATETIME_SETTINGS_DEFAULT : NUMBER_SETTINGS_DEFAULT,
    disabled,
    infos,
    url,
    ...rest
  } = props;
  const { type: settingType = DYNAMIC_CONTENT_DF_FORMAT_TYPE[formatType] } = setting || {};

  // Memo
  const typeOptions = useMemo(
    () => DYNAMIC_CONTENT_ATTR_DF_TYPE.filter(dfType => dfType.dataType === formatType),
    [formatType],
  );

  // Effects
  useUpdateEffect(() => {
    onSettingChange(
      formatType === 'datetime' ? DATETIME_SETTINGS_DEFAULT : NUMBER_SETTINGS_DEFAULT,
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formatType]);

  return (
    typeOptions.length > 0 && (
      <div style={{ display: 'flex', flexDirection: 'column' }} {...rest}>
        <Select
          disabled={disabled}
          value={settingType}
          options={typeOptions}
          onSelect={type =>
            onSettingChange({
              ...(setting as any),
              type: type as TDisplayFormat,
            })
          }
        />
        <DisplayFormatSettings
          disabled={disabled}
          onSettingsChange={setting => {
            onSettingChange(setting);
          }}
          displayFormatType={settingType as TDisplayFormat}
          formatSettings={setting}
          url={url ?? ''}
          infos={infos ?? {}}
        />
      </div>
    )
  );
};
