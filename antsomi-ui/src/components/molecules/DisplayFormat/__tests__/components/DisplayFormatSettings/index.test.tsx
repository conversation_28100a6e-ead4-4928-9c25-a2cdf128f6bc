// Unit tests for: DisplayFormatSettings

import { DATETIME_SETTINGS_DEFAULT, NUMBER_SETTINGS_DEFAULT } from '../../../constants';
import { DisplayFormatSettings } from '../../../DisplayFormatSettings';
import { formatDatetimeDF, formatNumberDF } from '../../../utils';
import { fireEvent, render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock the utils functions
jest.mock('../../utils', () => {
  const actual = jest.requireActual('../../utils');
  return {
    ...actual,
    formatDatetimeDF: jest.fn(() => '03 February, 2024'),
    formatNumberDF: jest.fn(() => '1,234.56 USD'),
  };
});

describe('DisplayFormatSettings component', () => {
  const mockOnSettingsChange = jest.fn();

  const mockInfos = { url: 'http://example.com', userId: '123' };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Happy Paths', () => {
    it('should render number format display correctly', () => {
      // Arrange
      (formatNumberDF as jest.Mock).mockReturnValue('1,000');
      render(
        <DisplayFormatSettings
          onSettingsChange={mockOnSettingsChange}
          formatSettings={NUMBER_SETTINGS_DEFAULT}
          displayFormatType="number"
          infos={mockInfos}
          url="http://example.com"
        />,
      );

      // Act & Assert
      expect(screen.getByText(/1,000/)).toBeInTheDocument();
    });

    it('should render datetime format display correctly', () => {
      // Arrange
      (formatDatetimeDF as jest.Mock).mockReturnValue('01/01/2023');
      render(
        <DisplayFormatSettings
          onSettingsChange={mockOnSettingsChange}
          formatSettings={DATETIME_SETTINGS_DEFAULT}
          displayFormatType="datetime"
          infos={mockInfos}
          url="http://example.com"
        />,
      );

      // Act & Assert
      expect(screen.getByText(/01\/01\/2023/)).toBeInTheDocument();
    });

    it('should toggle detail edit view on button click', () => {
      // Arrange
      render(
        <DisplayFormatSettings
          onSettingsChange={mockOnSettingsChange}
          formatSettings={NUMBER_SETTINGS_DEFAULT}
          displayFormatType="number"
          infos={mockInfos}
          url="http://example.com"
        />,
      );

      const button = screen.getByRole('button');

      // Act
      fireEvent.click(button);

      // Assert
      expect(screen.getByRole('button')).toHaveStyle('background-color: #DEEFFE');
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty formatSettings gracefully', () => {
      // Arrange
      render(
        <DisplayFormatSettings
          onSettingsChange={mockOnSettingsChange}
          formatSettings={{}}
          displayFormatType="number"
          infos={mockInfos}
          url="http://example.com"
        />,
      );

      // Act & Assert
      expect(screen.getByText(/Preview/)).toBeInTheDocument();
    });

    it('should not render format setting component if displayFormatType is invalid', () => {
      // Arrange
      render(
        <DisplayFormatSettings
          onSettingsChange={mockOnSettingsChange}
          formatSettings={NUMBER_SETTINGS_DEFAULT}
          displayFormatType={'invalidType' as any}
          infos={mockInfos}
          url="http://example.com"
        />,
      );

      // Act & Assert
      expect(screen.queryByText(/FormatNumber/)).not.toBeInTheDocument();
      expect(screen.queryByText(/FormatDatetime/)).not.toBeInTheDocument();
    });

    it('should disable button when disabled prop is true', () => {
      // Arrange
      render(
        <DisplayFormatSettings
          onSettingsChange={mockOnSettingsChange}
          formatSettings={NUMBER_SETTINGS_DEFAULT}
          displayFormatType="number"
          infos={mockInfos}
          url="http://example.com"
          disabled={true}
        />,
      );

      // Act & Assert
      expect(screen.getByRole('button')).toBeDisabled();
    });
  });
});

// End of unit tests for: DisplayFormatSettings
