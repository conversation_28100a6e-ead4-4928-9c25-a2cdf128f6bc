// Unit tests for: DisplayFormatSelect

import { TDatetimeFormatSetting, TNumberFormatSettings } from '../../../components';
import { DisplayFormatSelect } from '../../../DisplayFormatSelect';
import { fireEvent, render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mocking the Select and DisplayFormatSettings components
jest.mock('../../Select', () => ({
  Select: ({ disabled, value, options, onSelect }: any) => (
    <select disabled={disabled} value={value} onChange={e => onSelect(e.target.value)}>
      {options.map((option: any) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
}));

jest.mock('../DisplayFormatSettings', () => ({
  DisplayFormatSettings: ({ onSettingsChange, displayFormatType, formatSettings }: any) => (
    <div>
      <span>{displayFormatType}</span>
      <button onClick={() => onSettingsChange(formatSettings)}>Change Settings</button>
    </div>
  ),
}));

describe('DisplayFormatSelect() DisplayFormatSelect method', () => {
  const mockOnSettingChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Happy Path Tests
  describe('Happy Paths', () => {
    it('should render with initial settings and call onSettingChange', () => {
      const initialSetting: TDatetimeFormatSetting = {
        hasDateFormat: true,
        hasTimeFormat: false,
        dateParseFormat: 'DD/MM/YYYY',
        timeParseFormat: '24hour',
        dateParseOption: 'short',
        timeParseOption: 'medium',
        type: 'datetime',
      };

      render(
        <DisplayFormatSelect
          onSettingChange={mockOnSettingChange}
          setting={initialSetting}
          formatType="datetime"
        />,
      );

      expect(screen.getByText('datetime')).toBeInTheDocument();
      expect(mockOnSettingChange).toHaveBeenCalledWith(initialSetting);
    });

    it('should update type and settings when a new type is selected', () => {
      const initialSetting: TNumberFormatSettings = {
        // Assuming some initial number format settings
      };

      render(
        <DisplayFormatSelect
          onSettingChange={mockOnSettingChange}
          setting={initialSetting}
          formatType="number"
        />,
      );

      fireEvent.change(screen.getByRole('combobox'), { target: { value: 'datetime' } });

      expect(screen.getByText('datetime')).toBeInTheDocument();
    });
  });

  // Edge Case Tests
  describe('Edge Cases', () => {
    it('should handle undefined setting gracefully', () => {
      render(
        <DisplayFormatSelect
          onSettingChange={mockOnSettingChange}
          setting={undefined}
          formatType="datetime"
        />,
      );

      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    it('should handle empty typeOptions when formatType does not match', () => {
      render(
        <DisplayFormatSelect
          onSettingChange={mockOnSettingChange}
          setting={undefined}
          formatType="unknown"
        />,
      );

      expect(screen.queryByRole('combobox')).not.toBeInTheDocument();
    });

    it('should not call onSettingChange if no setting is provided', () => {
      render(
        <DisplayFormatSelect
          onSettingChange={mockOnSettingChange}
          setting={undefined}
          formatType="datetime"
        />,
      );

      expect(mockOnSettingChange).not.toHaveBeenCalled();
    });
  });
});

// End of unit tests for: DisplayFormatSelect
