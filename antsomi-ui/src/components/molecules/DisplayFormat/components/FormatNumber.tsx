// Libraries
import React, { useEffect, useMemo, useState } from 'react';

// Components
import { Radio } from '@antscorp/antsomi-ui/es/components/atoms';
import { Tooltip, Form, Select, Checkbox, Button } from 'antd';

// Json
import Currencies from '../currencies.json';

// Constants
import { ATTRIBUTE_NUMBERIC_FORMAT_OPTION } from '../constants';

// Services
import { getCurrencyList } from '@antscorp/antsomi-ui/es/services/MediaTemplateDesign/ListCurrency';

// Locales
import { translate, translations } from '@antscorp/antsomi-locales';
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';

// Types
import { TDisplayFormat } from '../DisplayFormatSettings';
import { PayloadInfo } from '@antscorp/antsomi-ui/es/types';
import {
  DecreaseDecimalIcon,
  IncreaseDecimalIcon,
  ViewDetailsInformationIcon,
} from '../../../icons';
import { CURRENCY_REGEX, DECIMAL_PLACES_REGEX } from '../regex';
import { useAppConfigContext } from '@antscorp/antsomi-ui/es/providers';
import { MEDIA_TEMPLATE_API } from '@antscorp/antsomi-ui/es/constants';

const groupingOptions = ATTRIBUTE_NUMBERIC_FORMAT_OPTION.GROUPING_SEPARATOR;
const decimalOptions = ATTRIBUTE_NUMBERIC_FORMAT_OPTION.DECIMAL_SEPARATOR;

export type TGroupingSeparator = (typeof groupingOptions)[keyof typeof groupingOptions]['value'];
export type TDecimalSeparator = (typeof decimalOptions)[keyof typeof decimalOptions]['value'];

export type TNumberFormatSettings = {
  decimalPlaces: number;
  grouping: TGroupingSeparator;
  decimal: TDecimalSeparator;
  isCompact: boolean;
  currencyCode: string;
  prefixType: 'symbol' | 'code';
  type?: TDisplayFormat;
};

type FormatNumberProps = {
  onSettingsChange?: (settingValues: TNumberFormatSettings) => void;
  // type: TDisplayFormat;
} & TNumberFormatSettings & { infos: PayloadInfo; url: string };

type LabeledValue = {
  label: string;
  value: string;
  disabled?: boolean;
};

export const FormatNumber = (props: FormatNumberProps) => {
  const { t, language } = i18nInstance;

  const { onSettingsChange, type: formatType, infos, url } = props;
  const { appConfig } = useAppConfigContext();
  const { auth, env } = appConfig || {};

  const { currencyCode, decimalPlaces, grouping, decimal, prefixType, isCompact } = props;

  const settingValues: TNumberFormatSettings = {
    decimalPlaces,
    grouping,
    decimal,
    isCompact,
    currencyCode,
    prefixType,
    type: formatType,
  };

  const [listCurrencyOptions, setListCurrencyOptions] = useState<LabeledValue[]>([]);
  const [isLoadingCurrencyOptions, setIsLoadingCurrencyOptions] = useState<boolean>(true);
  const [decimalPlacesLabel, setDecimalPlacesLabel] = useState<string>('');

  const onChangeSettingsHandle = (settingValues: TNumberFormatSettings) => {
    if (onSettingsChange) {
      onSettingsChange({
        ...settingValues,
        // type: displayType,
      });
    }
  };

  useEffect(() => {
    (async () => {
      if (listCurrencyOptions.length) {
        setListCurrencyOptions(prevOptions =>
          prevOptions.map(currencyOption => {
            currencyOption.label =
              prefixType === 'symbol'
                ? currencyOption.label.replace(
                    CURRENCY_REGEX,
                    `(${Currencies[currencyOption.value]?.symbol})`,
                  )
                : currencyOption.label.replace(CURRENCY_REGEX, `(${currencyOption.value})`);

            return currencyOption;
          }),
        );
      } else {
        setIsLoadingCurrencyOptions(true);
        try {
          const data = await getCurrencyList(
            { lang: language, url: `${MEDIA_TEMPLATE_API[env || 'sandbox-cdp']}/api/v1` },
            auth || {},
          );

          const currencies = data.reduce((accumulate: any, currency: any) => {
            if (Currencies[currency.code]) {
              accumulate.push({
                value: currency.code,
                label:
                  prefixType === 'symbol'
                    ? `${currency.name.replace(CURRENCY_REGEX, '')} (${Currencies[currency.code]?.symbol})`
                    : `${currency.name.replace(CURRENCY_REGEX, '')} (${currency.code})`,
              });
            } else {
              // console.log('library not found ', currency.code + ' - ' + currency.name);
            }

            return accumulate;
          }, [] as LabeledValue[]);

          setListCurrencyOptions(currencies);
        } catch (err) {
          setListCurrencyOptions([]);
        } finally {
          setIsLoadingCurrencyOptions(false);
        }
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [prefixType]);

  useEffect(() => {
    setDecimalPlacesLabel(
      translate(translations._DISPLAY_CHANGE_DECIMAL_PLACES).replace(
        DECIMAL_PLACES_REGEX,
        decimalPlaces.toString(),
      ),
    );
  }, [decimalPlaces]);

  const listGroupingSeparatorOptions = useMemo(
    (): LabeledValue[] =>
      Object.values(groupingOptions)
        .sort((a, b) => a.index - b.index)
        .map(grouping => ({
          ...grouping,
          ...(grouping.value === decimal && { disabled: true }),
        })),
    [decimal],
  );

  const listDecimalSeparatorOptions = useMemo(
    (): LabeledValue[] =>
      Object.values(decimalOptions)
        .sort((a, b) => a.index - b.index)
        .map(decimal => ({
          ...decimal,
          ...(decimal.value === grouping && { disabled: true }),
        })),
    [grouping],
  );

  return (
    <Form style={{ width: '100%', fontSize: '12px' }} labelAlign="left">
      {formatType === 'currency' && (
        <>
          <Form.Item
            labelCol={{ span: 10 }}
            wrapperCol={{ span: 14 }}
            label={translate(translations._TITL_CURRENCY)}
            colon={false}
          >
            <Radio.Group
              value={prefixType}
              onChange={({ target: { value } }) =>
                onChangeSettingsHandle({
                  ...settingValues,
                  prefixType: value as 'code' | 'symbol',
                })
              }
            >
              <Radio style={{ fontSize: 12 }} value="code">
                {
                  t(
                    translations.dynamicContent.modal.label.formNumberDisplayFormat.currencyCode,
                  ) as string
                }
              </Radio>
              <Radio style={{ fontSize: 12 }} value="symbol">
                {
                  t(
                    translations.dynamicContent.modal.label.formNumberDisplayFormat.currencySymbol,
                  ) as string
                }
              </Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item label=" " labelCol={{ span: 10 }} wrapperCol={{ span: 14 }} colon={false}>
            <Select
              style={{ width: 150 }}
              disabled={isLoadingCurrencyOptions}
              options={listCurrencyOptions}
              value={currencyCode}
              onChange={value =>
                onChangeSettingsHandle({
                  ...settingValues,
                  currencyCode: value,
                })
              }
              showSearch
            />
          </Form.Item>
        </>
      )}
      <Form.Item>
        <Checkbox
          style={{ fontSize: 12 }}
          checked={isCompact}
          onChange={({ target: { checked } }) => {
            onChangeSettingsHandle({
              ...settingValues,
              isCompact: checked,
            });
          }}
        >
          {translate(translations._TITL_EDIT_NUMBER_COMPACT) as string}
        </Checkbox>
      </Form.Item>

      <Form.Item
        label={decimalPlacesLabel}
        style={{ marginBottom: 12 }}
        colon={false}
        labelCol={{ span: 12 }}
        wrapperCol={{ span: 12 }}
      >
        <div style={{ display: 'flex', marginLeft: '55px', gap: '15px' }}>
          <Button
            style={{ border: 'none', boxShadow: 'none', borderRadius: '5px' }}
            disabled={decimalPlaces <= 0}
            onClick={() =>
              onChangeSettingsHandle({
                ...settingValues,
                decimalPlaces: decimalPlaces - 1,
              })
            }
            size="small"
            icon={<DecreaseDecimalIcon color="#005EB8" style={{ padding: '2px' }} />}
          />
          <Button
            style={{ border: 'none', boxShadow: 'none', borderRadius: '5px' }}
            onClick={() =>
              onChangeSettingsHandle({
                ...settingValues,
                decimalPlaces: decimalPlaces + 1,
              })
            }
            // disabled={decimalPlaces >= 100}
            size="small"
            icon={<IncreaseDecimalIcon color="#005EB8" style={{ padding: '2px' }} />}
          />
        </div>
      </Form.Item>

      <Form.Item
        label={
          <>
            <span style={{ marginRight: '6px' }}>{translate(translations._DISPLAY_GROUPING)}</span>
            <Tooltip
              placement="top"
              title={translate(translations._TOOLTIP__DISPLAY_DECIMAL) as string}
              style={{ marginLeft: 4, opacity: 0.4 }}
            >
              <ViewDetailsInformationIcon size={14} />
            </Tooltip>
          </>
        }
        colon={false}
        labelCol={{ span: 10 }}
        wrapperCol={{ span: 14 }}
      >
        <Select
          style={{ width: 100 }}
          value={grouping}
          onChange={value =>
            onChangeSettingsHandle({
              ...settingValues,
              grouping: value,
            })
          }
          options={listGroupingSeparatorOptions}
        />
      </Form.Item>

      <Form.Item
        label={
          <>
            <span style={{ marginRight: '6px' }}>{translate(translations._DISPLAY_DECIMAL)}</span>
            <Tooltip
              placement="top"
              title={translate(translations._TOOLTIP__DISPLAY_GROUPING) as string}
              style={{ marginLeft: 4, opacity: 0.4 }}
            >
              <ViewDetailsInformationIcon size={14} />
            </Tooltip>
          </>
        }
        labelCol={{ span: 10 }}
        wrapperCol={{ span: 14 }}
        colon={false}
      >
        <Select
          style={{ width: 100 }}
          value={decimal}
          onChange={value =>
            onChangeSettingsHandle({
              ...settingValues,
              decimal: value,
            })
          }
          options={listDecimalSeparatorOptions}
        />
      </Form.Item>
    </Form>
  );
};
