// Libraries
import React from 'react';

// Types
import { ThumbnailCardProps } from '../../molecules';
import { ModalProps } from '../../molecules/Modal';
import { TTemplateItem, TCategoryItem } from '../../template';

export type TThumbnail = { id: string | number; url: string };

export interface PreviewTemplateModalProps extends ModalProps {
  bannerProps?: BannerProps;
  thumbnailProps?: ThumbnailProps;
  similarTemplateProps?: SimilarTemplateProps;
  informationProps?: Omit<InformationProps, 'deviceType' | 'onDeviceTypeChange'>;
}

export interface BannerProps extends React.HTMLAttributes<HTMLDivElement> {
  deviceType?: InformationProps['deviceType'];
  showSkeleton?: boolean;

  /** Type of height includes: number, px, %, vh */
  height?: number | string;
  children?: React.ReactNode;
}

export type ThumbnailProps = Omit<
  ThumbnailCardProps,
  'id' | 'name' | 'thumbnail' | 'onClickWrapper'
> & {
  thumbnails?: TThumbnail[];
  onClickThumbnail?: (value: TThumbnail) => void;
};

export interface SimilarTemplateProps {
  show?: boolean;
  similarTemplates?: (TTemplateItem | undefined)[];
  similarCardProps?: Omit<ThumbnailCardProps, 'id' | 'name' | 'thumbnail'>;
}

export interface InformationProps {
  // Props
  deviceType: string;
  itemName?: string;
  categoryListing?: TCategoryItem[];
  buttonText?: string;
  description?: string;
  showDeviceRadios?: boolean;

  // Callbacks
  onDeviceTypeChange: (value: string) => void;
  onButtonClick?: React.MouseEventHandler<HTMLElement>;
}
