import LinkTo from '@storybook/addon-links/react';
import { Button, Tag, Table } from '@antscorp/antsomi-ui/es/index';
import {
  DataTypeTable,
  BannerPropsTable,
  InformationPropsTable,
  SimilarTemplatePropsTable,
  ThumbnailPropsTable,
  TThumbnailTable,
} from './components';

export default {
  title: 'Organisms/PreviewTemplateModal/DataType',
};

export const DataTypes = {
  render: () => <DataTypeTable />,
  name: 'DataTypes',
};

export const BannerProps = {
  render: () => <BannerPropsTable />,
  name: 'BannerProps',
};

export const InformationProps = {
  render: () => <InformationPropsTable />,
  name: 'InformationProps',
};

export const SimilarTemplateProps = {
  render: () => <SimilarTemplatePropsTable />,
  name: 'SimilarTemplateProps',
};

export const ThumbnailProps = {
  render: () => <ThumbnailPropsTable />,
  name: 'ThumbnailProps',
};

export const TThumbnail = {
  render: () => <TThumbnailTable />,
  name: 'TThumbnail',
};
