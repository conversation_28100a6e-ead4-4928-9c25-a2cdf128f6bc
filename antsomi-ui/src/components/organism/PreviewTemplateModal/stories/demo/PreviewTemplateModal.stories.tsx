// Libraries
import React, { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>bj, Meta, StoryFn } from '@storybook/react';

// Components
import { PreviewTemplateModal } from '../..';
import { Button } from '../../../../atoms';

// Constants
import {
  HTML,
  SIMILAR_CARD_HEIGHT_DEFAULT,
  SIMILAR_CARD_WIDTH_DEFAULT,
  THUMBNAIL_HEIGHT_DEFAULT,
  THUMBNAIL_SAMPLES,
  THUMBNAIL_URL,
  THUMBNAIL_WIDTH_DEFAULT,
} from '../../constants';
import { MENU_ITEMS_SAMPLE } from '../../../../template/TemplateListing/constants';

// Types
import { TThumbnail } from '../../types';
import { TCategoryItem, TTemplateItem } from '../../../../template';
import { TThumbnailCardId } from '../../../../molecules/ThumbnailCard';

// Styled
import { ImageWrapper } from '../../styled';

export default {
  title: 'Organisms/PreviewTemplateModal/Demo',
  component: PreviewTemplateModal,
  argTypes: {
    loading: {
      name: 'loading',
      defaultValue: false,
      description: 'Show modal loading indicator',
      table: {
        type: { summary: 'loading' },
        defaultValue: {
          summary: `false`,
        },
      },
      control: 'boolean',
    },
    bannerProps: {
      name: 'bannerProps',
      defaultValue: {
        showSkeleton: false,
      },
      description: 'Customize banner properties',
      table: {
        type: { summary: 'bannerProps' },
        defaultValue: {
          summary: `{
            showSkeleton: false,
        }`,
        },
      },
      control: 'object',
    },
    similarTemplateProps: {
      name: 'similarTemplateProps',
      defaultValue: {
        show: true,
        similarCardProps: {
          width: SIMILAR_CARD_WIDTH_DEFAULT,
          height: SIMILAR_CARD_HEIGHT_DEFAULT,
        },
      },
      description: 'Customize similar template properties',
      table: {
        type: { summary: 'similarTemplateProps' },
        defaultValue: {
          summary: `{
          show: true,
          similarCardProps: {
            width: ${SIMILAR_CARD_WIDTH_DEFAULT},
            height: ${SIMILAR_CARD_HEIGHT_DEFAULT},
          }
        }`,
        },
      },
      control: 'object',
    },
    thumbnailProps: {
      name: 'thumbnailProps',
      defaultValue: {
        show: true,
        width: THUMBNAIL_WIDTH_DEFAULT,
        height: THUMBNAIL_HEIGHT_DEFAULT,
      },
      description: 'Customize thumbnail properties',
      table: {
        type: { summary: 'similarTemplateProps' },
        defaultValue: {
          summary: `{
            show: true,
            width: ${THUMBNAIL_WIDTH_DEFAULT},
            height: ${THUMBNAIL_HEIGHT_DEFAULT},
          }`,
        },
      },
      control: 'object',
    },
    informationProps: {
      name: 'informationProps',
      defaultValue: {
        itemName: 'Template name',
        categoryListing: MENU_ITEMS_SAMPLE,
        buttonText: 'Use template',
        showDeviceRadios: true,
        description: 'Description show here',
        onButtonClick: () => {},
      },
      description: 'Customize information properties',
      table: {
        type: { summary: 'informationProps' },
        defaultValue: {
          summary: `{
            itemName: 'Template name',
            categoryListing: MENU_ITEMS_SAMPLE,
            buttonText: 'Use template',
            description: 'Description show here',
            showDeviceRadios: true,
            onButtonClick: () => {},
          }`,
        },
      },
      control: 'object',
    },
  },
  parameters: {
    docs: {
      description: {
        component: 'A Preview Template Modal. \n',
      },
    },
  },
} as Meta<typeof PreviewTemplateModal>;

const Template: StoryFn<typeof PreviewTemplateModal> = args => {
  const { thumbnailProps, similarTemplateProps, informationProps, bannerProps, ...rest } = args;
  // States
  const [open, setOpen] = useState<boolean>(false);
  const [image, setImage] = useState<string | undefined>(undefined);

  // Variables
  const thumbnails: TThumbnail[] = THUMBNAIL_SAMPLES;

  const similarTemplates: TTemplateItem[] = [];
  const templateQuantity = 5;
  for (let i = 1; i <= templateQuantity; i++) {
    similarTemplates.push({
      id: `template-${i}`,
      name: `template ${i}`,
      thumbnail: THUMBNAIL_URL,
    });
  }

  const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;

  // Side Effects
  useEffect(() => {
    setImage(thumbnails[0].url);
  }, [thumbnails]);

  // Handlers
  const handleClickThumbnail = (thumbnail: TThumbnail) => {
    setImage(thumbnail.url);
  };

  return (
    <>
      <Button onClick={() => setOpen(true)}>Preview</Button>
      <PreviewTemplateModal
        bannerProps={{ ...bannerProps, children: image }}
        thumbnailProps={{ thumbnails, onClickThumbnail: handleClickThumbnail, ...thumbnailProps }}
        similarTemplateProps={{ similarTemplates, ...similarTemplateProps }}
        informationProps={{ categoryListing: categoryItems, ...informationProps }}
        open={open}
        onOk={() => setOpen(false)}
        onCancel={() => setOpen(false)}
        {...rest}
      />
    </>
  );
};

export const Default = {
  render: Template,
  args: {},
};

export const BasicUsage: StoryObj<any> = {
  render: () => {
    // States
    const [open, setOpen] = useState<boolean>(false);

    // Variables
    const similarTemplates: TTemplateItem[] = [];
    const templateQuantity = 5;
    for (let i = 1; i <= templateQuantity; i++) {
      similarTemplates.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }

    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;

    return (
      <>
        <Button onClick={() => setOpen(true)}>Preview</Button>
        <PreviewTemplateModal
          bannerProps={{
            children: (
              <iframe title="Preview" srcDoc={HTML} style={{ width: '100%', height: '100%' }} />
            ),
          }}
          similarTemplateProps={{ similarTemplates: [] }}
          informationProps={{ categoryListing: categoryItems }}
          open={open}
          onOk={() => setOpen(false)}
          onCancel={() => setOpen(false)}
        />
      </>
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Simple Preview Template Modal with actions.',
      },
    },
  },
};

export const CustomizeBanner: StoryObj<any> = {
  render: () => {
    const [open, setOpen] = useState<boolean>(false);
    const [image, setImage] = useState<string | undefined>(undefined);

    const thumbnails: TThumbnail[] = THUMBNAIL_SAMPLES;

    const similarTemplates: TTemplateItem[] = [];
    const templateQuantity = 5;
    for (let i = 1; i <= templateQuantity; i++) {
      similarTemplates.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }

    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;

    useEffect(() => {
      setImage(thumbnails[0].url);
    }, [thumbnails]);

    const handleClickThumbnail = (thumbnail: TThumbnail) => {
      setImage(thumbnail.url);
    };

    return (
      <>
        <Button onClick={() => setOpen(true)}>Preview</Button>
        <PreviewTemplateModal
          bannerProps={{
            children: <ImageWrapper src={image} alt="" />,
            showSkeleton: true,
            height: '55vh',
          }}
          thumbnailProps={{ thumbnails, onClickThumbnail: handleClickThumbnail }}
          similarTemplateProps={{ similarTemplates }}
          informationProps={{ categoryListing: categoryItems }}
          open={open}
          onOk={() => setOpen(false)}
          onCancel={() => setOpen(false)}
        />
      </>
    );
  },

  parameters: {
    docs: {
      description: {
        story: "Customize Preview Template Modal's banner with actions.",
      },
    },
  },
};

export const CustomizeThumbnails: StoryObj<any> = {
  render: () => {
    // States
    const [open, setOpen] = useState<boolean>(false);
    const [image, setImage] = useState<string | undefined>(undefined);

    // Variables
    const thumbnails: TThumbnail[] = useMemo(() => THUMBNAIL_SAMPLES.slice(0, 3), []);

    const similarTemplates: TTemplateItem[] = [];
    const templateQuantity = 5;
    for (let i = 1; i <= templateQuantity; i++) {
      similarTemplates.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }
    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;

    // Side Effects
    useEffect(() => {
      setImage(thumbnails[0].url);
    }, [thumbnails]);

    // Handlers
    const handleClickThumbnail = (thumbnail: TThumbnail) => {
      setImage(thumbnail.url);
    };

    return (
      <>
        <Button onClick={() => setOpen(true)}>Preview</Button>
        <PreviewTemplateModal
          bannerProps={{ children: <ImageWrapper src={image} alt="" /> }}
          thumbnailProps={{
            thumbnails,
            width: 250,
            height: 250,
            onClickThumbnail: handleClickThumbnail,
          }}
          informationProps={{ categoryListing: categoryItems }}
          similarTemplateProps={{
            similarTemplates,
          }}
          open={open}
          onOk={() => setOpen(false)}
          onCancel={() => setOpen(false)}
        />
      </>
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Customize Thumbnails.',
      },
    },
  },
};

export const WithoutThumbnails: StoryObj<any> = {
  render: () => {
    // States
    const [open, setOpen] = useState<boolean>(false);

    // Variables
    const similarTemplates: TTemplateItem[] = [];
    const templateQuantity = 5;
    for (let i = 1; i <= templateQuantity; i++) {
      similarTemplates.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }
    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;

    return (
      <>
        <Button onClick={() => setOpen(true)}>Preview</Button>
        <PreviewTemplateModal
          bannerProps={{
            children: 'https://sandbox-st-media-template.antsomi.com/base64-ImageWrapper/20110.png',
          }}
          informationProps={{ categoryListing: categoryItems }}
          similarTemplateProps={{
            similarTemplates,
          }}
          open={open}
          onOk={() => setOpen(false)}
          onCancel={() => setOpen(false)}
        />
      </>
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Preview Template Modal without Thumbnails.',
      },
    },
  },
};

export const CustomizeSimilarTemplates: StoryObj<any> = {
  render: () => {
    // States
    const [open, setOpen] = useState<boolean>(false);
    const [image, setImage] = useState<string | undefined>(undefined);

    // Variables
    const thumbnails: TThumbnail[] = THUMBNAIL_SAMPLES;

    const similarTemplates: TTemplateItem[] = [];
    const templateQuantity = 5;
    for (let i = 1; i <= templateQuantity; i++) {
      similarTemplates.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }
    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;

    // Side Effects
    useEffect(() => {
      setImage(thumbnails[0].url);
    }, [thumbnails]);

    // Handlers
    const handleClickThumbnail = (thumbnail: TThumbnail) => {
      setImage(thumbnail.url);
    };

    const handleClickSimilarCard = (id: TThumbnailCardId) => {
      console.log('Click similar card: ', id);
    };

    return (
      <>
        <Button onClick={() => setOpen(true)}>Preview</Button>
        <PreviewTemplateModal
          bannerProps={{ children: <ImageWrapper src={image} alt="" /> }}
          thumbnailProps={{ thumbnails, onClickThumbnail: handleClickThumbnail }}
          informationProps={{ categoryListing: categoryItems }}
          similarTemplateProps={{
            similarTemplates,
            similarCardProps: {
              height: 410,
              onClickWrapper: handleClickSimilarCard,
            },
          }}
          open={open}
          onOk={() => setOpen(false)}
          onCancel={() => setOpen(false)}
        />
      </>
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Customize Similar Templates.',
      },
    },
  },
};

export const WithoutSimilarTemplates: StoryObj<any> = {
  render: () => {
    // States
    const [open, setOpen] = useState<boolean>(false);
    const [image, setImage] = useState<string | undefined>(undefined);

    // Variables
    const thumbnails: TThumbnail[] = THUMBNAIL_SAMPLES;
    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;

    // Side Effects
    useEffect(() => {
      setImage(thumbnails[0].url);
    }, [thumbnails]);

    // Handlers
    const handleClickThumbnail = (thumbnail: TThumbnail) => {
      setImage(thumbnail.url);
    };

    return (
      <>
        <Button onClick={() => setOpen(true)}>Preview</Button>
        <PreviewTemplateModal
          bannerProps={{ children: <ImageWrapper src={image} alt="" /> }}
          thumbnailProps={{ thumbnails, onClickThumbnail: handleClickThumbnail }}
          informationProps={{ categoryListing: categoryItems }}
          similarTemplateProps={{
            show: false,
          }}
          open={open}
          onOk={() => setOpen(false)}
          onCancel={() => setOpen(false)}
        />
      </>
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Preview Template Modal without Similar Templates.',
      },
    },
  },
};

export const CustomizeInfo: StoryObj<any> = {
  render: () => {
    // States
    const [open, setOpen] = useState<boolean>(false);
    const [image, setImage] = useState<string | undefined>(undefined);

    // Variables
    const thumbnails: TThumbnail[] = THUMBNAIL_SAMPLES;

    const similarTemplates: TTemplateItem[] = [];
    const templateQuantity = 10;
    for (let i = 1; i <= templateQuantity; i++) {
      similarTemplates.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }
    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;

    // Side Effects
    useEffect(() => {
      setImage(thumbnails[0].url);
    }, [thumbnails]);

    // Handlers
    const handleClickThumbnail = (thumbnail: TThumbnail) => {
      setImage(thumbnail.url);
    };

    const handleButtonClick: React.MouseEventHandler<HTMLElement> = e => {
      console.log(e);
    };

    return (
      <>
        <Button onClick={() => setOpen(true)}>Preview</Button>
        <PreviewTemplateModal
          bannerProps={{ children: <ImageWrapper src={image} alt="" /> }}
          thumbnailProps={{ thumbnails, onClickThumbnail: handleClickThumbnail }}
          informationProps={{
            categoryListing: categoryItems,
            itemName: 'Template name',
            description: 'Template description',
            buttonText: 'Use template',
            showDeviceRadios: false,
            onButtonClick: handleButtonClick,
          }}
          similarTemplateProps={{
            similarTemplates,
          }}
          open={open}
          onOk={() => setOpen(false)}
          onCancel={() => setOpen(false)}
        />
      </>
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Customize Preview Modal Info.',
      },
    },
  },
};

export const ModalLoading: StoryObj<any> = {
  render: () => {
    // States
    const [open, setOpen] = useState<boolean>(false);
    const [image, setImage] = useState<string | undefined>(undefined);

    // Variables
    const thumbnails: TThumbnail[] = THUMBNAIL_SAMPLES;

    const similarTemplates: TTemplateItem[] = [];
    const templateQuantity = 5;
    for (let i = 1; i <= templateQuantity; i++) {
      similarTemplates.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }

    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;

    // Side Effects
    useEffect(() => {
      setImage(thumbnails[0].url);
    }, [thumbnails]);

    return (
      <>
        <Button onClick={() => setOpen(true)}>Preview</Button>
        <PreviewTemplateModal
          loading
          bannerProps={{ children: <ImageWrapper src={image} alt="" /> }}
          thumbnailProps={{ thumbnails }}
          similarTemplateProps={{ similarTemplates }}
          informationProps={{ categoryListing: categoryItems }}
          open={open}
          onOk={() => setOpen(false)}
          onCancel={() => setOpen(false)}
        />
      </>
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Show Preview Modal with Loading Indicator.',
      },
    },
  },
};
