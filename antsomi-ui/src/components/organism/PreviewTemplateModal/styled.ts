import styled from 'styled-components';
import { Modal } from '../../molecules';

export const ModalContainer = styled(Modal)`
  &.antsomi-modal {
    width: fit-content !important;

    .antsomi-modal-content {
      width: 1400px;
      height: auto;
    }
  }

  .overview {
    flex: 1 1 0%;
  }
`;

export const ModalWrapper = styled.div`
  padding: 20px;
  width: fit-content;
  box-sizing: border-box;
`;

export const ImageWrapper = styled.img`
  width: 100%;
  height: 100%;
  object-fit: contain;
`;
