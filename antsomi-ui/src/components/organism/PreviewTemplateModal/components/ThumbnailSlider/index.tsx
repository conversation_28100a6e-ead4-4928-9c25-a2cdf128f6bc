// Libraries
import React, { memo, useMemo, useRef, useState } from 'react';
import Icon from '@antscorp/icons';
import { Swiper } from 'swiper/react';
import 'swiper/swiper-bundle.css';
import { Navigation, A11y, Thumbs } from 'swiper/modules';
import { isEmpty } from 'lodash';

// Styled
import { SwiperSlideWrapper, ThumbnailSliderWrapper } from './styled';

// Types
import { TThumbnail, ThumbnailProps } from '../../types';

// Components
import { Button, Flex } from '../../../../atoms';
import { ThumbnailCard } from '../../../../molecules';

// Constants
import { THUMBNAIL_HEIGHT_DEFAULT, THUMBNAIL_WIDTH_DEFAULT } from '../../constants';
import classNames from 'classnames';

/** Max width of list thumbnail's wrapper, it is used to show swiper if the width is greater than 990 */
const WRAPPER_WIDTH = 990;
const GAP_X = 10;
const ICON_SIZE = 24;

export interface ThumbnailSliderProps extends ThumbnailProps {}

export const ThumbnailSlider: React.FC<ThumbnailSliderProps> = memo(props => {
  const {
    thumbnails,
    width = THUMBNAIL_WIDTH_DEFAULT,
    height = THUMBNAIL_HEIGHT_DEFAULT,
    onClickThumbnail,
    ...restOfThumbnailProps
  } = props;

  //  States
  const [isSlideBeginning, setSlideBeginning] = useState<boolean>(true);
  const [isSlideEnd, setSlideEnd] = useState<boolean>(false);

  // Refs
  const navigationPrevRef = useRef(null);
  const navigationNextRef = useRef(null);

  // Variables
  /** The width of per thumbnail wrapper including thumbnail card's size, icon's size and gap */
  const thumbnailWrapperWidth = useMemo(() => width + GAP_X * 2 + ICON_SIZE, [width]);

  // Memo
  const isShowSwiper = useMemo(() => {
    const calculate = (thumbnails?.length || 0) * thumbnailWrapperWidth;
    return calculate > WRAPPER_WIDTH;
  }, [thumbnails?.length, thumbnailWrapperWidth]);

  // const thumbnailCacheValue = useMemo(() => new Date().getTime(), []);

  // Renders
  const renderThumbnail = (args: { thumbnail: TThumbnail; index: number }) => {
    const { thumbnail, index } = args;
    const { id, url } = thumbnail;
    return (
      <Flex gap={GAP_X}>
        {!!index && (
          <Flex align="center">
            <Icon
              type="icon-ants-expand-more"
              style={{ fontSize: ICON_SIZE, transform: 'rotate(270deg)' }}
            />
          </Flex>
        )}
        <ThumbnailCard
          key={id}
          id={id}
          width={width}
          height={height}
          thumbnail={url}
          // thumbnailCacheValue={thumbnailCacheValue}
          style={{ flexShrink: 0 }}
          className="thumbnail-animation"
          actionAvailable={false}
          onClickWrapper={() => onClickThumbnail?.(thumbnail)}
          {...restOfThumbnailProps}
        />
      </Flex>
    );
  };

  return thumbnails && !isEmpty(thumbnails) ? (
    <ThumbnailSliderWrapper>
      {isShowSwiper ? (
        <>
          <Swiper
            slidesPerView={Math.floor(WRAPPER_WIDTH / thumbnailWrapperWidth)}
            spaceBetween={GAP_X}
            modules={[Navigation, A11y, Thumbs]}
            navigation={{
              prevEl: navigationPrevRef.current,
              nextEl: navigationNextRef.current,
            }}
            onBeforeInit={swiper => {
              setTimeout(() => {
                // Override prevEl & nextEl now that refs are defined
                if (swiper.params.navigation && typeof swiper.params.navigation !== 'boolean') {
                  swiper.params.navigation.prevEl = navigationPrevRef.current;
                  swiper.params.navigation.nextEl = navigationNextRef.current;

                  // Re-init navigation
                  swiper.navigation.destroy();
                  swiper.navigation.init();
                  swiper.navigation.update();
                }
              });
            }}
            onSlideChange={({ isBeginning, isEnd }) => {
              setSlideBeginning(isBeginning);
              setSlideEnd(isEnd);
            }}
          >
            {thumbnails.map((thumbnail, index) => (
              <SwiperSlideWrapper key={`slide-${thumbnail.id}`}>
                {renderThumbnail({ thumbnail, index })}
              </SwiperSlideWrapper>
            ))}
          </Swiper>
          <Button
            ref={navigationPrevRef}
            className={classNames({ 'custom-button-prev': true, hidden: isSlideBeginning })}
          >
            <Icon type="icon-ants-expand-more" />
          </Button>
          <Button
            ref={navigationNextRef}
            className={classNames({ 'custom-button-next': true, hidden: isSlideEnd })}
          >
            <Icon type="icon-ants-expand-more" />
          </Button>
        </>
      ) : (
        <Flex gap={GAP_X} style={{ width: 'fit-content' }}>
          {thumbnails?.map((thumbnail, index) => (
            <React.Fragment key={thumbnail.id}>
              {renderThumbnail({ thumbnail, index })}
            </React.Fragment>
          ))}
        </Flex>
      )}
    </ThumbnailSliderWrapper>
  ) : null;
});
