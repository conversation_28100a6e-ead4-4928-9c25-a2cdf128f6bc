import styled from 'styled-components';
import { SwiperSlide } from 'swiper/react';

// Constants
import { THEME, globalToken } from '@antscorp/antsomi-ui/es/constants';

export const ThumbnailSliderWrapper = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  .swiper {
    max-width: 1030px;

    .swiper-slide {
      width: fit-content !important;
    }
  }

  .thumbnail-animation {
    box-shadow: none;
    transition: box-shadow 0.1s ease-in-out;
    border-radius: ${globalToken?.borderRadius}px;

    > div {
      border-radius: ${globalToken?.borderRadius}px;
    }

    &:hover {
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1), 0 0 32px rgba(0, 0, 0, 0.04);
    }
  }

  .custom-button-prev,
  .custom-button-next {
    z-index: 10;
    position: absolute;
    width: 24px;
    height: 24px;
    background-color: #ffffff;
    box-shadow: 0px 3px 3px 0px #002e591a;
    border-radius: 999px;
    display: flex;
    align-items: center;
    justify-content: center;

    /* Remove default style of Antd button */
    border-color: white !important;

    &.antsomi-btn-default:not(:disabled):hover {
      border-color: white !important;
      background-color: #ffffff !important;
    }
  }

  .custom-button-prev {
    left: 0px;
    transform: rotate(90deg);
  }

  .custom-button-next {
    right: 0px;
    transform: rotate(-90deg);
  }

  .hidden {
    display: none !important;
  }

  i {
    font-size: 20px;
    color: ${THEME?.token?.bw8};
  }
`;

export const SwiperSlideWrapper = styled(SwiperSlide)`
  list-style: none;
  display: flex;
  justify-content: center;
  align-items: center;
  width: fit-content;
`;
