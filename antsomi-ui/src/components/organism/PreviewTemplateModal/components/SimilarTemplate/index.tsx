// Libraries
import React, { memo, useMemo } from 'react';
import { isEmpty } from 'lodash';
// Components
import { ThumbnailCard } from '../../../../molecules';
import { Empty } from '../../../../template/TemplateListing/components/Empty';
import { Flex } from '../../../../atoms';

// Types
import { SimilarTemplateProps } from '../../types';

// Styled
import { SimilarTemplateWrapper, TemplateListWrapper } from './styled';

// Constants
import { SIMILAR_CARD_HEIGHT_DEFAULT, SIMILAR_CARD_WIDTH_DEFAULT } from '../../constants';

const MAX_QUANTITY_TEMPLATES = 4;

export const SimilarTemplate: React.FC<SimilarTemplateProps> = memo(props => {
  const { show = true, similarTemplates = [], similarCardProps } = props;
  const {
    width = SIMILAR_CARD_WIDTH_DEFAULT,
    height = SIMILAR_CARD_HEIGHT_DEFAULT,
    ...restOfSimilarCardProps
  } = similarCardProps || {};

  // Memo
  // const thumbnailCacheValue = useMemo(() => new Date().getTime(), []);

  return show && !isEmpty(similarTemplates) ? (
    <SimilarTemplateWrapper gap={15} vertical>
      <div className="title">Similar templates</div>

      <TemplateListWrapper templateQuantity={MAX_QUANTITY_TEMPLATES}>
        {similarTemplates.slice(0, MAX_QUANTITY_TEMPLATES)?.map((item, idx) => {
          const { id, name, thumbnail } = item || {};

          return (
            <ThumbnailCard
              key={id}
              id={id || idx}
              name={name}
              width={width}
              height={height}
              thumbnail={thumbnail}
              actionAvailable={false}
              className="thumbnail-card"
              // thumbnailCacheValue={thumbnailCacheValue}
              {...restOfSimilarCardProps}
            />
          );
        })}
      </TemplateListWrapper>
    </SimilarTemplateWrapper>
  ) : null;
});
