// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`<PositionSetting  /> > should match snapshot 1`] = `
.c2 {
  font-size: 20px;
  line-height: 1;
}

.c1 {
  color: #222222;
  font-size: 12px;
  font-family: Roboto;
  -webkit-transition-property: color,background-color,border-color,text-decoration-color,fill,stroke;
  transition-property: color,background-color,border-color,text-decoration-color,fill,stroke;
  -webkit-transition-timing-function: cubic-bezier(0.4,0,0.2,1);
  transition-timing-function: cubic-bezier(0.4,0,0.2,1);
  -webkit-transition-duration: 150ms;
  transition-duration: 150ms;
  font-size: 12px;
}

.c1.--disabled {
  color: #999999;
  pointer-events: none;
}

.c1.--secondary {
  color: #666666;
}

.c1.--error {
  color: #EF3340;
}

.c1.--warning {
  color: #faad14;
}

.c0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

<div
  class="c0"
>
  <div
    class="c1 --default"
    style="font-weight: bold; color: rgb(102, 102, 102);"
    title="Position"
  >
    Position
  </div>
  <div
    style="flex-shrink: 0;"
  >
    <button
      class="ant-btn css-dev-only-do-not-override-ht6oo7 ant-btn-text ant-btn-icon-only"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <i
          class="ants-icon-ants-edit-2-3123M c2"
          paths="0"
          role="icon"
          type="icon-ants-edit-2"
        />
      </span>
    </button>
  </div>
</div>
`;
