import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  WrapperAvatar,
  WrapperContentAvatar,
  WrapperLinkItemFiles,
  WrapperModalImg,
} from '../styled';
import { Icon, Image, Tooltip } from '@antscorp/antsomi-ui/es/components';
import { convertDateToTimestamp } from '../util';
import { generateAvatar } from '../../TicketEditorV2/util';
import { isEmpty } from 'lodash';

const IMAGE_EXTENDS = ['.png', '.jpg', '.jpeg'];

const MessageComponent = ({
  toUser,
  fromUser,
  followers,
  attachments,
  message,
  date,
  timeZone,
  submitterEmail,
}) => {
  const email = submitterEmail || fromUser?.email;
  const avatarImg = generateAvatar(email?.charAt(0)?.toUpperCase());
  const [isExpand, setIsExpand] = useState(false);
  const refMesage = useRef<HTMLDivElement>(null);
  const [modalImg, setModalImg] = useState({
    isOpen: false,
    src: '',
  });

  useEffect(() => {
    if (refMesage.current) {
      refMesage.current.querySelectorAll('img')?.forEach(img => {
        img.style.cursor = 'pointer';
        img.style.width = '100%';
        img.addEventListener('click', () => {
          setModalImg({
            ...modalImg,
            isOpen: true,
            src: img?.src,
          });
        });
      });

      refMesage.current.querySelectorAll('a')?.forEach(a => {
        const isOpenNewTab = (a.attributes as any)?.rel?.value?.includes('noopener noreferrer');
        if (isOpenNewTab) {
          a.style.color = '#005EB8';
          a.style.textDecoration = 'underline';
          a.style.cursor = 'pointer';
        }
      });
    }
  }, [refMesage.current]);

  const isImageUrl = url => IMAGE_EXTENDS.some(tail => url && url.toLowerCase().endsWith(tail));

  const [images, files] = useMemo(() => {
    const arrImgs: any = [];
    const arrFiles: any = [];
    attachments.forEach(file => {
      if (isImageUrl(file?.url)) {
        arrImgs.push(file);
      } else {
        arrFiles.push(file);
      }
    });

    return [arrImgs, arrFiles];
  }, [attachments]);

  const renderImage = file => <Image src={file?.url} />;

  return (
    <WrapperContentAvatar>
      <div className="comment-header">
        <WrapperAvatar>
          <img className="avatar" alt="avatar" src={avatarImg} />
          <div className="user-info">
            <span className="user-info__name">{email}</span>
            <div className="user-info__desc">
              <div className="user-info__to-user">
                <span>To: {toUser?.name}</span>
                {/* <span>&lt;{toUser?.email}&gt;</span> */}
                {!isExpand && (
                  <Icon
                    className="user-info__expand-icon"
                    type="icon-ants-caret-down1"
                    onClick={() => setIsExpand(true)}
                  />
                )}
              </div>
              {isExpand ? (
                <div className="user-info__followers">
                  <div className="user-info__followers__name">
                    <span className="user-info__to-user">
                      Follower(s):{` `}
                      {followers?.map(follower => follower?.name).join(', ')}
                      {/* &lt;{follower?.email}&gt;{' '} */}
                    </span>
                  </div>
                  <Icon
                    className="user-info__expand-icon open"
                    type="icon-ants-caret-down1"
                    onClick={() => setIsExpand(false)}
                  />
                </div>
              ) : null}
            </div>
          </div>
        </WrapperAvatar>
        <div className="comment-time">{convertDateToTimestamp(date, timeZone)}</div>
      </div>
      <div className="comment-content">
        <div dangerouslySetInnerHTML={{ __html: message }} ref={refMesage} />
        <WrapperLinkItemFiles className="no-border">
          {images.map(renderImage)}
          {files?.map(file => (
            <div className="file-item rounded" key={file?.url}>
              <div className="file-name-group">
                <Icon className="file-icon" type="icon-ants-attachment" />
                <Tooltip title={file?.file_name}>
                  <span className="file-name">{file?.file_name}</span>
                </Tooltip>
              </div>
              <a href={file?.url} download target="_blank">
                <Icon className="download-btn" type="icon-ants-file-download" />
              </a>
            </div>
          ))}
        </WrapperLinkItemFiles>
      </div>

      {!isEmpty(modalImg?.src) && (
        <Image
          src={''}
          preview={{
            src: modalImg?.src,
            visible: modalImg.isOpen && !isEmpty(modalImg.src),
            onVisibleChange: () => {
              setModalImg({ src: '', isOpen: false });
            },
          }}
        />
      )}
    </WrapperContentAvatar>
  );
};

export default MessageComponent;
