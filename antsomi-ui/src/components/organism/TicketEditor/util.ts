import axios from 'axios';
import { PORTAL_KEYS } from './constant';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);
dayjs.extend(timezone);

export function getObjectPropSafely(fn, defaultValue = '') {
  try {
    return fn();
  } catch (e) {
    return defaultValue;
  }
}

export function isNumeric(num) {
  return !isNaN(num) && num !== '';
}

export function getUserLocaleLanguage(config) {
  return safeParse(getWindowParam(PORTAL_KEYS.USER_LANGUAGE, config), 'en');
}

export function callApiWithAuth(
  endpoint,
  method = 'GET',
  body,
  domain = '',
  config: any = {},
  token = '',
  userId = '',
  isTicket = false,
  type = '',
  isAccount = false,
  isPermission = false,
  others = {},
) {
  const apiPid = getPortalId(config);
  const apiToken = token;
  // const _user_id = userId;
  let ownerId = userId;
  // get from other params
  if (isTicket) {
    const params: any = {
      _token: apiToken,
      _account_id: ownerId,
      _owner_id: ownerId,
      _user_id: userId,
    };
    // endpoint += `${apiToken}&_account_id=${_owner_id}&_user_id=${_user_id}`;
    if (type) {
      params.type = type;
      // endpoint += `&type=${type}`
    }
    return axios({
      method,
      url: `${domain}/${endpoint}`,
      params,
      data: body,
    });
    // .catch(err => serviceUnavailbale);
  }
  if (isAccount) {
    const params = {
      _token: apiToken,
      _account_id: ownerId,
      _user_id: userId,
      account_type: type,
    };

    return axios({
      method,
      url: `${domain}/${endpoint}`,
      params,
      data: body,
    });
    // .catch(err => serviceUnavailbale);
  }
  if (isPermission) {
    const params = {
      type,
      appCode: body.appCode,
      menuCode: body.menuCode,
      fullParent: body.fullParent,
      _token: apiToken,
      _user_id: body.ownerId || userId,
      _account_id: userId,
    };

    return axios({
      method,
      url: `${domain}/${endpoint}`,
      params,
      data: body,
    });
    // .catch(err => serviceUnavailbale);
  }
  if (Object.prototype.hasOwnProperty.call(others, '_owner_id')) {
    ownerId = safeParse((others as any)._owner_id, '');
  }

  const isJourney = window.location.href.includes('marketing-hub/journeys');
  if (ownerId === 'all' && !isJourney) {
    ownerId = userId;
  }

  const headers = {
    'Content-Type': config.contentType || 'application/json; charset=utf-8',
    token: apiToken,
  };

  if (urlHasQueryString(endpoint)) {
    endpoint += `&_token=${apiToken}&portalId=${apiPid}&languageCode=${getUserLocaleLanguage(
      config,
    )}&_user_id=${userId}&_account_id=${userId}`;
  } else {
    endpoint += `?_token=${apiToken}&portalId=${apiPid}&languageCode=${getUserLocaleLanguage(
      config,
    )}&_user_id=${userId}&_account_id=${userId}`;
  }

  if (isNumeric(ownerId)) {
    endpoint += `&_owner_id=${ownerId}`;
  }

  if (endpoint.includes('journey-histories')) {
    endpoint += `&token=${apiToken}`;
  }
  return axios({
    method,
    url: `${domain}/${endpoint}`,
    data: body,
    headers,
  });
  // .catch(
  //   err =>
  //     serviceUnavailbale,
  // );
}

export function isUrlEmbedded() {
  return false;
}

export const safeParse = (val, defaultVal) => {
  if (
    typeof val === 'undefined' ||
    val === 'undefined' ||
    val === null ||
    val.length === 0 ||
    val === 'NaN'
  ) {
    return defaultVal;
  }
  return val;
};

export const safeParseInt = (val, defaultVal) => {
  let tempt = safeParse(val, 0);
  if (tempt !== 0) {
    tempt = parseInt(tempt);
    if (Number.isNaN(tempt)) {
      return defaultVal;
    }
  }
  return tempt;
};

export function getPortalId(config) {
  const portalId = safeParse(getSessionStorage('api_pid', config), 0);
  if (portalId === 0) {
    return getAppParamURL(window.location.pathname, false, config).portalId;
  }
  return portalId;
  // return safeParse(getAppSession('api_pid'), 0);
}

export function getWindowParam(name, config) {
  return config[name];
}

export function getSessionStorage(key, config = {}) {
  if (isUrlEmbedded()) {
    return getDataWithEmbeddedPublic(key, config);
  }
  const tempt = getWindowParam(key, config);
  if (tempt !== undefined) {
    return tempt;
  }
  if (typeof Storage !== 'undefined') {
    return sessionStorage.getItem(key);
  }
  return '';
}

export function getAppParamURL(pathname, forceVersionV2 = false, config) {
  const output = {
    portalId: 0,
    accessUserId: 0,
  };
  const isV3 = pathname.indexOf('marketing-hub') > -1;

  let portalId = 0;
  let accessUserId = 0;
  if (forceVersionV2 || !isV3) {
    try {
      // const arr = pathname.split('/');
      const arr = pathname.split('v2');

      if (arr.length >= 2) {
        const arrTmp = arr[1].split('/');
        // console.log('arrTmp', arrTmp);
        if (arrTmp.length >= 2) {
          // console.log('arr', arrTmp);
          portalId = safeParseInt(arrTmp[2], 0);
          if (portalId <= 0) {
            portalId = safeParseInt(arrTmp[1], 0);
          }
        }
      }
      if (portalId <= 0) {
        portalId = safeParseInt(getAppSession('api_pid', config), 0);
      }
    } catch (e) {
      // console.error(e);
    }
  }

  // V3 thêm param userid trên URL
  // http://localhost:5000/gen2/33167/marketing-hub/journeys/3/create
  // => http://localhost:5000/gen2/33167/1600082431/marketing-hub/journeys/3/create
  if (isV3) {
    try {
      // const arr = pathname.split('/');
      const arr = pathname.split('v2');

      if (arr.length >= 2) {
        const arrTmp = arr[1].split('/');
        if (arrTmp.length >= 2) {
          portalId = safeParseInt(arrTmp[1], 0);
          accessUserId = safeParseInt(arrTmp[2], 0);
          // if (portalId <= 0) {
          //   portalId = safeParseInt(arrTmp[1], 0);
          // }
        }
      }
      if (portalId <= 0) {
        portalId = safeParseInt(getAppSession('api_pid', config), 0);
      }
    } catch (e) {
      // console.error(e);
    }
  }

  output.portalId = portalId;
  output.accessUserId = accessUserId;
  return output;
}

export function urlHasQueryString(url) {
  const arr = url.split('?');
  if (arr.length > 1 && arr[1] !== '') {
    return true;
  }
  return false;
}

function getDataWithEmbeddedPublic(key, config: any = {}) {
  const data = safeParse(config.embeddedData, {});
  return data[key];
}

export function getCookie(name) {
  if (isUrlEmbedded()) {
    return getDataWithEmbeddedPublic(name);
  }

  const cname = `${name}=`;
  const decodedCookie = decodeURIComponent(document.cookie);
  const ca = decodedCookie.split(';');

  for (let i = 0; i < ca.length; i += 1) {
    let c = ca[i];

    while (c.charAt(0) === ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(cname) === 0) {
      return c.substring(cname.length, c.length);
    }
  }

  return '';
}

export function getAppSession(key, config) {
  if (isUrlEmbedded()) {
    return getDataWithEmbeddedPublic(key, config);
  }
  let tempt = safeParse(getSessionStorage(key.config), '');

  if (tempt === '') {
    // console.error('------CAN"T NOT GET APP SESSION');
    tempt = getCookie(key);
  }
  return window.decodeURIComponent(tempt);
}

export function getEntriesV2(res, def) {
  if (typeof res !== 'undefined') {
    const code = parseInt(safeParse(res.data.code, 500));
    const message = safeParse(res.data.message, '');
    // validateRedirect(code, message);
    let codeMessage = safeParse(res.data.codeMessage, 'INTERNAL_SERVER_ERROR');
    let entries = def;
    if (code === 200) {
      const data = safeParse(res.data.data, {});
      entries = safeParse(data.entries, []);
      codeMessage = 'SUCCESS';
    } else {
      const data = safeParse(res.data.data, {});
      entries = safeParse(data.entries, def);
    }
    return {
      code,
      codeMessage,
      // message: getMessageByCode(codeMessage),
      messageAPI: message,
      data: entries,
    };
  }
  return {
    code: 500,
    codeMessage: 'INTERNAL_SERVER_ERROR',
    // message: getMessageByCode('INTERNAL_SERVER_ERROR'),
    data: def,
  };
}

export const formatAccountId = data => {
  const results = data?.map(item => item?.userId);
  return results;
};

export const formatParams = data => {
  const params = {
    title: '',
    ownerId: undefined,
    followers: [],
    referenceUrl: '',
    priority: '',
    feature: '',
    ticketType: '',
    category: '',
    message: '',
    attachments: [],
    attachmentsZendesk: [],
    submitterEmail: '',
  };
  if (data?.ownerId) {
    params.ownerId = data?.ownerId[0]?.userId;
  }
  if (data?.title) {
    params.title = data?.title;
  }
  if (data?.followers?.length) {
    params.followers = formatAccountId(data?.followers);
  }
  if (data?.referenceUrl) {
    params.referenceUrl = data?.referenceUrl;
  }
  if (Object.keys(data?.priority).length) {
    params.priority = data?.priority?.value;
  }
  if (Object.keys(data?.feature).length) {
    params.feature = data?.feature?.value;
  }
  if (Object.keys(data?.ticketType).length) {
    params.ticketType = data?.ticketType?.value;
  }
  if (Object.keys(data?.category).length) {
    params.category = data?.category?.value;
  }
  if (data?.message?.length) {
    params.message = data?.message;
  }
  if (data?.files?.length) {
    params.attachments = data?.files;
  }

  if (data?.fileZendesk) {
    params.attachmentsZendesk = data?.fileZendesk?.map(file => file?.token);
  }

  if (data?.submitterEmail) {
    params.submitterEmail = data?.submitterEmail;
  }
  return params;
};

export const formatDatarender = (data, listSelect, listAccount) => {
  if (data?.ownerId) {
    data.ownerId = listAccount?.filter(acc => acc.userId === data?.ownerId);
  }
  if (data?.followers?.length) {
    const newFollowers: any[] = [];
    data.followers?.forEach(fol => {
      listAccount.forEach(acc => {
        if (fol === acc.userId) {
          newFollowers.push(acc);
        }
      });
    });
    data.followers = newFollowers;
  }
  if (data?.priority) {
    const newPriority = listSelect
      ?.filter(sel => sel.value === 'priority')[0]
      ?.field_options?.filter(opt => opt.value === data.priority);
    [data.priority] = newPriority;
  }
  if (data?.feature) {
    const newFeature = listSelect
      ?.filter(sel => sel.value === 'feature')[0]
      ?.field_options?.filter(opt => opt.value === data.feature);
    [data.feature] = newFeature;
  }
  if (data?.ticketType) {
    const newTicket = listSelect
      ?.filter(sel => sel.value === 'ticketType')[0]
      ?.field_options?.filter(opt => opt.value === data.ticketType);
    [data.ticketType] = newTicket;
  }
  if (data?.category) {
    const newCategory = listSelect
      ?.filter(sel => sel.value === 'category')[0]
      ?.field_options?.filter(opt => opt.value === data.category);
    [data.category] = newCategory;
  }
  // if(data?.files?.length) {
  //   params.files = data?.files
  // }
  return data;
};

export const handleValidateContent = stringHTML => {
  // if (stringHTML) {
  //   const elment = document.createElement('div');
  //   elment.innerHTML = stringHTML;
  //   return (
  //     (elment?.firstChild as Element)?.tagName === 'IMG' ||
  //     (elment?.firstChild as Element)?.innerHTML?.replace(/&nbsp;|<br>/g, '')?.trim()
  //   );
  // }
  if (stringHTML) {
    const element = document.createElement('div');
    element.innerHTML = stringHTML.trim();

    if ((element?.firstChild as Element)?.tagName === 'IMG') return true;

    return element.textContent?.trim() !== '';
  }
  return false;
};

export const compareArrays = (a, b) => JSON.stringify(a) === JSON.stringify(b);

export const convertDateToTimestamp = (time, timeZone) =>
  dayjs(time).tz(timeZone).format('MMM DD YYYY hh:mm');

export const postCustomEvent = (type, data) => {
  if (!type) {
    return;
  }
  try {
    document.dispatchEvent(
      new CustomEvent(type, {
        detail: data,
        bubbles: true,
        cancelable: true,
        composed: false,
        // data,
      }),
    );
  } catch (error) {}
};
