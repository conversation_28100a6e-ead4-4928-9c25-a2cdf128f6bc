/* eslint-disable @typescript-eslint/no-use-before-define */
// Libraries
import React, { UIEventHandler } from 'react';
import styled from 'styled-components';

// Atoms
import { Tooltip, Icon } from '@antscorp/antsomi-ui/es/components/atoms';

import { DataSelect } from '..';
import { TextField } from '../ColumnSuggestion';

// Constants
import { THEME } from '@antscorp/antsomi-ui/es/constants';

type TreeData = {
  value: string;
  title: string;
  key: string;
  isLeaf: boolean;
}[];
interface TreeContent {
  treeData: TreeData;
  data: DataSelect;
  onChange: (value: any) => void;
  onScroll: UIEventHandler<HTMLDivElement>;
}
export const TreeContentWrapper: React.FC<TreeContent> = props => {
  const { treeData, onChange, data, onScroll } = props;

  const showContent = childs => {
    const result: any[] = [];
    childs.forEach((item, key) => {
      result.push(
        <Tooltip title={item.title}>
          <ListItem>
            <TextField>{item.title}</TextField>
            <Icon
              disabled={data.includes(item.value)}
              size={10}
              type="icon-ants-double-arrow-up"
              onClick={data.includes(item.value) ? () => {} : e => onChange(item.value)}
              style={{
                color: THEME.token?.colorPrimary,
                cursor: 'pointer',
                transform: 'rotate(90deg)',
              }}
            />
          </ListItem>
        </Tooltip>,
      );
    });
    if (result.length > 0) {
      return <List>{result}</List>;
    }

    return null;
  };
  return <WrapperContent onScrollCapture={onScroll}>{showContent(treeData)}</WrapperContent>;
};
export const WrapperContent = styled.div`
  width: 100%;
  height: 100%;
`;
export const List = styled.ul`
  width: 100%;
  height: 90%;
  overflow: auto;
  margin-top: 10px;
`;
export const ListItem = styled.li`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px;
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
`;
export const Span = styled.div`
  color: inherit;
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;
