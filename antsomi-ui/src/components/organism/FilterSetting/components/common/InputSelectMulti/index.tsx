/* eslint-disable @typescript-eslint/no-use-before-define */
// Libraries
import React, { <PERSON>actNode, UIEventHandler, useEffect, useState } from 'react';
import { keyBy } from 'lodash';
import classNames from 'classnames';
import styled from 'styled-components';
import { TreeSelectProps as AntdTreeSelectProps, Popover, Input } from 'antd';
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';

// Atoms
import { Button, Divider, Icon, Text } from '@antscorp/antsomi-ui/es/components/atoms';

// Molecules
import { ModalSelect, StyledTreeSelect } from '../InputOrSelect';
import { StyledTag as Tag } from '@antscorp/antsomi-ui/es/components/molecules/SelectV2/styled';

// Components
import { TreeContentWrapper } from './Tree';
import { ColumnSuggestion } from './ColumnSuggestion';

// Services
import {
  getListInfoMetadata,
  getListSuggestionItems,
  getListSuggestionObjects,
} from '@antscorp/antsomi-ui/es/services/MediaTemplateDesign/BusinessObject';

// Translation
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';

// Hooks
import { useDebounce } from '@antscorp/antsomi-ui/es/hooks/useDebounceV2';

// Constants
import {
  DEFAULT_CUSTOMER_META_DATA,
  DEFAULT_EVENT_META_DATA,
  DEFAULT_VISITOR_META_DATA,
  SEGMENT_IDS,
  VALUE_TYPE,
} from '../../../constants';
import { THEME } from '@antscorp/antsomi-ui/es/constants';

// Types
import {
  CustomerMetadata,
  EventBoFieldMetadata,
  EventMetadata,
  VisitorMetadata,
} from '@antscorp/antsomi-ui/es/components/atoms/InputDynamic/types';

// Utils
import { checkDuplicateArray, getScopSuggest, setValueSelectAll } from '../../../utils';

const { EVENT, CUSTOMER, VISITOR } = VALUE_TYPE;
const { TextArea } = Input;

interface InputSelectMultiProps {
  value: string[];
  extend?: string[];
  itemTypeId: number | null;
  itemTypeName: string | null;
  column: string | null;
  customerMetadata: CustomerMetadata;
  visitorMetadata: VisitorMetadata;
  eventMetadata?: EventBoFieldMetadata | any;
  valueType?: string;
  errorMessage?: string;
  hideSelectField?: boolean;
  onChange: (val: InputSelectMultiProps['value'], key: any, key2?: any) => void;
  onChangeMultipleValue: ({
    value,
    event_metadata,
    visitor_metadata,
    customer_metadata,
    value_type,
  }: {
    value?: string[];
    event_metadata?: EventMetadata;
    visitor_metadata?: VisitorMetadata;
    customer_metadata?: CustomerMetadata;
    value_type?: string;
  }) => void;
}

export type TreeData = {
  value: string;
  title: string;
  key: string;
  isLeaf: boolean;
}[];
export type DataSelect = any;

export const InputSelectMulti: React.FC<InputSelectMultiProps> = props => {
  // I18n
  const { t } = i18nInstance;

  // Props
  const {
    value,
    itemTypeId,
    itemTypeName,
    column,
    customerMetadata = DEFAULT_CUSTOMER_META_DATA,
    visitorMetadata = DEFAULT_VISITOR_META_DATA,
    eventMetadata = {},
    valueType,
    hideSelectField,
    onChangeMultipleValue,
    onChange,
    errorMessage,
    extend,
  } = props;
  // State
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [treeData, setTreeData] = useState<TreeData>([]);
  const [page, setPage] = useState(1);
  const [searchValue, setSearchValue] = useState('');
  const [isShowCheck, setIsShowCheck] = useState(false);
  const [mapLabel, setMapLabel] = useState({});
  const [dataSelect, setDataSelect] = useState<DataSelect>([]);
  const [dataExtend, setDataExtend] = useState<DataSelect>([]);
  const [modalSelectVisible, setModalSelectVisible] = useState(false);
  const [openPopover, setPopover] = useState(false);
  const [isCheckDropDown, setIsCheck] = useState(false);
  // Debounce
  const debounceSearchValue = useDebounce(searchValue, 500);

  useEffect(() => {
    (async () => {
      setLoading(true);

      const res = await getSuggestions({ pageNum: page, search: debounceSearchValue });

      const newData = {
        ...res,
      };

      setData(newData);
      setTreeData(Object.values(newData));
      setLoading(false);
    })();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceSearchValue]);

  useEffect(() => {
    if (column === SEGMENT_IDS) {
      (async () => {
        const res = await getInfoMetadata();

        const newData = {
          ...res,
        };

        setMapLabel(newData);
      })();
    }
    setDataSelect(value);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  useEffect(() => {
    if (extend) {
      setDataExtend(extend);
    }
  }, [extend]);
  useEffect(() => {
    if (isCheckDropDown) {
      setTimeout(() => {
        setIsCheck(false);
      }, 1000);
    }
  }, [isCheckDropDown]);

  const getSuggestions: any = async ({ pageNum, search }) =>
    column === SEGMENT_IDS
      ? getSuggestionObjects({ pageNum, search })
      : getSuggestionItems({ pageNum, search });

  const getSuggestionItems = async ({ pageNum, search }) => {
    const { rows } = await getListSuggestionItems(
      {},
      {
        limit: 20,
        page: pageNum,
        sd: 'asc',
        search,
        decryptFields: [column],
        propertyCode: column,
        itemTypeId,
        itemTypeName,
        // 1 | 3
        scope: getScopSuggest({ itemTypeId }),
        itemPropertyName: column,
        systemDefined: 0,
        isPk: 0,
      },
    );

    const data = keyBy(
      rows.map((item: { id?: string | number; name: string }) => {
        const key = item.id || item.name;
        return {
          value: key,
          title: key,
          key,
          isLeaf: true,
        };
      }),
      'value',
    );

    return data;
  };

  const getSuggestionObjects = async ({ pageNum, search }) => {
    const { rows } = await getListSuggestionObjects(
      {},
      {
        limit: 20,
        page: pageNum,
        sd: 'asc',
        search,
        decryptFields: [column],
        propertyCode: column,
        itemTypeId,
        itemTypeName,
        isLookupLabelId: true,
        feServices: 'suggestionMultilang',
        feKey: `${itemTypeId}-${itemTypeName}-${column}`,
        objectType: 'BO_SEGMENTS',
        isPk: 1,
        objectName: column,
        filters: {
          OR: [
            {
              AND: [
                {
                  type: 1,
                  column: 'item_type_id',
                  data_type: 'number',
                  operator: 'equals',
                  value: itemTypeId,
                },
                {
                  type: 1,
                  column: 'status',
                  data_type: 'number',
                  operator: 'matches',
                  value: [1, 2],
                },
              ],
            },
          ],
        },
      },
    );

    const data = keyBy(
      rows.map((item: { id?: string | number; name: string; status: number }) =>
        // const key = item.id || item.name;
        ({
          value: item.id,
          title: item.name,
          key: item.id,
          status: +item.status,
          isLeaf: true,
        }),
      ),
      'value',
    );

    return data;
  };

  const getInfoMetadata = async () => {
    const { rows } = await getListInfoMetadata(
      {},
      {
        objectType: 'BO_SEGMENTS',
        filters: {
          OR: [
            {
              AND: [
                {
                  column: 'segment_id',
                  data_type: 'number',
                  operator: 'matches',
                  value,
                },
              ],
            },
          ],
        },
      },
    );

    const data = rows.reduce((acc, cur) => {
      acc[cur.id] = cur.name;
      return acc;
    }, {});

    return data;
  };

  const onPopover = () => {
    setIsCheck(true);
    setPopover(!openPopover);
  };
  const onApply = data => {
    setIsCheck(true);
    setPopover(!openPopover);
    onApplyExtendValue(data);
  };

  const onSelectAll = () => {
    const data = setValueSelectAll(treeData);
    const newData = checkDuplicateArray(data.concat(value));
    setDataSelect(newData);
    onChange(newData, 'value');
  };
  const onChangeValue = (val: string[]) => {
    const newData = [...dataSelect];
    const index = dataSelect.findIndex(each => each === val);
    if (index === -1) {
      newData.push(val);
    } else {
      newData.splice(index, 1);
    }
    setDataSelect(newData);
    onChange(newData, 'value');
    // onChange(val);
  };
  const onChangeRemoveExtend = val => {
    const newData = [...dataExtend];
    const index = dataExtend.findIndex(each => each === val);
    if (index === -1) {
      newData.push(val);
    } else {
      newData.splice(index, 1);
    }
    setDataExtend(newData);
    onChange(newData, 'extend');
  };
  const onChangeOpen = () => {
    setOpen(!open);
    setPopover(false);
  };
  const onChangeTreeData = (val: TreeData) => {
    setTreeData(val);
  };
  const onLoadMore = async () => {
    setLoadingMore(true);
    const newPage = page + 1;

    const res = await getSuggestions({ pageNum: newPage, search: searchValue });

    const newData = {
      ...data,
      ...res,
    };

    setData(newData);
    setTreeData(Object.values(newData));
    setLoadingMore(false);

    setPage(prev => prev + 1);
  };

  const onScroll: UIEventHandler<HTMLDivElement> = event => {
    if (isShowCheck) {
      return;
    }

    const target = event.target as Element;
    const bottom = target.scrollHeight - target.scrollTop < target.clientHeight + 70;
    if (bottom && !loadingMore) {
      onLoadMore();
    }
  };

  const onSearch = (val: string) => {
    setLoading(true);
    setPage(1);
    setSearchValue(val);
  };

  const onChangeIsShowCheck = () => {
    if (!isShowCheck) {
      const newData = props.value.map(
        name =>
          data[name] || {
            // hard value
            value: name,
            title: name,
            key: name,
            isLeaf: name,
          },
      );
      onChangeTreeData(newData);
    } else {
      onChangeTreeData(Object.values(data));
    }
    setIsShowCheck(prev => !prev);
  };

  const onUncheckAll = () => {
    setDataSelect([]);
    props.onChange?.([], 'value', 'extend');
  };

  const handleOkModalSelect = ({ valueType, metaData }) => {
    const mapMetaData = {
      event_metadata: metaData.event,
      visitor_metadata: metaData.visitor,
      customer_metadata: metaData.customer,
    };

    setModalSelectVisible(false);
    onChangeMultipleValue({
      value: [],
      ...mapMetaData,
      value_type: valueType,
    });
  };
  const onApplyExtendValue = value => {
    const newArray = (dataExtend || []).concat(value);
    setDataExtend(newArray);
    onChange(newArray, 'extend');
  };
  const handleCancelModalSelect = () => {
    setModalSelectVisible(false);
  };
  const renderInput = () => (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: 32,
        padding: '4px 12px 4px 4px',
        borderBottom: `1px solid ${THEME.token?.gray5}`,
      }}
    >
      <div style={{ width: '100%', cursor: 'pointer' }} onClick={() => setModalSelectVisible(true)}>
        <Tag>
          {(valueType === EVENT.value &&
            (eventMetadata.item_property_label || eventMetadata.item_property_name)) ||
            (valueType === CUSTOMER.value && customerMetadata.item_property_name) ||
            (valueType === VISITOR.value && visitorMetadata.item_property_name)}
        </Tag>
      </div>
      <Icon
        type="icon-ants-remove"
        style={{ fontSize: 10, color: THEME.token?.colorTextBase, cursor: 'pointer' }}
        onClick={() => {
          onChangeMultipleValue({
            event_metadata: DEFAULT_EVENT_META_DATA,
            value_type: 'normal',
          });
        }}
      />
    </div>
  );

  return (
    <>
      {['event', 'customer', 'visitor'].includes(valueType || '') ? (
        renderInput()
      ) : (
        <SelectMultiple
          // open
          virtual={false}
          treeData={treeData}
          value={dataExtend ? value.concat(dataExtend) : value}
          dropdownExtraContent={
            !hideSelectField ? (
              <>
                <Divider
                  style={{
                    border: '0.51px solid rgb(203, 214, 226)',
                    marginTop: 6,
                    marginBottom: 6,
                  }}
                />
                <div style={{ display: 'flex', alignItems: 'center', padding: '0px 10px' }}>
                  <Text>{t(translations.or.title).toString()}</Text>
                  <Button type="text" onClick={() => setModalSelectVisible(true)}>
                    {t(translations.selectAField.title).toString()}
                  </Button>
                </div>
              </>
            ) : null
          }
          searchValue={searchValue}
          notFoundContent={
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: 128,
              }}
            >
              {loading ? 'Loading...' : 'No data'}
            </div>
          }
          onSearch={onSearch}
          onChange={onChangeValue}
          onChangeTreeData={onChangeTreeData}
          // data={data}
          loading={loading}
          onScroll={onScroll}
          isShowCheck={isShowCheck}
          onChangeIsShowCheck={onChangeIsShowCheck}
          onUncheckAll={onUncheckAll}
          mapLabel={mapLabel}
          dataSelect={dataSelect}
          onChangeOpen={onChangeOpen}
          open={open}
          onSelectAll={onSelectAll}
          onApplyExtendValue={onApplyExtendValue}
          column={column}
          extendValue={dataExtend}
          onChangeRemoveExtend={onChangeRemoveExtend}
          onPopover={onPopover}
          onApply={onApply}
          openPopover={openPopover}
          isCheckDropDown={isCheckDropDown}
        />
      )}

      {errorMessage ? (
        <Text color="#ff4d4f" style={{ marginLeft: 8 }}>
          {errorMessage}
        </Text>
      ) : null}

      <ModalSelect
        valueType={valueType || 'event'}
        isModalVisible={modalSelectVisible}
        customerMetadata={customerMetadata}
        visitorMetadata={visitorMetadata}
        eventMetadata={eventMetadata}
        handleOk={handleOkModalSelect}
        handleCancel={handleCancelModalSelect}
      />
    </>
  );
};

interface SelectMultipleProps
  extends AntdTreeSelectProps,
    Omit<SelectMultipleDropdownProps, 'menu'> {
  loading?: boolean;
  onChangeTreeData: (treeData: any) => void;
  onChange: (val: InputSelectMultiProps['value']) => void;
  // data: {
  //   [key: string]: {
  //     value: string;
  //     title: string;
  //     key: string;
  //     isLeaf: boolean;
  //   };
  // };
  mapLabel: { [key: string | number]: string };
  dropdownExtraContent?: ReactNode;
  treeData: TreeData;
  dataSelect: DataSelect;
  onChangeOpen: () => void;
  open?: boolean;
  onSelectAll: () => void;
  column?: string | null;
  extendValue?: any;
  isCheckDropDown?: boolean;
  searchValue: any;
  onSearch: (val: any) => void;
}

export const SelectMultiple: React.FC<SelectMultipleProps> = props => {
  const {
    onChangeTreeData,
    // data,
    loading,
    onScroll,
    isShowCheck,
    onChangeIsShowCheck,
    onUncheckAll,
    mapLabel,
    dropdownExtraContent,
    treeData,
    onChange,
    dataSelect,
    onChangeOpen,
    open,
    onSelectAll,
    onApplyExtendValue,
    extendValue,
    column,
    onChangeRemoveExtend,
    onPopover,
    onApply,
    openPopover,
    isCheckDropDown,
    searchValue,
    onSearch,
    ...restOf
  } = props;

  // I18next
  const { t } = i18nInstance;
  return (
    <StyledTreeSelect
      showSearch={false}
      searchValue={searchValue}
      treeCheckable
      // open
      placement="bottomRight"
      autoClearSearchValue
      dropdownStyle={{ minWidth: '600px', width: '600px' }}
      open={open}
      onDropdownVisibleChange={isCheckDropDown ? () => {} : onChangeOpen}
      maxTagCount="responsive"
      tagRender={v => <Tag {...v}>{mapLabel[v.value] || v.label}</Tag>}
      placeholder={t(translations.selectAnItem.title).toString()}
      dropdownRender={menu => (
        <SelectMultipleDropdown
          menu={menu}
          isShowCheck={isShowCheck}
          extraContent={dropdownExtraContent}
          onChangeIsShowCheck={onChangeIsShowCheck}
          onUncheckAll={onUncheckAll}
          onScroll={onScroll}
          treeData={treeData}
          onChange={onChange}
          dataSelect={dataSelect}
          onChangeOpen={onChangeOpen}
          onSelectAll={onSelectAll}
          onApplyExtendValue={onApplyExtendValue}
          column={column}
          extendValue={extendValue}
          onChangeRemoveExtend={onChangeRemoveExtend}
          onPopover={onPopover}
          onApply={onApply}
          openPopover={openPopover}
          searchValue={searchValue}
          onSearch={onSearch}
          loading={loading}
        />
      )}
      {...restOf}
    />
  );
};

interface SelectMultipleDropdownProps {
  menu: ReactNode;
  isShowCheck: boolean;
  onChangeIsShowCheck: () => void;
  onUncheckAll: () => void;
  onScroll: UIEventHandler<HTMLDivElement>;
  extraContent?: ReactNode;
  treeData: TreeData;
  onChange: (val: InputSelectMultiProps['value']) => void;
  dataSelect: DataSelect;
  onChangeOpen: () => void;
  open?: boolean;
  onSelectAll: () => void;
  onApplyExtendValue: (value: any) => void;
  column?: string | null;
  extendValue?: any;
  onChangeRemoveExtend: (value: any) => void;
  onPopover: () => void;
  onApply: (data: any) => void;
  openPopover: boolean;
  searchValue: any;
  onSearch: (val: any) => void;
  loading?: boolean;
}

export const SelectMultipleDropdown: React.FC<SelectMultipleDropdownProps> = props => {
  const {
    menu,
    isShowCheck,
    onChangeIsShowCheck,
    onUncheckAll,
    onScroll,
    extraContent,
    treeData,
    onChange,
    dataSelect,
    onChangeOpen,
    onSelectAll,
    onApplyExtendValue,
    extendValue = [],
    column,
    onChangeRemoveExtend,
    onPopover,
    onApply,
    openPopover,
    searchValue,
    onSearch,
    loading,
  } = props;
  const [data, setData] = useState([]);
  const [dataTextArea, setDataArea] = useState('');
  const onChangeText = (value, evt) => {
    const result = value.split(/\n/).filter(element => element);
    setDataArea(value);
    setData(result);
  };
  const onChangeKey = evt => {
    const newArray: any = [...data];
    if (evt.keyCode === 13) {
      // eslint-disable-next-line array-callback-return
      newArray.map((each, index) => {
        if (newArray.length === index + 1) {
          newArray[index] = `${each}\n`;
        }
      });
      setDataArea(`${dataTextArea}\n`);
      setData(newArray);
    }
    // console.log(data);
  };
  const onCancelExtend = (e: any) => {
    setData([]);
    setDataArea('');
    onPopover();
  };
  const onCancel = () => {
    // onUncheckAll();
    onChangeOpen();
  };
  const applyExtend = e => {
    setDataArea('');
    onApply(data);
    setData([]);
  };
  const onSearchInput = e => {
    onSearch(e.target.value);
  };
  const content = (
    <div
      style={{
        width: '300px',
        height: '200',
      }}
    >
      <div style={{ marginBottom: '5px' }}>Extend values</div>
      <div>
        <TextArea
          // componentKey={props.componentKey}
          aria-label="minimum height"
          placeholder="You can extend values by input or paste values, separated by enter"
          onChange={evt => onChangeText(evt.target.value, evt)}
          onKeyDown={onChangeKey}
          style={{
            width: '98%',
            height: '130px',
            border: '1px #c8ccd7 solid',
            outline: 'none',
            resize: 'none',
          }}
          value={dataTextArea || ''}
        />
      </div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-around',
          width: '120px',
          paddingTop: '15px',
        }}
      >
        <Button onClick={applyExtend} type="primary">
          Apply
        </Button>
        <Button onClick={onCancelExtend} type="default">
          Cancel
        </Button>
      </div>
    </div>
  );

  return (
    <StyledDropdown>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <WrapperHeader>
          <Input
            style={{
              width: 290,
              margin: '2px 10px 2px 5px',
              border: '1px solid rgba(0, 0, 0, 0.42)',
              borderRadius: '5px',
            }}
            value={searchValue}
            onChange={onSearchInput}
            placeholder="Search values"
          />
          <Icon
            style={{
              top: '13px',
              left: '270px',
              position: 'absolute',
            }}
            type="icon-ants-search"
            size={16}
          />
        </WrapperHeader>
      </div>
      <Divider
        style={{
          border: '0.51px solid rgb(203, 214, 226)',
          margin: '5px 0px 0px 0px',
        }}
      />
      <ContainerCustomize>
        <WrapperContentCustomize>
          {loading ? (
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: 128,
              }}
            >
              <Text color={THEME.token?.colorIcon}>Loading...</Text>
            </div>
          ) : (
            <SuggestionTitle>
              <HeaderContent>
                <Text style={{ fontWeight: 'bold', marginLeft: '10px' }}>
                  {column}
                  <span style={{ fontWeight: 'normal' }}>{`(${treeData.length})`}</span>
                </Text>
                <Text
                  style={{
                    cursor: 'pointer',
                    fontWeight: 'bold',
                    paddingLeft: '10px',
                    paddingRight: '10px',
                  }}
                  color={THEME.token?.colorPrimary}
                  onClick={onSelectAll}
                >
                  Select All
                </Text>
              </HeaderContent>

              <TreeContentWrapper
                onScroll={onScroll}
                data={dataSelect}
                onChange={onChange}
                treeData={treeData}
              />
            </SuggestionTitle>
          )}
        </WrapperContentCustomize>
        <WrapperSuggestion>
          <SuggestionTitle>
            <HeaderContent>
              <Text style={{ fontWeight: 'bold', marginLeft: '10px' }}>
                Selected
                <span style={{ fontWeight: 'normal' }}>{`(${
                  dataSelect.length + extendValue.length
                })`}</span>
              </Text>
              <Text
                style={{
                  cursor: 'pointer',
                  fontWeight: 'bold',
                  paddingLeft: '10px',
                  paddingRight: '10px',
                }}
                color={THEME.token?.colorPrimary}
                onClick={onUncheckAll}
              >
                Remove All
              </Text>
            </HeaderContent>
            <ColumnSuggestion
              onChangeRemoveExtend={onChangeRemoveExtend}
              extendValue={extendValue}
              onChange={onChange}
              data={dataSelect}
            />
          </SuggestionTitle>
        </WrapperSuggestion>
      </ContainerCustomize>
      <Divider style={{ border: '0.51px solid rgb(203, 214, 226)', margin: '0px 0px 7px 0px' }} />
      <WrapperFooterContent>
        <div style={{ display: 'flex', justifyContent: 'space-around', width: '120px' }}>
          <Button onClick={onChangeOpen} type="primary">
            Apply
          </Button>
          <Button onClick={onCancel} type="default">
            Cancel
          </Button>
        </div>
        <Popover
          getPopupContainer={triggerNode => triggerNode.parentElement as HTMLElement}
          visible={openPopover}
          // onVisibleChange={onVisibleChange}
          overlayClassName={classNames('antsomi-w-[330px]', 'no-arrow', 'title-bg-blue')}
          placement="bottomRight"
          content={content}
          trigger={['click']}
        >
          <Button onClick={onPopover} type="default">
            <Icon size={10} type="icon-ants-flag" />
            Extend Value
          </Button>
        </Popover>
      </WrapperFooterContent>
      {extraContent}
    </StyledDropdown>
  );
};

export const StyledDropdown = styled.div`
  .antsomi-select-tree-list {
    .antsomi-select-tree-title {
      font-size: ${THEME.token?.fontSize}px;
    }
    .antsomi-select-tree-treenode {
      margin-right: 8px;
      margin-left: 8px;

      .antsomi-select-tree-switcher.antsomi-select-tree-switcher-noop {
        display: none;
      }
    }
  }
`;
export const WrapperHeader = styled.div`
  display: flex;
  height: 35px;
  justify-content: space-between;
  .MuiPaper-root.MuiPaper-rounded {
    flex: 1;
  }
`;
export const ContainerCustomize = styled.div`
  overflow: hidden;
  display: flex;
  max-height: 350px;
  min-height: 250px;
`;
export const WrapperContentCustomize = styled.div`
  /* max-width: 40.0625rem; */
  // flex-grow: 1;
  width: 50%;
  border-right: 1px solid rgb(230, 230, 230);
`;
export const WrapperContentSuggestion = styled.div`
  /* max-width: 40.0625rem; */
  // flex-grow: 1;
  width: 50%;
`;
export const WrapperSuggestion = styled.div`
  width: 50%;
`;
export const SuggestionTitle = styled.div`
  font-size: 1rem;
  width: 100%;
  height: 100%;
  margin-top: 5px;
`;
export const SpanSelect = styled.span`
  margin-left: 15px;
  font-size: 12px;
`;
export const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;
export const WrapperFooterContent = styled.div`
  height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
`;
