/* eslint-disable @typescript-eslint/no-use-before-define */
// Libraries
import React from 'react';
import styled from 'styled-components';

// Atoms
import { Tooltip, Icon, Text } from '@antscorp/antsomi-ui/es/components/atoms';

// Molecules
import { DataSelect } from '..';

interface ColumnContent {
  data: DataSelect;
  extendValue?: any;
  onChange: (value: any) => void;
  onChangeRemoveExtend: (value: any) => void;
}
export const ColumnSuggestion: React.FC<ColumnContent> = props => {
  const { data, onChange, extendValue, onChangeRemoveExtend } = props;

  const showContent = (childs: any, extend: any) => {
    const result: any[] = [];
    childs.forEach((item: any) => {
      result.push(
        <Tooltip title={item}>
          <ListItem>
            <TextField>{item}</TextField>
            <Icon
              onClick={() => onChange(item)}
              type="icon-ants-remove-slim"
              size={10}
              style={{ cursor: 'pointer' }}
            />
          </ListItem>
        </Tooltip>,
      );
    });
    extend?.forEach((element: any) => {
      result.push(
        <Tooltip title={element}>
          <ListItem>
            <TextField>{element}</TextField>
            <Icon size={10} type="icon-ants-flag" />
            <Icon
              onClick={() => onChangeRemoveExtend(element)}
              type="icon-ants-remove-slim"
              size={10}
              style={{ cursor: 'pointer' }}
            />
          </ListItem>
        </Tooltip>,
      );
    });
    if (result.length > 0) {
      return <List>{result}</List>;
    }
    return null;
  };
  return <WrapperContent>{showContent(data, extendValue)}</WrapperContent>;
};
export const WrapperContent = styled.div`
  width: 100%;
  height: 100%;
`;
export const List = styled.ul`
  width: 100%;
  height: 90%;
  overflow: auto;
  margin-top: 10px;
  padding: 5px;
`;
export const ListItem = styled.li`
  border: 0.063rem solid rgb(230, 230, 230);
  padding: 0.5rem;
  font-size: 0.875rem;
  width: 100%;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  border-radius: 0.313rem;
  justify-content: space-between;
  margin-top: 5px;
`;
export const Span = styled.div`
  color: inherit;
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;
export const TextField = styled(Text)`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 180px;
`;
