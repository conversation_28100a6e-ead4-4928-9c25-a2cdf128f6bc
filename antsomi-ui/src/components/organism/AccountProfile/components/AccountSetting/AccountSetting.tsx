// Libraries
import axios from 'axios';
import clsx from 'clsx';
import dayjs from 'dayjs';
import React, { forwardRef, memo, useEffect, useRef, useState } from 'react';

// Hooks
import { useDebounce } from '@antscorp/antsomi-ui/es/hooks/useDebounce';
import useModal from './useModal';

// Components
// import SearchDropdown from 'Components/SearchDropdown';
import {
  Button,
  EditableName,
  Flex,
  Icon,
  Input,
  ModalV2,
  Radio,
  Spin,
} from '@antscorp/antsomi-ui/es';
import EditableInput from './components/EditableInput';
import LanguageSelector from './components/LanguageSelector';
import UploadAvatar from './components/UploadAvatar';

// Constants
import {
  allowChangeFields,
  defaultVals,
  fieldsUI,
  FILE_UPLOAD_ERROR,
  initialForm,
  ST_URL_UPLOAD_PLATFORM,
  VALUE_MAPPING,
} from './constants';

// Assets
import Image from './images/img-account-big.png';
import {
  AccountDetail,
  DrawerHeader,
  StyledCheckbox,
  StyledDatePicker,
  StyledRadioGroup,
} from './styled';
import './styles.scss';

// Utils
import { IAM_API } from '@antscorp/antsomi-ui/es/constants';
import {
  getDefaultMessage,
  getDuplicateMessage,
  getObjectPropSafely,
  handleError,
  isPermissionUpdate,
  validator,
} from './utils';

import type { AccountSettingProps, TForm, TLogo } from './types';

export { type AccountSettingProps };

const PATH =
  'antsomi-ui/src/components/organism/AccountProfile/components/AccountSettings/index.tsx';

export const AccountSetting: React.FC<AccountSettingProps> = memo(
  forwardRef((props, ref) => {
    const {
      iamDomain = IAM_API,
      token,
      mode = 'update',
      accountId,
      open,
      setDrawerHeader,
      isDisabledEdit,
      isAccountProfile = true,
    } = props;
    const isUpdateMode = mode === 'update';
    const [form, setForm] = useState<TForm>(initialForm);
    const [originEmail, setOriginEmail] = useState('');
    const [isPreview, setIsPreview] = useState(isUpdateMode);
    const [tempAccountInfo, setTempAccountInfo] = useState({});
    const [isOverwrite, setIsOverwrite] = useState(false);
    const [errors, setErrors] = useState<Record<string, any>>({});
    const [loading, setLoading] = useState(false);
    const [isUploadLogo, setIsUploadLogo] = useState(false);
    const [logo, setLogo] = useState<TLogo>({ url: '' });
    const [logoManual, setLogoManual] = useState<TLogo>({ url: '' });
    const [openChangeEmail, setOpenChangeEmail] = useState<any>(null);
    const [apiEndpoint] = useState(
      `${iamDomain}/api/account/index/${accountId}?_token=${token}&_user_id=${accountId}&_account_id=${accountId}`,
    );

    const saveRef = useRef(false);
    const tempAccountRef = useRef({});
    const readyCheckRef = useRef(false);
    const confirmingRef = useRef(false);
    const passwordRef = useRef<any>(null);
    const updateDataRef = useRef<any>(null);
    const formRef = useRef<TForm>();

    formRef.current = form;

    const [debounceEmail] = useDebounce(form.email, 700);

    const { showSuccess, showError, showConfirm, contextHolder } = useModal();

    const [messageBox, setMessageBox] = useState({
      isOpen: false,
      icon: 'icon-success-green',
      title: 'Success',
      message: 'Success',
      id: '',
    });

    // useImperativeHandle(ref, () => ({
    //   onCloseDrawer() {
    // console.log('closed from Account Settings');
    //   },
    // }));

    // useEffect(() => {
    //   setIsPreview(false);
    //   if (!open) {
    //     setForm(initialForm);
    //     setOriginEmail(initialForm.email!);
    //   }
    //   if (open && !isUpdateMode && !form.full_name) {
    //     setForm(prev => ({
    //       ...prev,
    //       full_name: `Untitled Account#${dayjs().format('YYYY-MM-DD HH:mm:ss')}`,
    //     }));
    //   }
    // }, [open, isUpdateMode]);

    const getAccountDetail = async () => {
      if (!accountId) return null;

      const accountInfo = await axios
        .get(apiEndpoint)
        .then(res => getObjectPropSafely(() => res.data.data));

      return accountInfo;
    };

    const fetchAccountDetail = async () => {
      setLoading(true);
      const accountInfo = await getAccountDetail().finally(() => {
        setLoading(false);
      });

      if (accountInfo) {
        setForm({
          ...accountInfo,
          birthday: dayjs(accountInfo.birthday),
          initDone: true,
        });
        setOriginEmail(accountInfo.email!);

        setLogo({
          isDefault: true,
          url: accountInfo.avatar,
        });
      }
    };

    const setValueAndResetError = (fieldName, value, newForm: TForm | null = null) => {
      try {
        let isCharacter = true;

        if (fieldName === 'full_name') {
          isCharacter = value.indexOf("'") === -1;
        }

        if (newForm) {
          setForm(prev => ({
            ...prev,
            ...newForm,
            [fieldName]: value,
          }));
        } else {
          setForm(prev => ({
            ...prev,
            [fieldName]: value,
          }));
        }

        if (!isCharacter) {
          setErrors({
            ...errors,
            [fieldName]: ['Invalid full name'],
          });
        } else {
          setErrors({
            ...errors,
            [fieldName]: [],
          });
        }
      } catch (e) {
        handleError(e, {
          path: PATH,
          name: 'setValueAndResetError',
        });
      }
    };

    useEffect(() => {
      if (isUpdateMode && accountId) {
        fetchAccountDetail();
      }
    }, [accountId]);

    // useEffect(() => {
    //   if (
    //     readyCheckRef.current &&
    //     debounceEmail &&
    //     debounceEmail.trim() &&
    //     validateEmail(debounceEmail)
    //   ) {
    //     checkUniqueField({
    //       fieldName: 'email',
    //       config: fieldsUI.email.checkUnique,
    //     })({ target: { value: debounceEmail } });
    //   }
    // }, [debounceEmail]);

    // useEffect(() => {
    //   if (saveRef.current || saveAccountFlag) {
    //     validateForm();
    //     saveRef.current = true;
    //   }
    // }, [saveAccountFlag]);

    useEffect(() => {
      setDrawerHeader(
        <DrawerHeader>
          <div className="left-content">
            {/* <div id="header-journey-left" /> */}
            <div id="header-journey-name">
              {form.full_name && (
                <EditableName
                  defaultValue={form.full_name}
                  error=""
                  onBlur={() => {}}
                  onChange={value => onTextFieldChange('full_name')({ target: { value } })}
                  readonly={isPreview}
                  required
                />
              )}
            </div>
          </div>
          <div id="header-journey-right" className="right-content">
            {isPreview ? (
              <Button type="primary" onClick={() => setIsPreview(false)} disabled={isDisabledEdit}>
                Edit
              </Button>
            ) : (
              <Button
                type="primary"
                onClick={validateForm}
                disabled={isUpdateMode && isDisabledEdit}
              >
                Save
              </Button>
            )}
          </div>
        </DrawerHeader>,
      );
    }, [form.full_name, isPreview]);

    const onDateFieldChange = fieldName => date => {
      try {
        // const value = dayjs(date).format('YYYY-MM-DD');
        setValueAndResetError(fieldName, date);
      } catch (error) {
        setErrors(prev => ({
          ...prev,
          [fieldName]: dayjs(),
        }));
      }
    };

    const onTextFieldChange = fieldName => e => {
      const { value } = e.target;
      let isDone = false;
      if (fieldName === 'email') {
        readyCheckRef.current = true;
        if (isOverwrite) {
          setIsOverwrite(false);
          setValueAndResetError(fieldName, value, initialForm);
          isDone = true;
        }
      }

      if (fieldName === 'full_name' && mode === 'create') {
        if (!isUploadLogo) {
          buildAvatar(value);
        }
      }

      if (!isDone) {
        setValueAndResetError(fieldName, value);
      }
    };

    const onNumberFieldChange = fieldName => event => {
      try {
        const number = event.target.value.replace(/[,. ]/g, '').trim();

        if (+number >= 0) {
          setValueAndResetError(fieldName, number);
        }
      } catch (e) {
        handleError(e, {
          path: PATH,
          name: 'onNumberFieldChange',
        });
      }
    };

    // const onSelectFieldChange = (fieldName) => (newState) => {
    //     try {
    //         setValueAndResetError(fieldName, {
    //             id: newState.object_id,
    //             label: newState.object_name
    //         });
    //     } catch (e) {
    //         handleError(e, {
    //             path: PATH,
    //             name: 'onSelectFieldChange'
    //         });
    //     }
    // };

    const onRadioFieldChange = fieldName => e => {
      setForm(prev => ({
        ...prev,
        [fieldName]: +e.target.value,
      }));
    };

    const onSelectFieldChange = fieldName => (value, item) => {
      setForm(prev => ({
        ...prev,
        [fieldName]: value,
      }));
    };

    const onChangeValidateEmail = () => {
      setForm(prev => ({
        ...prev,
        validateEmail: !form.validateEmail,
      }));
    };

    const buildAvatar = fullName => {
      function generateAvatar(text, foregroundColor = '#fff', backgroundColor = '#005EB8') {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');

        canvas.width = 150;
        canvas.height = 150;

        // Draw background
        if (context) {
          context.fillStyle = backgroundColor;
          context.fillRect(0, 0, canvas.width, canvas.height);

          // Draw text
          context.font = "70px 'sans serif'";
          context.fillStyle = foregroundColor;
          context.textAlign = 'center';
          context.textBaseline = 'middle';
          // context.textDrawingMode = 'glyph';
          // context.patternQuality = 'nearest';
          context.fillText(text, canvas.width / 2, canvas.height / 2, canvas.height / 2);

          return canvas.toDataURL('image/png');
        }

        return '';
      }

      if (fullName) {
        const url = generateAvatar(fullName.charAt(0).toUpperCase());

        setLogoManual({
          isDefault: false,
          url,
        });
      } else {
        setLogoManual({
          isDefault: false,
          url: '',
        });
      }
    };

    const onConfirmMessageBox = () => {
      try {
        const newForm: TForm = {};

        Object.keys(form).forEach(field => {
          newForm[field] =
            tempAccountRef.current[field] || defaultVals[field]
              ? tempAccountRef.current[field] || defaultVals[field]
              : '';
        });
        newForm.password = '********';
        setForm(prev => ({
          ...prev,
          ...newForm,
        }));
        setErrors({});
        setIsOverwrite(true);

        setMessageBox({
          ...messageBox,
          isOpen: false,
        });
      } catch (err) {
        handleError(err, {
          path: PATH,
          name: 'closeMessageBox',
        });
      }
    };

    const onChange = {
      number: onNumberFieldChange,
      textarea: onTextFieldChange,
      text: onTextFieldChange,
      radio: onRadioFieldChange,
      select: onSelectFieldChange,
      date: onDateFieldChange,
    };

    const checkUniqueField =
      ({ fieldName, config }) =>
      event => {
        try {
          const fieldValue = event.target.value.trim();

          if (fieldValue) {
            const checkRequest = config.service({
              ...config.params,
              [config.mapping]: fieldValue,
              user_id: accountId,
            });

            if (checkRequest) {
              checkRequest.then(res => {
                if (
                  mode === 'create' &&
                  res &&
                  res.data &&
                  res.data.code === 105 &&
                  res.data.data &&
                  res.data.data.user
                ) {
                  setTempAccountInfo(res.data.data.user);
                  tempAccountRef.current = res.data.data.user;
                  showConfirm({
                    title: "This account's profile has already existed.",
                    content: 'Do you want to add this account to this portal?',
                    onOk: onConfirmMessageBox,
                  });
                } else {
                  setErrors({
                    ...errors,
                    [fieldName]:
                      res && res.data && res.data.data && res.data.data.status
                        ? []
                        : [getDuplicateMessage(fieldName)],
                  });
                }
              });
            }
          }
        } catch (e) {
          handleError(e, {
            path: PATH,
            name: 'setValueAndResetError',
          });
        }
      };

    const validateUniqueField = async ({ fieldName, config }) => {
      try {
        const fieldValue = form[fieldName].trim();

        if (fieldValue) {
          const res = await config.service({
            ...config.params,
            [config.mapping]: fieldValue,
            user_id: isOverwrite ? form.user_id : accountId,
          });

          return res && res.data && res.data.data && res.data.data.status
            ? null
            : getDuplicateMessage(fieldName);
        }

        return null;
      } catch (e) {
        handleError(e, {
          path: PATH,
          name: 'setValueAndResetError',
        });
      }
    };

    const renderField = (fieldType, inputProps) => {
      try {
        if (
          isUpdateMode &&
          (isPreview || !inputProps.isUpdate || isDisabledEdit) &&
          !['lang', 'password'].includes(fieldType)
        ) {
          if (fieldType === 'date') {
            return inputProps.value ? dayjs(inputProps.value).format('DD-MM-YYYY') : '';
          }
          return VALUE_MAPPING[inputProps.fieldName]
            ? VALUE_MAPPING[inputProps.fieldName][inputProps.value]
            : inputProps.value;
        }
        const errorMsg = getObjectPropSafely(() => errors[inputProps.fieldName][0]);

        switch (fieldType) {
          case 'text':
            return (
              <Input
                {...inputProps}
                showCurrentLenght={false}
                status={errorMsg ? 'error' : undefined}
                errorMsg={errorMsg}
                style={{ padding: 5 }}
              />
            );
          case 'password':
            return (
              <EditableInput
                {...inputProps}
                iamDomain={iamDomain}
                token={token}
                apiEndpoint={apiEndpoint}
                isPreview={isPreview}
                errorMsg={errorMsg}
              />
            );
          case 'textarea':
            return <textarea {...inputProps} />;
          case 'date':
            // {...inputProps} isShowLabel={false} error={errorMsg}
            return (
              <StyledDatePicker
                value={inputProps.value}
                onChange={inputProps.onChange}
                popupClassName="antsomi-custom-calendar-picker"
                showToday={false}
                prevIcon={<Icon type="icon-ants-angle-left" size="11" />}
                nextIcon={<Icon type="icon-ants-angle-right1" size="11" />}
                allowClear={false}
                suffixIcon={
                  <Icon type="icon-ants-calendar-picker" size="24" color="rgba(0, 0, 0, 0.54)" />
                }
              />
            );
          case 'radio':
            // return <RadioButtonGroup {...inputProps} />;
            return (
              <StyledRadioGroup
                value={+inputProps.value}
                onChange={inputProps.onChange}
                name={inputProps.fieldName}
                disabled={inputProps.disabled}
              >
                {inputProps.arrRadios.map(item => (
                  <Radio value={item.value} key={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </StyledRadioGroup>
            );
          case 'lang':
            return <LanguageSelector {...inputProps} isPreview={isPreview} />;
          default:
            return (
              <Input
                {...inputProps}
                showCurrentLenght={false}
                status={errorMsg ? 'error' : undefined}
                errorMsg={errorMsg}
                style={{ padding: 5 }}
              />
            );
          // return (
          //   <div className="dropdown">
          //     <SearchDropdown {...inputProps} />
          //   </div>
          // );
        }
      } catch (error) {
        return null;
      }
    };

    const renderFieldUI = item => {
      const [
        fieldName,
        {
          fieldLabel,
          fieldType = 'text',
          type = 'text',
          maxLength,
          minLength,
          allowNull,
          position,
          placeholder,
          fontBold,
          checkUnique,
          isUpdate: isUpdateField,
          isEditing: isEditingField,
          verify,
          messageVerify,
          autoComplete,
        },
      ] = item;
      let inputProps: any = {};
      const isUpdate = isUpdateField !== undefined ? isUpdateField : isUpdateMode;
      const isEditing = isEditingField !== undefined ? isEditingField : mode === 'create';
      const isDisabled = allowChangeFields.includes(fieldName)
        ? false
        : isOverwrite || (isUpdateMode && !isUpdate);
      try {
        switch (fieldType) {
          case 'text':
            inputProps = {
              fieldName,
              type,
              maxLength: maxLength || 255,
              minLength: minLength || 0,
              value: form[fieldName],
              onChange: onChange[fieldType](fieldName),
              placeholder: placeholder || fieldLabel,
              className: `form-control ${fieldType === 'number' && 'text-right'}`,
              style: fontBold ? { fontWeight: 'bold', width: 300 } : { width: 300 },
              // ...checkUnique && {onBlur: checkUniqueField({fieldName, config: checkUnique})},
              handleUpdateField,
              disabled: isDisabled,
              mode,
              allowNull,
              isUpdate,
              isEditing,
              isDisabledEdit,
              autoComplete,
              componentKey: form[fieldName],
            };
            break;
          case 'password':
            inputProps = {
              fieldName,
              type,
              maxLength: maxLength || 255,
              minLength: minLength || 0,
              value: form[fieldName],
              lastChangePass: form.last_change_pass,
              onChange: onChange.text(fieldName),
              placeholder: placeholder || fieldLabel,
              className: `form-control ${fieldType === 'number' && 'text-right'}`,
              style: fontBold ? { fontWeight: 'bold', width: 300 } : { width: 300 },
              // ...checkUnique && {onBlur: checkUniqueField({fieldName, config: checkUnique})},
              handleUpdatePassword,
              accountId,
              g2fa: !!+form.g2fa!,
              handleUpdateField,
              setLoading,
              disabled: isDisabled,
              mode,
              allowNull,
              isUpdate,
              isEditing,
              isDisabledEdit,
              autoComplete,
            };
            break;
          case 'radio':
            inputProps = {
              ...fieldsUI[fieldName],
              value: form[fieldName],
              onChange: onChange[fieldType](fieldName),
              handleUpdateField,
              disabled: isDisabled,
              mode,
              isUpdate,
              isEditing,
              isDisabledEdit,
            };
            break;
          case 'lang':
            inputProps = {
              ...fieldsUI[fieldName],
              value: form[fieldName],
              onChange: onChange.select(fieldName),
              // handleUpdateField,
              isDisabled,
              mode,
              isUpdate,
              isEditing,
              isDisabledEdit,
            };
            break;
          case 'date':
            inputProps = {
              ...fieldsUI[fieldName],
              value: form[fieldName],
              onChange: onChange[fieldType](fieldName),
              handleUpdateField,
              isDisabled,
              mode,
              isUpdate,
              isEditing,
              isDisabledEdit,
            };
            break;
          default:
            inputProps = {};
          //     inputProps = {
          //         style: {width: '100%'},
          //         modeSearch: 'SERVER',
          //         onClick: onSelectFieldChange(fieldName),
          //         default: form[fieldName],
          //         autoLoad: true,
          //         flip: false,
          //         ...fieldType,
          //         isUpdate,
          //         isEditing,
          //         isDisabledEdit
          //     };
        }
      } catch (e) {
        handleError(e, {
          path: PATH,
          name: 'renderBlock',
        });
      }

      if (isUpdateMode && !form.initDone) {
        inputProps.onChange = () => null;
      }

      return (
        <div
          className="form-group"
          key={`create_${fieldName}_${position}`}
          style={
            isPreview
              ? { marginTop: 20, height: fieldName === 'password' ? 80 : 30 }
              : { marginTop: 20, height: 'unset' }
          }
        >
          <label className="field-L" style={{ top: isPreview || !inputProps.isUpdate ? 0 : 8 }}>
            {fieldLabel}
            {allowNull || <span className="other-color">*</span>}
          </label>
          <div
            className={clsx('container-field', {
              'w-full': fieldName === 'password',
            })}
          >
            {renderField(fieldType, inputProps)}
            {mode === 'create' && verify && messageVerify ? (
              <div className="checkbox-verify">
                <StyledCheckbox
                  name="verify-email"
                  checked={form.validateEmail}
                  onChange={onChangeValidateEmail}
                >
                  {messageVerify}
                </StyledCheckbox>
                {/* <label>
                          
                            <input type="checkbox" onClick={onChangeValidateEmail} />
                            <span className="icon-check" />

                            <span>{messageVerify}</span>
                        </label> */}
              </div>
            ) : null}
          </div>
          {errors[fieldName] && !['text', 'date', 'password'].includes(fieldType) && (
            <div className="container-field">
              {renderFieldErrors(fieldName, fieldType === 'number')}
            </div>
          )}
        </div>
      );
    };

    const renderFieldErrors = (fieldName, textAlignRight = true, padding = true) => {
      try {
        if (errors[fieldName] && errors[fieldName].length) {
          return (
            <div style={padding ? { padding: '0px 5px' } : {}}>
              {errors[fieldName].map((error, index) => (
                <span
                  key={`create_company_${fieldName}_error_${index}`}
                  className={`error ${textAlignRight && 'text-right'}`}
                  style={{ fontSize: 12 }}
                >
                  {error}
                </span>
              ))}
            </div>
          );
        }

        return null;
      } catch (err) {
        handleError(err, {
          path: PATH,
          name: 'renderFieldErrors',
        });
      }
    };

    const validateField = async ([fieldName, value]) => {
      try {
        value = typeof value === 'string' ? value.trim() : value;
        const fieldUI = fieldsUI[fieldName];

        const fieldErrors: string[] = [];

        if (fieldName === 'password' && form.validateEmail) {
          return [fieldName, fieldErrors];
        }

        const defaultMessage = getDefaultMessage(fieldName);
        let isCharacter = true;

        if (fieldUI) {
          if (value) {
            const { validate } = fieldUI;
            const { minLength, maxLength } = fieldUI;

            if (minLength && value.trim().length < minLength) {
              fieldErrors.push(`${fieldUI.fieldLabel} too short`);
            }

            if (maxLength && value.trim().length > maxLength) {
              fieldErrors.push(`${fieldUI.fieldLabel} too long`);
            }

            // if (fieldUI.fieldType === 'date' && !value) {
            //   fieldErrors.push('Invalid date');
            // }

            if (fieldName === 'full_name') {
              isCharacter = value.indexOf("'") === -1;
              if (!isCharacter) {
                fieldErrors.push('Invalid full name');
              }
            }

            if (validate && validate.length) {
              validate.forEach(validate => {
                const { method, message } = validator[validate];

                if (typeof method === 'function') {
                  if (!method(value)) {
                    fieldErrors.push(message || defaultMessage);
                  }
                }
              });
            }

            if (!fieldErrors.length && fieldUI.checkUnique) {
              const resultUnique = await validateUniqueField({
                fieldName,
                config: fieldUI.checkUnique,
              });

              if (resultUnique) {
                fieldErrors.push(resultUnique);
              }
            }
          } else if (!fieldUI.allowNull) {
            fieldErrors.push(defaultMessage);
          }
        }

        return [fieldName, fieldErrors];
      } catch (e) {
        handleError(e, {
          path: PATH,
          name: 'validateField',
        });
      }
    };

    const validateForm = async () => {
      try {
        // do things when no errors
        if (formRef.current) {
          const arrErrs = await Promise.all(Object.entries(formRef.current).map(validateField));

          const errs = Object.fromEntries(arrErrs as any[]);

          setErrors({
            ...errors,
            ...errs,
          });
          if (Object.values(errs).every(fieldErrors => !fieldErrors.length)) {
            saveAccount();
          }
        }
      } catch (e) {
        handleError(e, {
          path: PATH,
          name: 'validateForm',
        });
      }
    };

    const renderBlock = (group, position: number | null = null) => {
      try {
        return Object.entries(fieldsUI)
          .filter(
            ([fieldName, obj]) =>
              obj.position &&
              group === obj.position[0] &&
              ((position === null && obj.position[1]) || position === obj.position[1]) &&
              (fieldName !== 'password' || !form.validateEmail),
          )
          .map(item => renderFieldUI(item));
      } catch (e) {
        handleError(e, {
          path: PATH,
          name: 'renderBlock',
        });
      }
    };

    const doSaveAccount = async updateData => {
      setOpenChangeEmail(false);
      setLoading(true);

      let result: any = null;
      // loadingCompleteRef.current = false;
      // setTimeout(() => {
      //   if (loadingCompleteRef.current === true) {
      //     if (callback && typeof callback === 'function') {
      //       callback();
      //     }
      //   } else {
      //     loadingCompleteRef.current = true;
      //   }
      // }, 2000);

      result = await axios.put(apiEndpoint, updateData);
      setLoading(false);
      const modeLabel = isUpdateMode ? 'Update' : 'Create';
      if (result && result.data && result.data.data && result.data.data.userId) {
        // if (loadingCompleteRef.current) {
        //   if (callback && typeof callback === 'function') {
        //     callback();
        //   }
        //   return true;
        // } else {
        //   loadingCompleteRef.current = true;
        // }
        showSuccess({ content: `${modeLabel} account successfully!` });

        if (updateData.email) {
          setOriginEmail(updateData.email);
          updateDataRef.current = null;
        }
      } else if (
        result &&
        result.data &&
        result.data.message &&
        result.data.message.includes('Email already exists')
      ) {
        showError({
          content: result.data.message,
        });
      } else {
        showError({
          content: `${modeLabel} account fail, please try again later!`,
        });
      }
    };

    const saveAccount = async () => {
      if (!formRef.current) return;

      const {
        user_id: userId,
        full_name: fullName,
        gender,
        birthday,
        phone,
        address,
        email,
        password,
        account_type: accountType,
        language,
        validateEmail,
      } = formRef.current as TForm;

      const updateData: Record<string, any> = {
        full_name: fullName?.trim(),
        gender,
        birthday: birthday?.format('YYYY-MM-DD'),
        phone: phone && phone.trim(),
        address: address && address.trim(),
        // email: email && email.trim(),
        language,
        // validate_email: !!validateEmail,
        // source: 'aacm',
      };

      if (email?.trim() !== originEmail) {
        updateDataRef.current = {
          ...updateData,
          email: email?.trim(),
        };
        setOpenChangeEmail(true);
        // showConfirm({
        //   title: 'Change email',
        //   content: (
        //     <Flex vertical gap="15px">
        //       <p>Are you sure you want to change your email to {email?.trim()}?</p>
        //       <Input ref={passwordRef} placeholder="Enter your password to save change" />
        //     </Flex>
        //   ),
        //   onOk: () =>
        //     doSaveAccount({
        //       ...updateData,
        //       email,
        //       password: passwordRef.current?.value.trim(),
        //     }),
        // });
      } else {
        doSaveAccount(updateData);
      }

      return false;
    };

    const handleUpdatePassword = async (oldPassword, newPassword) => {
      const result = await axios.put(apiEndpoint, {
        id: accountId,
        new_password: newPassword,
        re_password: newPassword,
        old_password: oldPassword,
        // source: 'aacm',
      });

      return !!(result && result.data && result.data.data && result.data.data.userId);
    };

    const handleUpdateField = async (field, value = undefined) => {
      try {
        const fieldValue = value === undefined ? form[field] : value;

        if (field !== 'avatar') {
          if (!field || fieldsUI[field] === undefined) {
            return false;
          }
          const [fieldName, fieldErrors] = (await validateField([field, fieldValue])) || [];

          setErrors({
            ...errors,
            [fieldName]: fieldErrors,
          });

          if ((fieldErrors && fieldErrors.length) || (errors[field] && errors[field].length)) {
            return false;
          }
        }

        const result = await axios.put(apiEndpoint, {
          [field]: fieldValue,
          // source: 'aacm',
        });

        if (isPermissionUpdate(result)) {
          return;
        }

        if (result && result.data && result.data.data && result.data.data.userId) {
          if (field === 'language') {
            showSuccess({
              content: 'Change language successfully',
              title: 'Success',
            });
          }
          return true;
        }
        return false;
      } catch (error) {
        handleError(error, {});
      }
    };

    const onFileUploading = () => {
      try {
        // props.onProgressLoadingActive();
      } catch (error) {
        handleError(error, {
          component: PATH,
          action: 'onFileUploading',
          args: {},
        });
      }
    };

    const handleChangeLogo = newProps => {
      try {
        // props.onProgressLoadingDeactive();

        if (newProps.url) {
          setIsUploadLogo(true);
          setLogo(newProps);
          setErrors({
            ...errors,
            avatar: null,
          });
          if (mode === 'update') {
            handleUpdateField('avatar', newProps.url);
          }
        }
      } catch (error) {
        handleError(error, {
          component: PATH,
          action: 'handleChangeMainLogo',
          args: { newProps },
        });
      }
    };

    const showFileError = error => {
      let fileError = '';

      switch (error) {
        case FILE_UPLOAD_ERROR.INVALID_FILE_TYPE:
          fileError = 'File type is not supported.||Please use .png or .jpg file.';
          break;
        case FILE_UPLOAD_ERROR.INVALID_FILE_SIZE:
          fileError = 'File size is too big.||Please use under 3MB file.';
          break;
        case FILE_UPLOAD_ERROR.INVALID_FILE_DIMENSION:
          fileError = 'File dimension is too big.||Please use dimensions under 5000 px.';
          break;
        default:
          fileError = '';
      }
      setErrors({
        ...errors,
        avatar: fileError,
      });
    };

    const avatarUrl =
      (logo && logo.url && (logo.isDefault ? '' : ST_URL_UPLOAD_PLATFORM) + logo.url) ||
      (logoManual && logoManual.url) ||
      (!loading ? Image : '');

    try {
      return (
        <Spin spinning={loading}>
          <AccountDetail className="wrap-table">
            <div className="form-profile">
              {/* {loading ? <Loading loading={true} marginTop="35%" /> : null} */}
              <div className="left-profile">
                {logo && <img src={avatarUrl} alt="" />}
                {/* {
                  logo && logo.url ? (
                      <img src={(logo.isDefault ? '' : ST_URL_UPLOAD_PLATFORM) + logo.url}
                          width="132" height="132" alt="" />
                  ) : logoManual && logoManual.url ? (
                      <img src={logoManual.url}
                          width="132" height="132" alt="" />
                  ) : !loading ? (
                      <img src={Image} className="img-account-big" />
                  ) : null
              } */}

                <div className="preview mt-10">
                  {isDisabledEdit || isPreview ? null : (
                    <div className="block-choose-input-file">
                      <UploadAvatar
                        // ref={input => {
                        //   this.fileInput = input;
                        // }}
                        onFileUploading={onFileUploading}
                        onFileUploaded={handleChangeLogo}
                        callbackFileError={showFileError}
                        dimensions={{ width: 5000, height: 5000 }}
                        extensions={['png', 'jpg', 'PNG', 'JPG']}
                        maxFileSize={3072}
                        iamDomain={iamDomain}
                        authStr={`?_token=${token}&_user_id=${accountId}&_account_id=${accountId}`}
                        setLoading={setLoading}
                      />
                      <span className="choose-file-text">Change avatar</span>
                    </div>
                  )}
                  {errors.avatar ? (
                    <div
                      className="error"
                      style={{
                        width: 'fit-content',
                        textAlign: 'left',
                        margin: '0 auto',
                      }}
                    >
                      <div className="error">
                        {errors.avatar && errors.avatar.includes('||')
                          ? errors.avatar.split('||')[0]
                          : null}
                      </div>
                      <div className="error">
                        {errors.avatar && errors.avatar.includes('||')
                          ? errors.avatar.split('||')[1]
                          : null}
                      </div>
                    </div>
                  ) : null}
                </div>
              </div>
              <div className="right-profile">
                {mode === 'update' ? renderBlock(1, 0) : null}
                {renderBlock(2, null)}
                {renderBlock(1, null)}
                {renderBlock(3, null)}
                {renderBlock(4, null)}
              </div>
            </div>
            {contextHolder}
          </AccountDetail>
          <ModalV2
            title="Change email"
            open={!!openChangeEmail}
            okText="Confirm"
            onOk={() => {
              if (!form.password?.trim()) {
                setErrors({
                  ...errors,
                  password: 'Please input password!',
                });

                return;
              }
              doSaveAccount({
                ...updateDataRef.current,
                password: form.password?.trim(),
              });
            }}
            onCancel={() => setOpenChangeEmail(false)}
            width={330}
          >
            <Flex vertical gap="15px">
              <p>
                Are you sure you want to change your email to{' '}
                <strong>{updateDataRef.current?.email.trim()}</strong>?
              </p>
              <Input
                value={form.password}
                onChange={e => setForm(p => ({ ...p, password: e.target.value.trim() }))}
                status={errors.password ? 'error' : undefined}
                errorMsg={errors.password}
                placeholder="Enter your password to save change"
              />
            </Flex>
          </ModalV2>
        </Spin>
      );
    } catch (error) {
      handleError(error, {
        path: PATH,
        action: 'Menu',
        args: {},
      });
    }
  }),
);
