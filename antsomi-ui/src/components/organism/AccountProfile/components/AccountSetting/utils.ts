import { FILE_UPLOAD_ERROR } from './constants';

export const getObjectPropSafely = (fn, defaultValue = '') => {
  try {
    return fn();
  } catch (e) {
    return defaultValue;
  }
};

export const getDefaultMessage = fieldName => {
  fieldName = fieldName.replace('_', ' ');
  return `Invalid ${fieldName
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .split(' ')
    .map(word => word.charAt(0).toLowerCase() + word.slice(1))
    .join(' ')}`;
};

export const getDuplicateMessage = fieldName => {
  return `The ${fieldName
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .split(' ')
    .map(word => word.charAt(0).toLowerCase() + word.slice(1))
    .join(' ')} is already being used. Please try another.`;
};

export const validateEmail = value => {
  const emailRegex =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

  return emailRegex.test(value);
};

export const validateUrl = _str => {
  const urlPattern =
    /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)/g;

  return urlPattern.test(_str);
};

export const validator = {
  email: {
    method: validateEmail,
    message: 'Invalid email format',
  },
  url: {
    method: validateUrl,
    message: 'Invalid URL format',
  },
  id: {
    method: val => !!val.id,
  },
};

export const isPermissionUpdate = response =>
  response && response.data && response.data.code && response.data.code === 403;

export const handleError = (e, params) => {
  console.error(e)
};

export const validateFile = async (file, extensions, maxFileSize, dimensions, callbackError) => {
  const _URL = window.URL || window.webkitURL;
  let isValidDimension = true;
  let isValidImage = true;
  const fileExtension = file.name.split('.').pop();
  const isExtensionValid = extensions.includes(fileExtension);

  if (!isExtensionValid) {
    callbackError(FILE_UPLOAD_ERROR.INVALID_FILE_TYPE);
    return false;
  }

  const isFileSizeValid = file.size < maxFileSize * 1024; // in KB

  if (!isFileSizeValid) {
    callbackError(FILE_UPLOAD_ERROR.INVALID_FILE_SIZE);
    return false;
  }

  isValidDimension = await new Promise((resolve, reject) => {
    const img = new Image();

    img.onload = () => {
      if (img.width > dimensions.width || img.height > dimensions.height) {
        isValidDimension = false;
        resolve(false);
      }
      resolve(true);
    };
    img.onerror = () => {
      isValidImage = false;
      resolve(false);
    };
    img.src = _URL.createObjectURL(file);
  });

  if (!isValidDimension) {
    callbackError(FILE_UPLOAD_ERROR.INVALID_FILE_DIMENSION);
    return false;
  }

  return true;
};
