import styled from 'styled-components';
import { Typography } from '@antscorp/antsomi-ui/es/components/atoms/Typography';
import { Select } from '@antscorp/antsomi-ui/es/components/molecules/Select';

export const StyledSelect = styled(Select)`
  .img-lang {
    width: 21px;
    height: 15px;
    margin-right: 10px;
  }
  .img-lang-en {
    background: url('~Assets/images/langs/english.png') no-repeat;
  }
  .img-lang-vi {
    background: url('~Assets/images/langs/vietnam.png') no-repeat;
  }
`;

export const StyledSelectOption = styled(Typography)`
  display: flex !important;
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
  gap: 6px;
`;
