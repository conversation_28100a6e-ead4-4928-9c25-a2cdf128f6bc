/* eslint-disable jsx-a11y/iframe-has-title */
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable no-multi-str */
// Libraries
import { isEmpty, keyBy } from 'lodash';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

// Types
import { CaptureTypeProps } from '@antscorp/antsomi-ui/es/components/molecules/CaptureScreen/types';
import { ControlPosition } from 'react-draggable';
import {
  AllAppOptionsProps,
  HelpDocItem,
  IHelpProps,
  ImageDrewProps,
  MenuItemTypeProps,
  StreamTrackProps,
  ValueFormProps,
} from './types';
// Assets
import '@antscorp/icons/main.css';

// Services
import TicketService from '@antscorp/antsomi-ui/es/services/Ticket';
import Service from '@antscorp/antsomi-ui/es/components/organism/TicketEditor/Service';

// Component
import { LoadingOutlined } from '@ant-design/icons';
import { QuillEditor } from '@antscorp/antsomi-ui/es/components';
import Icon from '@antscorp/icons';
// import { Editor } from '@tinymce/tinymce-react';
import { Button, Spin } from 'antd';
import {
  ButtonFeedback,
  ControlGroup,
  ControlLabel,
  EditorWrapper,
  ErrorMessage,
  FlexCenter,
  Header,
  HiddenBlock,
  IconButtonStyled,
  IframeHelp,
  Image,
  Label,
  LabelTitle,
  Link,
  Overlay,
  PreviewBox,
  SendFeedback,
  Span,
  Text,
  TitleSearch,
  TreeContent,
  Video,
  Wrapper,
  WrapperBody,
  WrapperContentHelp,
  WrapperFooter,
  WrapperFooterSpace,
  WrapperHeader,
  WrapperIconEditor,
  WrapperImage,
  WrapperInputFile,
  WrapperLinkFiles,
  WrapperLinkItemFiles,
  WrapperLoading,
  WrapperSearch,
} from './styled';

// Atoms
import { Input } from '@antscorp/antsomi-ui/es/components/atoms';

// Molecules
import {
  CaptureScreen,
  ChatBox,
  PopupDraggable,
  Select,
  Dropdown,
} from '@antscorp/antsomi-ui/es/components/molecules';

// Constants
import { THEME, TINYMCE_API_KEY } from '@antscorp/antsomi-ui/es/constants';
import {
  ATTACH_CAPTURE_TYPES,
  ATTACH_KEYS,
  DEFAULT_POSITIONS,
  MENU_KEYS,
  MESSAGE_TYPE,
  PORTALS_ANTSOMI_PACKAGE_UI_KEY,
  PORTALS_ANTSOMI_PACKAGE_UI_KEY_POPUP,
  REPORT_TYPES,
  TICKET_CUSTOM_MESSAGE_KEY,
} from './constants';

// Utils
import {
  BugIcon,
  CameraIcon,
  OpenUrlIcon,
  RequestIcon,
  ScreenshotMonitorIcon,
} from '@antscorp/antsomi-ui/es/components/icons';
import {
  base64ToFile,
  convertBlobToFile,
  generateUniqueId,
  mergeAudioStreams,
} from '@antscorp/antsomi-ui/es/components/molecules/CaptureScreen/utils';
import { DataNode } from 'antd/es/tree';
import { expendDefault, formatParams, postCustomEvent } from './utils';
import { useGetListUser } from './queries';
import { TCallBack } from '../../molecules/QuillEditor/QuillEditor';

const antIcon = <LoadingOutlined style={{ fontSize: 40 }} spin />;
const Loading = ({ isLoading, height, width }) =>
  isLoading && (
    <WrapperLoading className="loader-container" height={height} width={width}>
      <Spin indicator={antIcon} />
    </WrapperLoading>
  );

type ReportValueType = (typeof REPORT_TYPES)[keyof typeof REPORT_TYPES];

const Help: React.FC<IHelpProps> = props => {
  const { configs, triggerType, buttonProps, boundsDraggable, isShowResizeHover, children } = props;
  const {
    apiKey,
    domainPlatform,
    appCode,
    domain,
    portalId,
    token,
    userId,
    config,
    domainTicket,
    domainUpload,
    avatar,
  } = configs;

  // const [isOpenDropdown, setIsOpenDropdown] = useState<boolean>(false);
  const [defaultPositionDrawActions, setDefaultPositionDrawActions] = useState<ControlPosition>(
    DEFAULT_POSITIONS.capture,
  );
  const [defaultPositionRecordActions, setDefaultPositionRecordActions] = useState<ControlPosition>(
    DEFAULT_POSITIONS.record,
  );
  const [isMainLoading, setIsMainLoading] = useState<boolean>(false);
  const [isShowPopup, setIsShowPopup] = useState<boolean>(false);
  const [title, setTitle] = useState<string>('');
  const [type, setType] = useState<ReportValueType>(REPORT_TYPES.HELP);
  const [isValidAll, setIsValidAll] = useState<boolean>(true);
  const [valueInput, setValueInput] = useState<ValueFormProps>({
    title: '',
    ticketType: '',
    priority: 'normal',
    category: '',
    ownerId: Number(userId),
    followers: [],
    message: '',
    files: [],
    referenceUrl: '',
    fileZendesk: [],
  });

  const { listUsers, isFetchingUsers } = useGetListUser({
    domain,
    config,
    userId,
    token,
  });

  const [errFile, setErrFile] = useState<any>({
    isError: false,
    message: '',
  });
  const [pathHelp, setHelp] = useState<string>('');
  const [urlImageDrawer, setUrlImageDrawer] = useState<string>('');
  const [urlVideoPreview, setUrlVideoPreview] = useState<any>('');
  const [imageDrew, setImageDrew] = useState<ImageDrewProps>({
    imageName: '',
    dataURL: '',
  });
  const [isOpenImageDrawer, setIsOpenImageDrawer] = useState<boolean>(false);
  const [captureType, setCaptureType] = useState<CaptureTypeProps>(ATTACH_KEYS.CAPTURE);
  // const [open, setOpen] = useState<boolean>(false);
  const [dataRecorded, setDataRecorded] = useState<any>([]);
  const [dataListHelp, setDataListHelp] = useState<any>([]);
  const [isMute, setIsMute] = useState<boolean>(false);
  const [appTargeting, setAppTargeting] = useState<AllAppOptionsProps>({
    label: '',
    value: '',
  });
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [allAppOptions, setAllAppOptions] = useState<Array<AllAppOptionsProps>>([]);
  const [isForceStopRecorder, setIsForceStopRecorder] = useState<boolean>(false);
  const [isLoadingListHelp, setIsLoadingHelp] = useState<boolean>(true);
  const fetchingRef = useRef(false);
  const recorderRef = useRef<MediaRecorder>();
  const streamTracks = useRef<StreamTrackProps>();
  const allAppOptionsRef = useRef<Array<AllAppOptionsProps>>([]);
  const [fileInputKey, setFileInputKey] = useState(0);
  const [isLoadingUpload, setIsLoadingUpload] = useState<boolean>(false);

  // const showModal = (captureKey: CaptureTypeProps) => {
  //   setOpen(true);
  //   setCaptureType(captureKey);
  // };

  const items = useMemo(
    (): any => [
      {
        label: (
          <a
            href="https://docs.antsomi.com/cdp-365-user-guide-en/release-note/2023"
            target="_blank"
            rel="noreferrer"
            style={{ textDecoration: 'none' }}
          >
            <Label
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: 33,
                width: '100%',
              }}
            >
              New features & announcements
              <OpenUrlIcon size={20} color={THEME.token?.colorIcon} />
            </Label>
          </a>
        ),
        key: MENU_KEYS.FEATURE_ANNOUNCEMENT,
      },
      {
        label: <Label>Leave feedback</Label>,
        key: MENU_KEYS.FEEDBACK,
      },
      {
        label: <Label>Get help</Label>,
        key: MENU_KEYS.HELP,
      },
      {
        label: <Label>Get ideas with Antsomi Intelligence</Label>,
        key: MENU_KEYS.CHAT,
      },
      {
        type: 'divider',
      },
      {
        label: (
          <a
            href={`${domainTicket}/${portalId}/#/${userId}/tickets`}
            target="_blank"
            rel="noreferrer"
            style={{ textDecoration: 'none' }}
          >
            <Label className="verify-support">Verify support request</Label>
          </a>
        ),
        key: MENU_KEYS.SUPPORT,
      },
    ],
    [domainTicket, portalId, userId],
  );

  const modifiedDomain = useMemo(() => {
    const regex = /^(https?:)/;

    if (regex.test(domain)) {
      return domain.replace(regex, '');
    }

    return domain;
  }, [domain]);

  const fetchDataListHelp = () => {
    setIsLoadingHelp(true);
    TicketService.help.callApi
      .getList({ appCode, role: '1' }, domainPlatform, token, config, userId)
      .then(res => {
        if (res && res.data.code === 200 && res.data) {
          const { data } = res.data;
          setDataListHelp(data);
          setIsLoadingHelp(false);
          setExpandedKeys(expendDefault(data));
        } else {
          setIsLoadingHelp(true);
        }
      })
      .catch(err => {
        // console.log('err ===>', err)
      });
  };

  // const handleCancel = () => {
  //   setOpen(false);
  // };
  const handleClick = (data: MenuItemTypeProps): any => {
    const { key = '' } = data;
    switch (key) {
      case MENU_KEYS.FEEDBACK: {
        setType(REPORT_TYPES.FEEDBACK);
        setTitle('Send feedback to Antsomi');
        setIsShowPopup(true);
        break;
      }
      case MENU_KEYS.HELP: {
        fetchDataListHelp();
        setType(REPORT_TYPES.HELP);
        setTitle('Get Help');
        setIsShowPopup(true);
        break;
      }
      case MENU_KEYS.CHAT: {
        setType(REPORT_TYPES.CHAT);
        setTitle('Antsomi Intelligence');
        setIsShowPopup(true);
        break;
      }
      default: {
        // setIsOpenDropdown(false);
        break;
      }
    }
  };

  const handleUploadFile = async file => {
    const uploadService = Service.tickets.callApi.uploadFile({
      domain: domainUpload,
      token,
      userId,
    });

    try {
      setIsMainLoading(true);

      const response = await uploadService!({ files: [file] });

      const fileZendesk = await uploadZendeskService(file);

      if (response.code === 200) {
        setValueInput(prevVal => ({
          ...prevVal,
          files: [...valueInput.files, response.data[0]],
        }));
      }

      if (!isEmpty(fileZendesk)) {
        setValueInput(prevVal => ({
          ...prevVal,
          fileZendesk: [...valueInput.fileZendesk, fileZendesk],
        }));
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('error :>', error);
    } finally {
      setIsMainLoading(false);
    }
  };

  useEffect(() => {
    const handleFetchAllApps = async () => {
      try {
        setIsMainLoading(true);

        const res = await TicketService.tickets.callApi.getCustomFields(
          {},
          domainTicket,
          token,
          config,
          userId,
          'get-custom-fields',
        );

        if (res && res.code === 200) {
          const { data } = res;

          if (data && Array.isArray(data.fields)) {
            const features = data.fields.find(item => item.value === 'feature');
            const { field_options = [] } = features || {};

            const temp = field_options.map(app => ({
              label: app.name,
              value: app.value,
              raw: app,
            }));
            setAppTargeting(temp[0]);
            setAllAppOptions(temp);
            allAppOptionsRef.current = temp;
          }
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('error :>', error);
      } finally {
        setIsMainLoading(false);
      }
    };

    if (!fetchingRef.current && isEmpty(allAppOptions)) {
      fetchingRef.current = true;
      handleFetchAllApps();
    }
  }, [allAppOptions, config, domainTicket, token, userId]);

  const handleOnchangeFile = e => {
    const file = e.target?.files[0];
    if (!file) return;

    const sizeFile = file.size;
    const fileType = file.type;

    const isImage = fileType.startsWith('image/');

    const limitSize = isImage ? 10 * 1024 * 1024 : 50 * 1024 * 1024;
    if (sizeFile >= limitSize) {
      setErrFile({
        isError: true,
        message: `Maximum file size is reached (10MB for image & 50MB for other types).`,
      });
    } else {
      setErrFile({
        isError: false,
        message: '',
      });
      handleUploadFile(e.target.files[0]);
    }
    setFileInputKey(fileInputKey + 1);
  };

  const handleRemoveFile = token => {
    let newListFile = valueInput.files;
    newListFile = newListFile.filter(list => list?.token !== token);
    setValueInput(prevVal => ({ ...prevVal, files: newListFile }));
  };

  const handleRemoveFileV2 = (index: number) => {
    let newListFile = valueInput.files;
    newListFile = newListFile.filter((_, fileIndex) => fileIndex !== index);

    const newFileZendesk = valueInput.fileZendesk.filter((_, fileIndex) => fileIndex !== index);

    setValueInput(prevVal => ({
      ...prevVal,
      files: newListFile,
      fileZendesk: newFileZendesk,
    }));

    setFileInputKey(prev => prev + 1);
  };

  const uploadZendeskService = async listFiles => {
    const formData = new FormData();

    formData.append('file', listFiles);
    const params = {
      data: formData,
    };

    const responseZendesk = await Service.tickets.callApi.upload(
      params,
      domainTicket,
      token,
      config,
      userId,
      'ticket',
    );

    if (responseZendesk.code === 200) {
      return responseZendesk.data;
    }

    return [];
  };

  const handleResetData = () => {
    setUrlImageDrawer('');
    setUrlVideoPreview('');
    setIsValidAll(true);
    // setIsOpenDropdown(false);
    setValueInput(prev => ({
      ...prev,
      message: '',
      title: '',
      files: [],
      fileZendesk: [],
    }));
    setIsShowPopup(false);
    setErrFile({
      isError: false,
      message: '',
    });
    setImageDrew({
      imageName: '',
      dataURL: '',
    });
    setDataRecorded([]);
    setSearchValue('');
    setAutoExpandParent(false);
    setExpandedKeys([]);
  };

  useEffect(() => {
    if (dataRecorded) {
      const dataRecordedBlob = new Blob(dataRecorded, { type: 'video/mp4' });
      const url = URL.createObjectURL(dataRecordedBlob);
      setUrlVideoPreview(url);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataRecorded]);

  useEffect(() => {
    if (isForceStopRecorder && !isEmpty(dataRecorded)) {
      setDataRecorded([]);
      setIsForceStopRecorder(false);
    }
  }, [dataRecorded, isForceStopRecorder]);

  useEffect(
    () => () => {
      if (streamTracks.current) {
        const { desktopStream, voiceStream } = streamTracks.current;
        if (desktopStream && voiceStream) {
          desktopStream.getTracks().forEach(track => track.stop());
          voiceStream.getTracks().forEach(track => track.stop());
        }
      }
    },
    [],
  );

  const handleCreateRecorder = async () => {
    try {
      let controller;
      if (
        'CaptureController' in window &&
        'setFocusBehavior' in (window as any).CaptureController.prototype
      ) {
        controller = new (window as any).CaptureController();
        controller.setFocusBehavior('no-focus-change');
      }

      const displayMediaOptions: any = {
        // preferCurrentTab: true,
        video: {
          cursor: 'always',
        },
        audio: true,
        controller,
        selfBrowserSurface: 'include',
      };

      const desktopStream = await navigator.mediaDevices.getDisplayMedia(displayMediaOptions);
      const voiceStream = await navigator.mediaDevices.getUserMedia({
        video: false,
        audio: !isMute,
      });

      const tracks = [
        ...desktopStream.getVideoTracks(),
        ...mergeAudioStreams(desktopStream, voiceStream),
      ];

      const stream = new MediaStream(tracks);
      const recorder = new MediaRecorder(stream);
      const data: Array<any> = [];

      // ondataavailable -> fired periodically each time timeslice millisecond of media have been recorded
      // or when the entire media is recorded if no timeslice is specified
      recorder.ondataavailable = event => data.push(event.data);

      const stopped = new Promise((resolve, reject) => {
        recorder.onstop = resolve;
        recorder.onerror = (event: any) => reject(event.name);
      });
      Promise.all([stopped, recorder]).then(() => {
        setDataRecorded(data);
      });

      const videoTrack = desktopStream.getVideoTracks()[0];
      // eslint-disable-next-line func-names
      videoTrack.onended = function () {
        if (recorder) {
          recorder.stop();
        }
        desktopStream.getTracks().forEach(track => track.stop());
        voiceStream.getTracks().forEach(track => track.stop());
        setIsShowPopup(true);
        setIsOpenImageDrawer(false);
      };

      recorderRef.current = recorder;
      streamTracks.current = {
        desktopStream,
        voiceStream,
      };
      setIsForceStopRecorder(false);
      setIsOpenImageDrawer(true);
      // handleCancel();
      setIsShowPopup(false);
    } catch (error) {
      console.log('error :>', error);
      setIsShowPopup(true);
    }
  };

  const handleCaptureScreen = async () => {
    try {
      let controller;
      if (
        'CaptureController' in window &&
        'setFocusBehavior' in (window as any).CaptureController.prototype
      ) {
        controller = new (window as any).CaptureController();
        controller.setFocusBehavior('no-focus-change');
      }
      const displayMediaOptions: any = {
        // preferCurrentTab: true,
        video: {
          displaySurface: 'browser',
          cursor: 'always',
        },
        audio: false,
        surfaceSwitching: 'include',
        controller,
        selfBrowserSurface: 'include',
      };

      // navigator.mediaDevices.enumerateDevices().then(value => {
      //   console.log('insideee.dev - list :>', value);
      // });

      // asking permission to use a media input to record current tab
      const supportedConstraints = navigator.mediaDevices.getSupportedConstraints();

      // if (supportedConstraints.displaySurface) {
      //   displayMediaOptions.video.displaySurface = 'monitor';
      // }
      if (supportedConstraints.width) {
        displayMediaOptions.video.width = { ideal: 1920, max: 1920 };
      }
      if (supportedConstraints.height) {
        displayMediaOptions.video.heigh = { ideal: 1080 };
      }
      if (supportedConstraints.aspectRatio) {
        displayMediaOptions.video.aspectRatio = 1.777777778;
      }
      if (supportedConstraints.frameRate) {
        displayMediaOptions.video.frameRate = { max: 30 };
      }
      const stream = await navigator.mediaDevices.getDisplayMedia(displayMediaOptions);
      const video = document.createElement('video');

      video?.addEventListener('loadedmetadata', () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // passing video width & height as canvas width & height
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        video.play(); // playing the video so the drawn image won't be black or blank
        // drawing an image of the captured video stream
        ctx?.drawImage(video, 0, 0, canvas.width, canvas.height);
        stream.getVideoTracks()[0].stop(); // terminating first video track of the stream

        // passing canvas data url as screenshot preview src
        setUrlImageDrawer(canvas.toDataURL());
        setIsOpenImageDrawer(true);
        // handleCancel();
        setIsShowPopup(false);
      });
      video.srcObject = stream; // passing capture stream data as video source object
    } catch (error) {
      // if image couldn't capture by any reason, then alert the msg
      // eslint-disable-next-line no-console
      console.info('Failed to capture screenshot!', error);
      setIsShowPopup(true);
    }
  };

  const callback = (type: string, dataIn?: any): void => {
    switch (type) {
      case 'ON_CLOSE_POPUP': {
        handleResetData();
        break;
      }
      case 'ON_BACK_POPUP': {
        setType(REPORT_TYPES.HELP);
        break;
      }
      case REPORT_TYPES.ISSUE: {
        setTitle('Report an issue to Antsomi');
        setType(type);
        break;
      }
      case REPORT_TYPES.IDEA: {
        setTitle('Request an idea to Antsomi');
        setType(type);
        break;
      }
      case 'CLOSE_DRAWER': {
        setIsOpenImageDrawer(false);
        setIsShowPopup(true);

        if (captureType === ATTACH_KEYS.CAPTURE) {
          setUrlImageDrawer('');
        } else if (captureType === ATTACH_KEYS.RECORD) {
          setIsForceStopRecorder(true);
          const recorder = recorderRef.current as MediaRecorder;
          const { desktopStream, voiceStream } = streamTracks.current as StreamTrackProps;

          if (recorder && recorder.state === 'recording') {
            recorder.stop();
          }

          if (desktopStream && voiceStream) {
            desktopStream.getTracks().forEach(track => track.stop());
            voiceStream.getTracks().forEach(track => track.stop());
          }
        }
        break;
      }
      case 'ON_DRAWER_DONE': {
        setIsOpenImageDrawer(false);
        setImageDrew(dataIn as ImageDrewProps);
        setIsShowPopup(true);
        break;
      }
      case 'STOP_RECORDER': {
        setIsOpenImageDrawer(false);
        setIsShowPopup(true);
        streamTracks.current = undefined;
        break;
      }
      case 'ON_CHANGE_MUTE': {
        setIsMute(prevMute => !prevMute);
        break;
      }
      case 'SET_POSITION_DRAG': {
        const { name = '', x = 0, y = 0 } = dataIn;

        if (name === ATTACH_KEYS.CAPTURE) {
          setDefaultPositionDrawActions({ x, y });
        }
        if (name === ATTACH_KEYS.RECORD) {
          setDefaultPositionRecordActions({ x, y });
        }
        break;
      }
      case 'RESET_POSITION_DRAG': {
        setDefaultPositionDrawActions(DEFAULT_POSITIONS.capture);
        setDefaultPositionRecordActions(DEFAULT_POSITIONS.record);
        break;
      }
      default: {
        break;
      }
    }
  };

  useEffect(() => {
    const handleEvent = e => {
      const { type, helpType, helpInfo, dataImage } = e.data;

      if (!type || !helpType || !helpInfo) {
        return;
      }

      switch (type) {
        case 'open_antsomi_help':
          if (REPORT_TYPES[helpType]) {
            setIsShowPopup(true);
            callback(REPORT_TYPES[helpType]);
            setValueInput(prev => ({
              ...prev,
              ...helpInfo,
            }));

            if (dataImage) {
              setImageDrew({
                imageName: dataImage.imageName,
                dataURL: dataImage.dataUrl,
              });
            }

            if (helpInfo.feature && Array.isArray(allAppOptions)) {
              const targetApp = allAppOptionsRef.current.find(
                item => item.value === helpInfo.feature,
              );
              if (targetApp) setAppTargeting(targetApp);
            }
          }
          break;
        default:
      }
    };

    window.addEventListener('message', handleEvent);

    return () => {
      handleResetData();
      window.removeEventListener('message', handleEvent);
    };
  }, []);

  const handleCreateTicket = async params => {
    try {
      setIsMainLoading(true);
      let attachments: string[] = [];
      let attachmentsZendesk: any[] = [];

      const uploadServices = TicketService.tickets.callApi.uploadImg({
        domain: domainUpload,
        token,
        userId,
      });

      const { imageDrew = {}, dataRecorded = [], type = '', ...restParams } = params;
      if (params.attachments && !isEmpty(params.attachments)) {
        attachments = [...params.attachments];
      }

      if (params.attachmentsZendesk && !isEmpty(params.attachmentsZendesk)) {
        attachmentsZendesk = [...params.attachmentsZendesk];
      }

      if (imageDrew.dataURL) {
        const imageFile = base64ToFile(
          imageDrew.dataURL,
          `${imageDrew.imageName}-${generateUniqueId()}.png`,
        );
        if (imageFile) {
          const response = await uploadServices!({ files: [imageFile], mode: 'file' });
          const fileZendesk = await uploadZendeskService(imageFile);

          if (response.code === 200) {
            attachments.push(response.data[0]);
          }

          if (!isEmpty(fileZendesk)) {
            attachmentsZendesk.push(fileZendesk?.token);
          }
        }
      }

      if (!isEmpty(dataRecorded)) {
        const blob = dataRecorded[0];
        const blobName = `video-${generateUniqueId()}.mp4`;
        const videoFile = convertBlobToFile(blob, blobName);

        if (videoFile) {
          const response = await uploadServices!({ files: [videoFile], mode: 'video' });
          const fileZendesk = await uploadZendeskService(videoFile);

          if (response.code === 200) {
            attachments.push(response.data[0]);
          }

          if (!isEmpty(fileZendesk)) {
            attachmentsZendesk.push(fileZendesk?.token);
          }
        }
      }

      const dataToAPI = {
        ...restParams,
        attachments,
        attachmentsZendesk,
        networkId: Number(portalId),
        ticketType: type === REPORT_TYPES.ISSUE ? 'bug' : 'request',
      };

      const res = await TicketService.tickets.callApi.createTicket(
        dataToAPI,
        domainTicket,
        token,
        config,
        userId,
      );

      if (res.code === 200) {
        postCustomEvent(TICKET_CUSTOM_MESSAGE_KEY, {
          type: MESSAGE_TYPE.TICKET_CREATE_STATUS,
          value: true,
        });
      } else {
        postCustomEvent(TICKET_CUSTOM_MESSAGE_KEY, {
          type: MESSAGE_TYPE.TICKET_CREATE_STATUS,
          value: false,
        });
      }

      handleResetData();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('error :>', error);
    } finally {
      setIsMainLoading(false);
    }
  };

  const handleSubmit = () => {
    if (!valueInput.title || !valueInput.message) {
      setIsValidAll(false);
      return;
    }

    if (!errFile.isError) {
      const params = formatParams({ ...valueInput, feature: appTargeting });
      const mapUserById = keyBy(listUsers, 'userId');

      const ownerEmail = mapUserById[params.ownerId || '']?.email;

      const referenceUrl = window.location.href;

      handleCreateTicket({
        ...params,
        type,
        imageDrew,
        dataRecorded,
        submitterId: Number(userId),
        submitterEmail: ownerEmail,
        referenceUrl,
      });
    }
  };

  // console.log('insideee.dev - data :>', valueInput, appTargeting, errFile);

  const handleShareCapture = (type: CaptureTypeProps) => {
    setCaptureType(type);
    setIsShowPopup(false);
    return () => {
      if (type === ATTACH_KEYS.CAPTURE) {
        handleCaptureScreen();
      } else {
        handleCreateRecorder();
      }
    };
  };

  const handleChangeAttachFile = (
    key: ATTACH_KEYS.CAPTURE | ATTACH_KEYS.RECORD,
    type: 'edit' | 'delete',
  ) => {
    if (type === 'edit') {
      // showModal(key);
      handleShareCapture(key)();
    } else if (type === 'delete') {
      if (key === ATTACH_KEYS.CAPTURE) {
        setImageDrew({
          imageName: '',
          dataURL: '',
        });
      } else if (key === ATTACH_KEYS.RECORD) {
        setDataRecorded([]);
      }
    }
  };
  const onSelect = (selectedKeys, info) => {
    const { node } = info;
    const { path } = node as HelpDocItem;
    if (path) {
      setHelp(path);
      setType(REPORT_TYPES.HELP_V1);
    }

    // console.log('selected', selectedKeys, info);
  };

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  const getParentKey = (key: React.Key, tree: DataNode[]): React.Key => {
    let parentKey: React.Key;
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.children) {
        if (node.children.some(item => item.key === key)) {
          parentKey = node.key;
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children);
        }
      }
    }
    return parentKey!;
  };

  // const onExpand: DirectoryTreeProps['onCheck'] = (checkedKeys, info) => {
  //   console.log('onCheck', checkedKeys, info);
  // };
  const dataList: { key: React.Key; title: any }[] = [];
  const generateList = (data: DataNode[]) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      const { key, title } = node;
      dataList.push({ key, title });
      if (node.children) {
        generateList(node.children);
      }
    }
  };
  generateList(dataListHelp);
  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    const newExpandedKeys = dataList
      .map(item => {
        if (item.title.toString().toLowerCase().indexOf(value.toLowerCase()) > -1) {
          return getParentKey(item.key, dataListHelp);
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);
    setExpandedKeys(newExpandedKeys as React.Key[]);
    setSearchValue(value);
    setAutoExpandParent(true);
  };

  const renderIcon = (name, style) =>
    ({
      'icon-ants-capture': <ScreenshotMonitorIcon style={style} />,
      'icon-ants-camera': <CameraIcon style={style} />,
    })[name];

  const treeData = useMemo(() => {
    const loop = (data: HelpDocItem[]): HelpDocItem[] =>
      data.map(item => {
        const strTitle = item.title as string;
        const index = strTitle.toString().toLowerCase().indexOf(searchValue.toLowerCase());
        const beforeStr = strTitle.substring(0, index);
        const afterStr = strTitle.slice(index + searchValue.length);
        const title =
          index > -1 ? (
            <Span styles={item.children}>
              {beforeStr}
              <TitleSearch>{searchValue}</TitleSearch>
              {afterStr}
            </Span>
          ) : (
            <Span styles={item.children}>{strTitle}</Span>
          );
        if (item.children) {
          return { title, key: item.key, children: loop(item.children) };
        }
        return {
          title,
          key: item.key,
          path: item.path,
        };
      });

    return loop(dataListHelp);
  }, [searchValue, dataListHelp]);
  const renderAttachActions = (): React.ReactNode =>
    ATTACH_CAPTURE_TYPES.map(item => (
      <ControlGroup key={item.key} size={10} direction="vertical" style={{ width: 'auto' }}>
        <ButtonFeedback
          height={30}
          borderRadiusCustom={3}
          icon={renderIcon(item.icon, {
            fill: THEME.token?.colorPrimary,
            maxWidth: '24px',
          })}
          onClick={() => handleShareCapture(item.key)()}
        >
          {item.label}
        </ButtonFeedback>
        {((item.key === ATTACH_KEYS.CAPTURE && imageDrew.dataURL) ||
          (!isForceStopRecorder &&
            !isEmpty(dataRecorded) &&
            urlVideoPreview &&
            item.key === ATTACH_KEYS.RECORD)) && (
          <PreviewBox>
            <Overlay className="overlay-preview">
              <FlexCenter style={{ height: '100%', gap: 10 }}>
                {item.actions?.map(action => (
                  <ButtonFeedback
                    key={`${item.key}-${action.key}`}
                    height={30}
                    style={{
                      borderRadius: `${THEME.token?.borderRadius}px`,
                      width: 34,
                    }}
                    borderRadiusCustom={3}
                    icon={
                      <Icon
                        type={action.icon}
                        style={{
                          color: THEME.token?.colorPrimary,
                          fontSize: '24px',
                        }}
                      />
                    }
                    onClick={() =>
                      handleChangeAttachFile(item.key, action.key as 'edit' | 'delete')
                    }
                  />
                ))}
              </FlexCenter>
            </Overlay>
            {item.key === ATTACH_KEYS.CAPTURE ? (
              <Image src={imageDrew.dataURL} alt="send feedback" isFull />
            ) : (
              <Video src={urlVideoPreview} muted autoPlay={false} controls={false} />
            )}
          </PreviewBox>
        )}
      </ControlGroup>
    ));

  const renderContentPopup = (type: string): React.ReactNode | null => {
    switch (type) {
      case REPORT_TYPES.FEEDBACK: {
        return (
          <SendFeedback>
            <FlexCenter>
              <WrapperImage>
                <HiddenBlock />
                <Image
                  src="https://st-media-template.antsomi.com/upload/2023/06/16/aa5d68c3-40a2-425f-beb4-cb1abd89e8cb.png"
                  alt="send feedback"
                  style={{ maxWidth: 'unset' }}
                />
              </WrapperImage>
            </FlexCenter>
            <FlexCenter style={{ margin: '50px 3px 0' }}>
              <FlexCenter
                onClick={e => {
                  e.stopPropagation();
                  callback(REPORT_TYPES.ISSUE);
                }}
              >
                <ButtonFeedback
                  icon={
                    // <Icon
                    //   type="icon-ants-bug"
                    //   style={{ color: `${THEME.token?.colorIcon}`, fontSize: '24px' }}
                    // />
                    <BugIcon style={{ fill: THEME.token?.colorIcon, maxWidth: '24px' }} />
                  }
                  style={{ marginRight: 15 }}
                >
                  <Text>Report an Issue</Text>
                </ButtonFeedback>
              </FlexCenter>
              <FlexCenter
                onClick={e => {
                  e.stopPropagation();
                  callback(REPORT_TYPES.IDEA);
                }}
              >
                <ButtonFeedback
                  icon={
                    // <Icon
                    //   type="icon-ants-idea"
                    //   style={{ color: `${THEME.token?.colorIcon}`, fontSize: '24px' }}
                    // />
                    <RequestIcon style={{ fill: THEME.token?.colorIcon, maxWidth: '24px' }} />
                  }
                >
                  <Text>Request an Idea</Text>
                </ButtonFeedback>
              </FlexCenter>
            </FlexCenter>
          </SendFeedback>
        );
      }
      case REPORT_TYPES.IDEA:
      case REPORT_TYPES.ISSUE: {
        return (
          <ControlGroup size={20} direction="vertical">
            {/* {type === REPORT_TYPES.ISSUE && ( */}
            <ControlGroup size={5} direction="vertical">
              <ControlLabel>
                When you noticed this issue, what were you trying to do?{' '}
                <span style={{ color: THEME.token?.red8 }}>*</span>
              </ControlLabel>
              <Select
                defaultValue={appTargeting}
                value={appTargeting}
                style={{ width: '100%' }}
                onChange={(_, option) => setAppTargeting(option as AllAppOptionsProps)}
                options={allAppOptions}
              />
            </ControlGroup>
            {/* )} */}
            <ControlGroup size={5} direction="vertical">
              <ControlLabel>
                Title <span style={{ color: THEME.token?.red8 }}>*</span>
              </ControlLabel>
              <Input
                placeholder="Enter ticket title"
                value={valueInput.title}
                onChange={event => {
                  const { value } = event?.target;
                  event.stopPropagation();
                  setValueInput(prev => ({ ...prev, title: value }));
                }}
              />
              <ErrorMessage isShow={!isValidAll && !valueInput?.title}>
                *This field can&apos;t be empty
              </ErrorMessage>
            </ControlGroup>
            <ControlGroup size={5} direction="vertical">
              <ControlLabel>
                Message <span style={{ color: THEME.token?.red8 }}>*</span>
              </ControlLabel>
              <EditorWrapper>
                {/* <Editor
                  apiKey={apiKey || TINYMCE_API_KEY}
                  value={valueInput.message}
                  init={{
                    height: 200,
                    width: '100%',
                    max_height: 800,
                    max_width: 800,
                    menubar: false,
                    plugins: [
                      'advlist autolink lists link tinydrive image emoticons charmap print preview anchor',
                      'searchreplace visualblocks code fullscreen',
                      'insertdatetime media paste code help wordcount ', //
                    ],
                    toolbar:
                      'formatselect | bold italic underline strikethrough code | image emoticons | \
                                  forecolor backcolor preview link |\
                                  alignleft aligncenter alignright alignjustify outdent indent |\
                                  numlist bullist checklist undo redo',
                    tinydrive_token_provider: `//${modifiedDomain}/hub/thirdparty-services/v2.0/tinymce?portalId=${portalId}&token=${token}`,
                    skin: 'snow',
                    toolbar_mode: 'sliding',
                    content_css: false,
                    branding: false,
                    resize: false,
                    statusbar: false,
                    setup(editor) {
                      editor.on('init', e => {
                        editor.getBody().style.fontSize = `${THEME.token?.fontSize}px`;
                      });
                    },
                    placeholder: 'Enter your comment...',
                    entity_encoding: 'raw',
                    paste_data_images: true,
                  }}
                  // disabled={!!props.disabled}
                  // outputFormat='text'
                  onEditorChange={content =>
                    setValueInput(prevVal => ({
                      ...prevVal,
                      message: content,
                    }))
                  }
                /> */}
                <QuillEditor
                  value={valueInput.message}
                  uploadService={Service.tickets.callApi.uploadImg({
                    domain: domainUpload,
                    token,
                    userId,
                  })}
                  onChange={content => {
                    setValueInput(prevVal => ({
                      ...prevVal,
                      message: content,
                    }));
                    setIsLoadingUpload(content.includes('<img src="data:image/png;base64'));
                  }}
                  callback={callbackEditor}
                  placeholder="Enter your comment..."
                  borderColor="#e6e6e6"
                  isRoundCorner={false}
                  height={159}
                />
                <div>
                  {valueInput.files?.length > 0 && (
                    <WrapperLinkFiles>
                      {valueInput.files?.map((file, index) => (
                        <WrapperLinkItemFiles key={file?.token}>
                          <span
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '10px',
                            }}
                          >
                            <Icon
                              type="icon-ants-hyperlink"
                              style={{
                                color: THEME.token?.colorPrimary,
                                fontSize: '24px',
                              }}
                            >
                              link
                            </Icon>
                            <Text
                              style={{
                                color: '#000',
                                fontSize: `${THEME.token?.fontSize}px`,
                              }}
                            >
                              {file?.file_name}
                            </Text>
                          </span>
                          <Icon
                            type="icon-ants-remove-slim"
                            style={{
                              color: THEME.token?.colorIcon,
                              fontSize: '12px',
                              cursor: 'pointer',
                            }}
                            onClick={() => handleRemoveFileV2(index)}
                          >
                            clear
                          </Icon>
                        </WrapperLinkItemFiles>
                      ))}
                    </WrapperLinkFiles>
                  )}
                  <WrapperIconEditor borderTop={Boolean(valueInput.files?.length)}>
                    <WrapperInputFile>
                      <label htmlFor={`fileImage-${fileInputKey}`} className="upload-wrapper-label">
                        <Icon type="icon-ants-attachment" className="upload-icon" />
                      </label>
                      <input
                        key={fileInputKey}
                        type="file"
                        style={{ position: 'absolute', top: 0, right: 0, display: 'none' }}
                        name={`fileImage-${fileInputKey}`}
                        id={`fileImage-${fileInputKey}`}
                        onChange={handleOnchangeFile}
                      />
                    </WrapperInputFile>
                  </WrapperIconEditor>
                </div>
              </EditorWrapper>
              {errFile.isError ? (
                <ErrorMessage isShow={errFile.isError}>{errFile.message}</ErrorMessage>
              ) : (
                <ErrorMessage isShow={!isValidAll && !valueInput?.message}>
                  *This field can&apos;t be empty
                </ErrorMessage>
              )}
            </ControlGroup>
            <ControlGroup size={10} direction="vertical">
              <ControlLabel>Attached Screenshot/ Video Recorded</ControlLabel>
              <FlexCenter
                style={{
                  justifyContent: 'flex-start',
                  alignItems: 'flex-start',
                  gap: 10,
                }}
              >
                {renderAttachActions()}
              </FlexCenter>
            </ControlGroup>
          </ControlGroup>
        );
      }
      case REPORT_TYPES.HELP: {
        return (
          <Wrapper>
            <WrapperSearch placeholder="Search..." onChange={onChange} />
            <div style={{ padding: '13px 15px' }}>
              <LabelTitle>Popular Help Resources</LabelTitle>
              {isLoadingListHelp && (
                <WrapperLoading style={{ background: 'none' }} height="80%" width="100%">
                  <Spin indicator={antIcon} />
                </WrapperLoading>
              )}
              {treeData.length > 0 &&
                treeData.map(item => (
                  <WrapperContentHelp>
                    <TreeContent
                      onSelect={onSelect}
                      onExpand={onExpand}
                      treeData={[item]}
                      expandedKeys={expandedKeys}
                      autoExpandParent={autoExpandParent}
                    />
                  </WrapperContentHelp>
                ))}
            </div>
          </Wrapper>
        );
      }
      case REPORT_TYPES.CHAT: {
        return (
          <ChatBox
            domain={domainTicket}
            userId={userId}
            token={token}
            avatar={avatar}
            style={{
              height: 'calc(100% - 50px)',
            }}
            withoutBox
          />
        );
      }
      case REPORT_TYPES.HELP_V1: {
        return (
          <Wrapper>
            <IframeHelp src={pathHelp} />
          </Wrapper>
        );
      }
      default: {
        return null;
      }
    }
  };

  const isDisableButtonSend = useMemo(
    () => !valueInput?.title || !valueInput?.message || isLoadingUpload,
    [valueInput, isLoadingUpload],
  );

  const callbackEditor = (type: TCallBack, value: string | boolean) => {
    switch (type) {
      case TCallBack.LOADING_UPLOAD:
        setIsLoadingUpload(value as boolean);
        break;
      case TCallBack.ERROR: {
        switch (value) {
          case 'FILE_TOO_LARGE':
            setErrFile({
              isError: true,
              message: '*Maximum upload file size: 10MB',
            });
            break;
          case 'RESET':
            setErrFile({
              isError: false,
              message: '',
            });
            break;
          default:
            break;
        }
        break;
      }
      default:
        break;
    }
  };

  const renderPopupLayout = (type: ReportValueType): React.ReactNode => (
    <>
      <Loading isLoading={isMainLoading || isFetchingUsers} width="100%" height="100%" />
      <WrapperHeader className="cursor">
        <Header>{title}</Header>
      </WrapperHeader>
      <WrapperBody
        className="popup-content"
        style={{
          padding:
            type === REPORT_TYPES.CHAT ||
            type === REPORT_TYPES.HELP ||
            type === REPORT_TYPES.HELP_V1
              ? '0'
              : '13px 15px',
        }}
      >
        {renderContentPopup(type)}
      </WrapperBody>
      {![
        REPORT_TYPES.FEEDBACK,
        REPORT_TYPES.HELP,
        REPORT_TYPES.CHAT,
        REPORT_TYPES.HELP_V1,
      ].includes(type) && (
        <WrapperFooter>
          <Button
            type="primary"
            style={{ marginRight: 15, fontWeight: 'bold' }}
            onClick={() => handleSubmit()}
            disabled={isDisableButtonSend}
          >
            Send
          </Button>
          <Button onClick={() => callback('ON_CLOSE_POPUP')}>Cancel</Button>
        </WrapperFooter>
      )}
      {type === REPORT_TYPES.HELP_V1 && (
        <WrapperFooterSpace>
          <Button
            type="primary"
            style={{ marginRight: 15, fontWeight: 'bold' }}
            onClick={() => callback('ON_BACK_POPUP')}
          >
            BACK
          </Button>
          <Link
            target="_blank"
            href={pathHelp}
            rel="noreferrer"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 7,
            }}
          >
            Open in new tab
            <OpenUrlIcon
              style={{
                fill: THEME.token?.colorPrimary,
                maxWidth: 18,
                maxHeight: 18,
              }}
            />
          </Link>
        </WrapperFooterSpace>
      )}
    </>
  );

  return (
    <>
      <Dropdown
        menu={{
          style: { minWidth: 250, padding: 0 },
          items: items as any,
          onClick: info => handleClick(info as MenuItemTypeProps),
        }}
        // onOpenChange={isOpen => setIsOpenDropdown(isOpen)}
        trigger={triggerType}
        placement="bottomRight"
      >
        {children || (
          <IconButtonStyled {...buttonProps}>
            <Icon type="icon-ants-help" />
          </IconButtonStyled>
        )}
      </Dropdown>
      {isShowPopup &&
        createPortal(
          <PopupDraggable
            isHiddenResizing
            bounds={boundsDraggable}
            isShowResizeHover={isShowResizeHover}
            callback={callback}
            styleContainer={{
              display: 'flex',
              flexDirection: 'column',
              textAlign: 'left',
            }}
          >
            {renderPopupLayout(type)}
          </PopupDraggable>,
          document.body,
          PORTALS_ANTSOMI_PACKAGE_UI_KEY_POPUP,
        )}
      {isOpenImageDrawer &&
        createPortal(
          <CaptureScreen
            recorderConfigs={{
              recorder: recorderRef.current,
              streamTracks: streamTracks.current,
              isMute,
            }}
            defaultPositions={{
              [ATTACH_KEYS.CAPTURE]: defaultPositionDrawActions,
              [ATTACH_KEYS.RECORD]: defaultPositionRecordActions,
            }}
            captureType={captureType}
            src={urlImageDrawer}
            callback={callback}
          />,
          document.body,
          PORTALS_ANTSOMI_PACKAGE_UI_KEY,
        )}
      {/* <Modal // Hiện tại dùng Prompt mặc định của Screen Capture API, không dùng Modal custom này
        open={open}
        closable={false}
        title={<Header style={{ display: 'block', padding: 15 }}>Choose what to share</Header>}
        onOk={handleCaptureScreen}
        onCancel={handleCancel}
        footer={
          <WrapperFooter style={{ position: 'unset', padding: 15, textAlign: 'left' }}>
            <Button
              type="primary"
              style={{ marginRight: 7, fontWeight: 'bold' }}
              // onClick={handleCaptureScreen}
              onClick={() => {
                if (captureType === ATTACH_KEYS.CAPTURE) {
                  handleCaptureScreen();
                } else {
                  handleCreateRecorder();
                }
              }}
            >
              Share
            </Button>
            <Button key="back" onClick={handleCancel}>
              Cancel
            </Button>
          </WrapperFooter>
        }
        style={{ minWidth: 630 }}
      >
        <WrapperBodyModal>
          <Text>cdp.antsomi.com wants to share the contents of your screen.</Text>
          <Tabs size="middle" tabPosition="top" items={TABS_SHARING_SCREEN} />
        </WrapperBodyModal>
      </Modal> */}
    </>
  );
};

Help.defaultProps = {
  triggerType: ['click'],
  boundsDraggable: 'body',
  isShowResizeHover: true,
  configs: {
    apiKey: TINYMCE_API_KEY,
    domain: '//sandbox-app.cdp.asia',
    domainTicket: 'https://sandbox-issue.antsomi.com',
    domainUpload: 'https://sandbox-media-template.antsomi.com/cdp',
    portalId: '33167',
    userId: '1600080515',
    token: '5474r2x214z254a4u2a4y4m503w5p2r5a4s2g4x2l5e4',
    domainPlatform: 'https://platform.ants.tech',
    appCode: 'APP_API_HUB',
    avatar: 'https://c0-platform.ants.tech/avatar/2021/09/20/bldjzfbz33.png',
    config: {
      p_timezone: 'Asia/Hong_Kong',
      api_pid: 33167,
      p_f_longdatetime: "dd MMMM 'at' HH:mm:ss",
      user_language: 'en',
      embeddedData: {},
      INSIGHT_U_OGS: 'uogs',
    },
  },
  children: '',
};
export { Help };
