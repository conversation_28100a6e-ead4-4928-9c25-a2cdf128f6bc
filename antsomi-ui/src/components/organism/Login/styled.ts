import { Button, Flex, Form, Input } from 'antd';
import styled from 'styled-components';

export const LoginMain = styled(Flex)`
  background: #eaf1fb;

  height: 100vh;
  max-height: 100vh;

  width: 100%;

  overflow: hidden;
`;

export const LoginBannerZone = styled.div`
  display: flex;
  flex: 2;
  z-index: 1;

  min-width: calc(100vw - 404px);
  max-width: calc(100vw - 300px);
`;

export const WrapperLogin = styled(Flex)`
  height: 100%;
  overflow-y: auto;
  background-color: white;
  z-index: 5;

  width: ${props => (props.isMobile ? '100%' : '')};
  min-width: ${props => (props.isMobile ? '100%' : '300px')};
  max-width: ${props => (props.isMobile ? '100%' : '404px')};

  ${(props: { isMobile: boolean }) =>
    !props.isMobile ? 'box-shadow: 3px 0px 9px 0px #002e5926' : ''}
`;

export const WrapperImage = styled.img`
  width: 180px;
  height: 40px;
  object-fit: contain;
`;

export const Label = styled.span`
  color: #005eb8;
  text-align: center;
  font-weight: 500;

  :hover {
    cursor: pointer;
  }

  @media (max-width: 768px) {
    color: #000000;
  }
`;

export const Image = styled.img``;

export const WrapperContent = styled.span`
  font-size: 14px;
  color: #595959;

  &.validate {
    text-align: center;
  }

  &.forgot-password {
  }

  &.verify_account {
    text-align: center;
    color: #595959;
    font-size: 14px;
  }
`;

export const AvatarImage = styled.img`
  border-radius: 50%;
  width: 80px;
  height: 80px;
`;

export const StyledForm = styled(Form)`
  input {
    height: ${(props: { isMobile: boolean }) => (props.isMobile ? ' 44px' : '36px')};
    font-size: ${(props: { isMobile: boolean }) =>
      props.isMobile ? ' 16px !important' : '14px !important'};

    &::placeholder {
      font-size: ${(props: { isMobile: boolean }) =>
        props.isMobile ? ' 16px !important' : '14px !important'};
    }
  }

  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-background-clip: text;
    transition: background-color 200s ease-in-out 0s;
  }

  .antsomi-input-password-icon {
    font-size: ${(props: { isMobile: boolean }) => (props.isMobile ? ' 24px' : '14px')};

    &:hover {
      cursor: pointer;
    }
  }

  .antsomi-input-affix-wrapper {
    padding: 0 10px !important;
    font-size: ${(props: { isMobile: boolean }) =>
      props.isMobile ? ' 16px !important' : '14px !important'};

    &::placeholder {
      font-size: ${(props: { isMobile: boolean }) =>
        props.isMobile ? ' 16px !important' : '14px !important'};
    }
  }

  .antsomi-form-item-explain-error {
    font-size: 11px !important;
  }
`;

export const StyleInput = styled(Input)`
  height: ${(props: { isMobile: boolean }) =>
    props.isMobile ? ' 44px !important' : '36px !important'};
  font-size: ${(props: { isMobile: boolean }) =>
    props.isMobile ? ' 16px !important' : '14px !important'};

  &::placeholder {
    font-size: ${(props: { isMobile: boolean }) =>
      props.isMobile ? ' 16px !important' : '14px !important'};
  }
`;

export const StyleButton = styled(Button)`
  height: ${(props: { isMobile: boolean }) =>
    props.isMobile ? ' 44px !important' : '40px !important'};
  font-size: ${(props: { isMobile: boolean }) =>
    props.isMobile ? ' 16px !important' : '14px !important'};
`;

export const Text = styled.div`
  color: ${(props: { color?: string; isMobile: boolean }) =>
    props.color ? props.color : ' #000000'};
  font-size: ${(props: { isMobile: boolean }) => (props.isMobile ? ' 14px !important' : '12px')};
`;
