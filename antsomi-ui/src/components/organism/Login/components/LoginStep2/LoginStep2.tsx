/* eslint-disable react-hooks/exhaustive-deps */

// Libraries
import { Button, Checkbox, Flex, Typography } from 'antd';
import { get } from 'lodash';
import React, { useEffect, useRef } from 'react';
import { useImmer } from 'use-immer';
import { useCookies } from 'react-cookie';
import { isMobile } from 'react-device-detect';

// Components
import { WidgetLayout } from '../WidgetLayout';
import { Avatar } from '../Avatar';
import { IconInfo } from '../IconInfo';

// Images
import loginStep2Image from '../../../../../assets/images/components/Login/loginStep2.png';
import step2 from '../../../../../assets/images/components/Login/step2.png';

import { LoginData } from '../../types';

import { StyleButton, StyleInput, Text } from '../../styled';
import { TextInfo } from './styled';
import { Title } from '../WidgetLayout/styled';

interface TLoginStep2 {
  domainLogin: string;
  codeKey?: string;
  token?: string;
  username?: string;
  onLoginSuccess?: (data: Partial<LoginData>) => void;
  onLoginFail?: () => void;
  backLogin?: () => void;
  loginData?: Partial<LoginData>;
}

const { Text: TextAntsomiUI } = Typography;

export const LoginStep2: React.FC<TLoginStep2> = props => {
  const {
    loginData,
    domainLogin,
    codeKey = 'code',
    token = '',
    username = '',
    onLoginSuccess,
    onLoginFail,
    backLogin,
  } = props;

  const [state, setState] = useImmer({
    code: '',
    isTrust: false,
    codeError: true,
    isLoading: false,
    count: 0,
  });

  const errorRef = useRef(false);

  const [cookies, setCookie] = useCookies();

  const signIn = () => {
    errorRef.current = true;
    setState(draft => {
      draft.count += 1;
    });
    if (!token || state.codeError) {
      return;
    }

    const verificationParams = {
      [codeKey]: state.code,
      token,
      trust_key: state.isTrust ? generateGuid() : null,
    };

    const url = `${domainLogin}api/account/authenticate`;

    const data = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(verificationParams),
    };

    setState(draft => {
      draft.isLoading = true;
    });
    const request = new XMLHttpRequest();
    request.open('POST', url, true);
    request.setRequestHeader('Content-Type', 'application/json');
    request.withCredentials = false;

    request.onreadystatechange = () => {
      if (request.readyState === XMLHttpRequest.DONE) {
        setState(draft => {
          draft.isLoading = false;
        });
      }
      if (request.readyState === XMLHttpRequest.DONE && request.status === 200) {
        const response = JSON.parse(request.responseText);

        if (response.code === 200) {
          if (verificationParams.trust_key) {
            const trustKeyCookieName = `ogs-${btoa(username)}`;
            localStorage.setItem(trustKeyCookieName, verificationParams.trust_key);
            setCookie(trustKeyCookieName, verificationParams.trust_key, { path: '/' });
          }
          if (onLoginSuccess && typeof onLoginSuccess === 'function') {
            onLoginSuccess(response.data);
          }
        } else {
          setState(draft => {
            draft.codeError = true;
          });
          if (onLoginFail && typeof onLoginFail === 'function') {
            onLoginFail();
          }
        }
      }
    };

    request.send(JSON.stringify(verificationParams));
  };

  const generateGuid = () => {
    function s4() {
      return Math.floor((1 + Math.random()) * 0x10000)
        .toString(16)
        .substring(1);
    }

    return `${s4() + s4()}-${s4()}-${s4()}-${s4()}-${s4()}${s4()}${s4()}`;
  };

  const handleBack = () => {
    if (backLogin && typeof backLogin === 'function') {
      backLogin();
    }
  };

  useEffect(() => {
    if (state.code && state.code.trim()) {
      setState(draft => {
        draft.codeError = false;
      });
    } else {
      setState(draft => {
        draft.codeError = true;
      });
    }
  }, [state.code]);

  return (
    <WidgetLayout
      header={
        <Avatar
          image={get(loginData, 'personal.avatar')}
          name={get(loginData, 'personal.full_name')}
          email={get(loginData, 'personal.email')}
        />
      }
      isShowLogo={false}
      title={
        <Title isMobile={isMobile} style={{ marginTop: 20 }}>
          Identity Verification
        </Title>
      }
      gapContent={30}
      content={
        <Flex vertical gap={50} style={{ marginTop: 20 }}>
          <Flex vertical gap={20} align="center">
            <img src={loginStep2Image} alt="" />
            <Text isMobile={isMobile} color="#595959">
              For added security, we need to verify your identity.
            </Text>
          </Flex>
          <Flex vertical gap={isMobile ? 10 : 13}>
            <Flex gap={5} align="center">
              <Text isMobile={isMobile} color="#595959">
                Please enter the 6-digit code registered to your device.
              </Text>
              <IconInfo
                image={step2}
                title={
                  <TextAntsomiUI
                    style={{
                      marginTop: 20,
                      textAlign: 'center',
                    }}
                  >
                    <Text isMobile={isMobile}>
                      Open Google Authenticator and enter the code from <b>antsomi.com</b>
                    </Text>
                  </TextAntsomiUI>
                }
              />
            </Flex>
            <Flex vertical gap={isMobile ? 20 : 11}>
              <Flex vertical gap={10}>
                <StyleInput
                  isMobile={isMobile}
                  maxLength={6}
                  placeholder="Input your code"
                  value={state.code}
                  onChange={e => {
                    setState(draft => {
                      draft.code = e.target.value;
                    });
                    errorRef.current = true;
                  }}
                  status={errorRef.current && state.codeError ? 'error' : undefined}
                />
                {errorRef.current && state.codeError ? (
                  <TextAntsomiUI type="danger">Invalid code, please try again</TextAntsomiUI>
                ) : null}
              </Flex>
              <Checkbox
                checked={state.isTrust}
                onChange={e => {
                  setState(draft => {
                    draft.isTrust = e.target.checked;
                  });
                }}
              >
                <TextInfo isMobile={isMobile}>Remember this browser for future logins</TextInfo>
              </Checkbox>
            </Flex>
          </Flex>
        </Flex>
      }
      footer={
        <Flex vertical gap={115}>
          <Flex vertical gap={isMobile ? 30 : 15}>
            <StyleButton isMobile={isMobile} type="primary" onClick={signIn}>
              Verify
            </StyleButton>

            <StyleButton isMobile={isMobile} onClick={handleBack} type="text">
              Back to login
            </StyleButton>
          </Flex>
          <Flex justify="center">
            <Button
              type="text"
              onClick={handleBack}
              style={{
                fontSize: isMobile ? '16px' : '14px',
              }}
            >
              Sign in with a different account
            </Button>
          </Flex>
        </Flex>
      }
    />
  );
};
