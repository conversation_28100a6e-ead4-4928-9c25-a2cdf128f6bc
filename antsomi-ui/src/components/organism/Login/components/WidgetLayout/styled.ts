import { Flex } from 'antd';
import styled from 'styled-components';

export const Title = styled.div`
  font-size: ${(props: { isMobile: boolean }) => (props.isMobile ? '20px' : '18px')};
  font-weight: 700;
  color: #005eb8;
  text-align: center;
`;

export const WidgetLayoutMain = styled(Flex)`
  position: relative;

  width: 100%;
  height: 100%;
  overflow: auto;

  .sticky-footer {
    margin-top: 50px;
    bottom: 0px;
    width: 100%;
    z-index: 100;
  }
`;
