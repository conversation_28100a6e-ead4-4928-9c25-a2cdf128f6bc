// Libraries
import React, { memo, useEffect, useLayoutEffect, useState } from 'react';
import { get, isArray, isEmpty, isNull, set } from 'lodash';

// Locale
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';

// Atoms
import { Button } from '@antscorp/antsomi-ui/es/components/atoms';

// Components
import Content from './Content';

// Styled
import { ContentContainer } from './styled';

// Constants
import {
  ARTICLE_RANKING_DEFAULT,
  GET_TOP_RANKING_DEFAULT,
  MAX_NUM_OF_CONTENT_SOURCE,
} from './constants';
import {
  FILTERS_DEFAULT,
  ITEM_TYPE_NAME,
  PRODUCT_ITEM_TYPE_ID,
  PRODUCT_RANKING_DEFAULT,
} from '@antscorp/antsomi-ui/es/constants/contentSource';

// Queries
import { useGetListBO } from '@antscorp/antsomi-ui/es/queries/BusinessObject';
import { useGetListFallbackBO } from '@antscorp/antsomi-ui/es/queries/ThirdParty';
import { getDataTableBOQuery } from '@antscorp/antsomi-ui/es/queries/BusinessObject/useGetDataTableBO';

// Hooks
import { useDeepCompareEffect } from '@antscorp/antsomi-ui/es/hooks';
import { useDebounce } from '@antscorp/antsomi-ui/es/hooks/useDebounceV2';

// Types
import type { TServiceAuth } from '@antscorp/antsomi-ui/es/types';
import type {
  ContentSourceModeView,
  HideOptionTypes,
  JourneySettings,
  TContentSourceSettings,
  TRanking,
  UpdateGroupAction,
} from './types';

// Provider
import {
  useContentSourceDispatch,
  useSelectBoIdByGroupFromCS,
  useSelectContentSources,
  useSelectModeView,
} from './provider';
import {
  deleteContentSourceGroup,
  setContentSourceGroup,
  setContentSourceGroupBOData,
  updateContentSourceSettings,
  updateModeView,
  emitItemTypeChange,
} from './reducer';

// Utils
import { handleError } from '@antscorp/antsomi-ui/es/utils';

interface SettingsProps {
  serviceAuth: TServiceAuth;
  mode: ContentSourceModeView;
  justOneGroup?: boolean;
  applyFor?: string;
  hideOptionTypes?: HideOptionTypes;
  onChange: (contentSource: TContentSourceSettings) => void;
  initValue?: TContentSourceSettings;
  isShowErrorAlert: boolean;
  journeySettings: JourneySettings;
}

const PATH = 'src/components/organism/ContentSources/Settings.tsx';

const Settings = (props: SettingsProps) => {
  const {
    serviceAuth,
    mode,
    isShowErrorAlert,
    initValue,
    justOneGroup = false,
    journeySettings,
    hideOptionTypes,
    applyFor,
    onChange,
  } = props;
  const { url = '', userId, token, accountId } = serviceAuth;

  const { t } = i18nInstance;
  const boIdByGroupCS = useSelectBoIdByGroupFromCS();
  const contentSources = useSelectContentSources();
  const { groups, expanded } = contentSources;
  const modeView = useSelectModeView();
  const isCDP = modeView === 'CDP';

  const [isInitDataCDPDone, setIsInitDataCDPDone] = useState(false);
  const [isLoadingDataTableBO, setIsLoadingDataTableBO] = useState(false);
  const contentSourcesDebounce = useDebounce(contentSources, 400);

  const {
    data: listBO,
    isLoading: isLoadingListBO,
    refetch: refetchListBO,
  } = useGetListBO(
    {
      url,
    },
    {
      token,
      userId,
      accountId,
    },
    {
      onSuccess(data) {
        if (
          !isInitDataCDPDone &&
          (isCDP || justOneGroup) &&
          isArray(data) &&
          contentSources.groups.length === 1 &&
          contentSources.groups[0] &&
          !contentSources.groups[0].itemTypeId
        ) {
          const BOProduct = data.find(bo => +bo.id === PRODUCT_ITEM_TYPE_ID) || {};
          const groupId = get(contentSources, 'groups[0].groupId', '');
          const itemTypeId = BOProduct?.id;
          const itemTypeDisplay = BOProduct?.label;
          const itemTypeName = BOProduct?.name;
          let valueRanking: TRanking = GET_TOP_RANKING_DEFAULT;

          if (itemTypeId === PRODUCT_ITEM_TYPE_ID) {
            valueRanking = PRODUCT_RANKING_DEFAULT;

            if (isCDP || justOneGroup) {
              set(valueRanking, 'algorithms.sort', 'order');
            }
          } else if (itemTypeName === ITEM_TYPE_NAME.ARTICLE) {
            valueRanking = ARTICLE_RANKING_DEFAULT;
          }

          const newRanking: TRanking = valueRanking;

          if (groupId && itemTypeId) {
            // eslint-disable-next-line @typescript-eslint/no-use-before-define
            contentSourceDispatch({
              type: 'SET_CONTENT_SOURCE_GROUP',
              payload: {
                groupId,
                values: {
                  itemTypeId,
                  itemTypeDisplay,
                  itemTypeName,
                  ranking: newRanking,
                  filters: FILTERS_DEFAULT,
                },
              },
            });
            setIsInitDataCDPDone(true);
          }
        }
      },
    },
  );
  const { data: listFallbackBO } = useGetListFallbackBO({
    serviceAuth,
  });

  const contentSourceDispatch = useContentSourceDispatch();

  const handleChangeItemTypeId = ({
    groupId,
    id,
    callback,
  }: {
    groupId: string;
    id: number | null;
    isDelete?: boolean;
    callback?: () => void;
  }) => {
    try {
      const selected = listBO?.find((bo: any) => bo.value === id);
      const itemTypeDisplay = selected?.label;
      const itemTypeName = selected?.name;
      let valueRanking: TRanking = GET_TOP_RANKING_DEFAULT;

      if (id === PRODUCT_ITEM_TYPE_ID) {
        valueRanking = PRODUCT_RANKING_DEFAULT;

        if (isCDP || justOneGroup) {
          set(valueRanking, 'algorithms.sort', 'order');
        }
      } else if (itemTypeName === ITEM_TYPE_NAME.ARTICLE) {
        valueRanking = ARTICLE_RANKING_DEFAULT;
      }

      const newRanking: TRanking = valueRanking;

      if (groupId) {
        contentSourceDispatch({
          type: 'SET_CONTENT_SOURCE_GROUP',
          payload: {
            groupId,
            values: {
              itemTypeId: id,
              itemTypeDisplay,
              itemTypeName,
              ranking: newRanking,
              filters: FILTERS_DEFAULT,
            },
          },
        });

        if (callback) callback();
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleChangeItemTypeId',
        args: {},
      });
    }
  };

  const handleChangeContentSourceGroup = (groupId: string, action: UpdateGroupAction) => {
    try {
      switch (action.type) {
        case 'BO_TYPE': {
          const { itemTypeId } = action.payload;
          if (itemTypeId) {
            handleChangeItemTypeId({ groupId, id: itemTypeId });

            contentSourceDispatch(emitItemTypeChange());
          }
          break;
        }
        case 'ALGORITHMS': {
          const { ranking } = action.payload;

          contentSourceDispatch(setContentSourceGroup({ groupId, values: { ranking } }));
          break;
        }
        case 'FILTER': {
          const { conditions } = action.payload;
          contentSourceDispatch(
            setContentSourceGroup({ groupId, values: { filters: conditions } }),
          );

          break;
        }
        case 'FALLBACK': {
          const { fallback } = action.payload;

          if (fallback) {
            contentSourceDispatch(setContentSourceGroup({ groupId, values: { fallback } }));
          }

          break;
        }
        case 'NAME': {
          const { name } = action.payload;
          contentSourceDispatch(setContentSourceGroup({ groupId, values: { groupName: name } }));

          break;
        }
        case 'LEVEL': {
          const { level } = action.payload;
          contentSourceDispatch(setContentSourceGroup({ groupId, values: { level } }));

          break;
        }
        default: {
          throw new Error('Invalid action type');
        }
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleChangeContentSourceGroup',
        args: {
          groupId,
          action,
        },
      });
    }
  };

  const handleDeleteContentSourceGroup = (groupId: string) => {
    const group = contentSources.groups.find(g => g.groupId === groupId);

    if (group)
      // Re-use function in case change content source type
      // for case remove content source
      handleChangeItemTypeId({
        groupId,
        id: group.itemTypeId,
        isDelete: true,
        callback: () => contentSourceDispatch(deleteContentSourceGroup(groupId)),
      });
  };

  useEffect(() => {
    contentSourceDispatch({
      type: 'SERVICE_AUTH',
      payload: {
        serviceAuth,
      },
    });
  }, [serviceAuth, contentSourceDispatch]);

  useLayoutEffect(() => {
    if (mode) {
      contentSourceDispatch(updateModeView(mode));
    }
  }, [mode, contentSourceDispatch]);

  // INIT group in case using CDP mode
  useLayoutEffect(() => {
    if ((isCDP || justOneGroup) && groups.length === 0) {
      contentSourceDispatch({
        type: 'ADD_CONTENT_SOURCE',
        payload: {},
      });
    }
  }, [isCDP, justOneGroup, contentSourceDispatch, groups.length]);

  // init data
  useDeepCompareEffect(() => {
    if (!isEmpty(initValue)) {
      contentSourceDispatch(updateContentSourceSettings(initValue));

      if (isCDP || justOneGroup) {
        setIsInitDataCDPDone(true);
      }
    } else {
      if (isCDP || justOneGroup) {
        refetchListBO();
        setIsInitDataCDPDone(false);
      }
    }
  }, [initValue, contentSourceDispatch, isCDP, justOneGroup]);

  useDeepCompareEffect(() => {
    let ignore = false;

    if (!isEmpty(boIdByGroupCS) && !isEmpty(serviceAuth)) {
      const groupKeyList = Object.keys(boIdByGroupCS);

      const fetchListCSDataTableBO = async () => {
        const listPromises: Promise<any>[] = [];

        groupKeyList.forEach(groupId => {
          const itemTypeId = boIdByGroupCS[groupId];
          const promise = getDataTableBOQuery(serviceAuth, itemTypeId, { groupId });
          listPromises.push(promise);
        });

        setIsLoadingDataTableBO(true);
        const listBOResponses = await Promise.all(listPromises);
        const boDataByGroupId: Record<string, any[]> = {};

        listBOResponses.forEach(item => {
          const { data, params } = item;

          boDataByGroupId[params.groupId] = data.data;
        });

        if (!ignore) {
          contentSourceDispatch(setContentSourceGroupBOData(boDataByGroupId));
        }

        setIsLoadingDataTableBO(false);
      };

      fetchListCSDataTableBO();
    }

    return () => {
      ignore = true;
    };
  }, [boIdByGroupCS, serviceAuth, contentSourceDispatch]);

  // callback data out
  useDeepCompareEffect(() => {
    const groups = get(contentSourcesDebounce, 'groups', []);
    const isValidCallbackOut =
      groups.length > 0 && groups.every(group => !isNull(group.itemTypeId));

    if (typeof onChange === 'function' && isValidCallbackOut) {
      onChange(contentSourcesDebounce);
    }
  }, [contentSourcesDebounce]);

  return (
    <ContentContainer className="contentsource-container">
      <Content
        isLoadingListBO={isLoadingListBO || isLoadingDataTableBO}
        hideOptionTypes={hideOptionTypes}
        applyFor={applyFor}
        justOneGroup={justOneGroup}
        listBO={listBO}
        groups={groups}
        expanded={expanded}
        onChangeExpand={expanded => {
          contentSourceDispatch({
            type: 'EXPANDED',
            payload: {
              expanded,
            },
          });
        }}
        onChangeGroup={(groupId: string, action: UpdateGroupAction) => {
          handleChangeContentSourceGroup(groupId, action);
        }}
        onDeleteGroup={groupId => handleDeleteContentSourceGroup(groupId)}
        journeySettings={journeySettings}
        isShowErrorAlert={isShowErrorAlert}
        listFallbackBO={listFallbackBO || []}
      />
      {groups.length < MAX_NUM_OF_CONTENT_SOURCE && !isCDP && !justOneGroup && (
        <Button
          type="text"
          onClick={() =>
            contentSourceDispatch({
              type: 'ADD_CONTENT_SOURCE',
              payload: {},
            })
          }
        >
          + {t(translations.addGroup.title).toString()}
        </Button>
      )}
    </ContentContainer>
  );
};

export default memo(Settings);
