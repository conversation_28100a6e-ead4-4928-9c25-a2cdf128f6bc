// Libraries
import React from 'react';
import { StoryObj, Meta, StoryFn } from '@storybook/react';

// Components
import ContentSources from './index';

// Constants
import { JOURNEY_SETTINGS_DEFAULT } from './constants';

export default {
  title: 'Organisms/ContentSources',
  component: ContentSources,
  argTypes: {
    useQueryClient: {
      name: 'useQueryClient',
      description: 'Allows you to override the default query client',
      defaultValue: true,
      table: {
        type: {
          summary: 'boolean',
          defaultValue: {
            summary: true,
          },
        },
      },
      control: 'boolean',
    },
    serviceAuth: {
      name: 'serviceAuth',
      description: 'Allows you to override the default auth service credentials',
      defaultValue: {
        url: '',
        token: '',
        userId: '',
        accountId: '',
      },
      table: {
        type: {
          summary: 'TServiceAuth',
        },
        defaultValue: {
          summary: `{
            url: '',
            token: '',
            userId: '',
            accountId: '',
          }`,
        },
      },
      control: 'object',
    },
    isShowErrorAlert: {
      name: 'isShowErrorAlert',
      description: 'Allows you to display an error alert',
      defaultValue: false,
      table: {
        type: {
          summary: 'boolean',
        },
        defaultValue: {
          summary: false,
        },
      },
      control: 'boolean',
    },
    mode: {
      name: 'mode',
      description:
        'Allows you to override the default mode render for the content sources component (see: **CDP** or **MEDIA**)',
      defaultValue: 'CDP',
      table: {
        type: {
          summary: 'ContentSourceModeView',
        },
        defaultValue: {
          summary: 'CDP',
        },
      },
      control: 'select',
      options: ['CDP', 'MEDIA'],
    },
    initValue: {
      name: 'initValue',
      description: 'Allows you to set the initial value for the content sources component.',
      defaultValue: undefined,
      table: {
        type: {
          summary: 'TContentSourceSettings',
        },
        defaultValue: {
          summary: undefined,
        },
      },
      control: 'object',
    },
    journeySettings: {
      name: 'journeySettings',
      description: 'Allows you to set the journey settings for the content sources component.',
      defaultValue: JOURNEY_SETTINGS_DEFAULT,
      table: {
        type: {
          summary: 'JourneySettings',
        },
        defaultValue: {
          summary: `${JOURNEY_SETTINGS_DEFAULT}`,
        },
      },
      control: 'object',
    },
    onChange: {
      name: 'onChange',
      description: 'Allows you to set the callback function to handle the on change event.',
      defaultValue: () => {},
      table: {
        type: {
          summary: '(contentSource: TContentSourceSettings) => void',
        },
        defaultValue: {
          summary: '() => {}',
        },
      },
      control: '-',
    },
  },
  parameters: {
    docs: {
      description: {
        component: 'Add a content sources component to dynamic attribute data.',
      },
    },
  },
} as Meta<typeof ContentSources>;

const ContentSourcesStory: StoryFn<typeof ContentSources> = () => <ContentSources />;

export const Basic = {
  render: ContentSourcesStory,
};

export const UsingWithGroupMediaMode: StoryObj<typeof ContentSources> = {
  render: () => (
    <ContentSources
      mode="MEDIA"
      serviceAuth={{
        userId: **********,
        accountId: **********,
        token: '5474r2x214z254a4u2a4y4p5k4m5a436x2q2v2o47406',
        url: 'https://sandbox-media-template.antsomi.com/cdp/api/v1',
      }}
    />
  ),
  parameters: {
    docs: {
      description: {
        story: 'This story uses the **MEDIA** mode with the **GROUP** type.',
      },
    },
  },
};
