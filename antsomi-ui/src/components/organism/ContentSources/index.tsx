// Libraries
import React, { memo } from 'react';

// Query config
import {
  queryClientAntsomiUI,
  QueryClientProviderAntsomiUI,
} from '@antscorp/antsomi-ui/es/queries/configs';

// Components
import Settings from './Settings';

// Types
import { TServiceAuth } from '@antscorp/antsomi-ui/es/types';
import {
  ContentSourceModeView,
  HideOptionTypes,
  JourneySettings,
  TContentSourceSettings,
} from './types';

// Providers
import { ContentSourceProvider } from './provider';

// Constants
import { JOURNEY_SETTINGS_DEFAULT } from './constants';

export interface ContentSourceProps {
  useQueryClient: boolean;
  serviceAuth: TServiceAuth;
  isShowErrorAlert: boolean;
  justOneGroup?: boolean;
  applyFor?: string;
  hideOptionTypes?: HideOptionTypes;
  mode: ContentSourceModeView;
  initValue?: TContentSourceSettings;
  onChange: (contentSource: TContentSourceSettings) => void;
  journeySettings: JourneySettings;
}

const defaultProps: ContentSourceProps = {
  useQueryClient: true,
  isShowErrorAlert: false,
  justOneGroup: false,
  hideOptionTypes: {
    filter: false,
    level: false,
    ranking: false,
  },
  journeySettings: JOURNEY_SETTINGS_DEFAULT,
  mode: 'CDP',
  onChange: () => {},
  serviceAuth: {
    url: '',
    token: '',
    userId: '',
    accountId: '',
  },
};

const ContentSource = (props: ContentSourceProps) => {
  const {
    useQueryClient,
    serviceAuth,
    mode,
    isShowErrorAlert,
    applyFor,
    justOneGroup,
    initValue,
    hideOptionTypes,
    journeySettings,
    onChange,
  } = props;

  if (!useQueryClient)
    return (
      <Settings
        mode={mode}
        applyFor={applyFor}
        initValue={initValue}
        hideOptionTypes={hideOptionTypes}
        justOneGroup={justOneGroup}
        serviceAuth={serviceAuth}
        isShowErrorAlert={isShowErrorAlert}
        journeySettings={journeySettings}
        onChange={onChange}
      />
    );
  return (
    <QueryClientProviderAntsomiUI client={queryClientAntsomiUI}>
      <ContentSourceProvider>
        <Settings
          mode={mode}
          initValue={initValue}
          applyFor={applyFor}
          justOneGroup={justOneGroup}
          hideOptionTypes={hideOptionTypes}
          serviceAuth={serviceAuth}
          isShowErrorAlert={isShowErrorAlert}
          journeySettings={journeySettings}
          onChange={onChange}
        />
      </ContentSourceProvider>
    </QueryClientProviderAntsomiUI>
  );
};

ContentSource.defaultProps = defaultProps;
export default memo(ContentSource);
