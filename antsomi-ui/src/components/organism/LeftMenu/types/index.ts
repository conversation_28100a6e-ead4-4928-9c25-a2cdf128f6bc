// Types
import { TFeatureMenu } from '@antscorp/antsomi-ui/es/models/LeftMenu';

export type TMenuItem = Omit<Partial<TFeatureMenu>, 'children' | keyof TRequireMenuItemKey> & {
  key: string;
  label: string;
  icon: string | null;
  disabled?: boolean;
  children?: TMenuItem[];
  style?: React.CSSProperties;
} & TOption;

export type TMenuFeatureItem = Omit<Partial<TFeatureMenu>, 'children'> &
  TRequireMenuItemKey &
  TOption & { children?: TMenuFeatureItem[] };

type TRequireMenuItemKey = {
  menu_item_code: string;
  menu_item_name: string;
  icon_name: string | null;
};

type TOption = {
  logo_url?: string | null;
  options?: { key: string; label: string }[];
  optionCallback?: (args: { menuItemKey: string; optionKey: string }) => void;
};

export type TLeftMenuType = 'customization' | 'recommendation' | 'default';
