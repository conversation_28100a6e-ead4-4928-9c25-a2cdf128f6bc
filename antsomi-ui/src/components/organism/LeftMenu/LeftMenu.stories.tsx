// Libraries
import React, { useState } from 'react';
import { StoryObj, Meta, StoryFn } from '@storybook/react';

// Components
import { LeftMenu } from '.';
import { MenuMapping } from '../../template/Layout/components/RecommendationWorkspace/components';

// Types
import { AppConfigProviderProps } from '../../..';

// Constants
import { CUSTOM_MENU_ITEMS } from './constants';
import { DASHBOARD_MODULE_CONFIG } from '../../template/Layout/constants';

const appConfig: AppConfigProviderProps = {
  env: 'development',
  auth: {
    token: '5474r2x214z294b423a4y4j574j5i4t5i4x2t4f4l5q5',
    portalId: 33167,
    userId: '**********',
    accountId: '**********',
  },
  languageCode: 'en',
};

export default {
  title: 'Organisms/LeftMenu',
  component: LeftMenu,
  argTypes: {
    appConfig: {
      name: 'appConfig',
      defaultValue: appConfig,
      description: 'App configuration',
      table: {
        type: { summary: 'AppConfigProviderProps' },
        defaultValue: {
          summary: `${appConfig}`,
        },
      },
      control: 'object',
    },
    show: {
      name: 'show',
      defaultValue: true,
      description: 'Handle show left menu',
      table: {
        type: { summary: 'show' },
        defaultValue: {
          summary: `true`,
        },
      },
      control: 'boolean',
    },
    showLogo: {
      name: 'showLogo',
      defaultValue: true,
      description: 'Handle show logo',
      table: {
        type: { summary: 'showLogo' },
        defaultValue: {
          summary: `true`,
        },
      },
      control: 'boolean',
    },
    objectType: {
      name: 'objectType',
      defaultValue: DASHBOARD_MODULE_CONFIG.objectType,
      description: 'Pass object type to get dashboard api',
      table: {
        type: { summary: 'objectType' },
        defaultValue: {
          summary: `${DASHBOARD_MODULE_CONFIG.objectType}`,
        },
      },
      control: 'text',
    },
    objectId: {
      name: 'objectId',
      defaultValue: DASHBOARD_MODULE_CONFIG.objectId,
      description: 'Pass object id to get dashboard api',
      table: {
        type: { summary: 'objectId' },
        defaultValue: {
          summary: `${DASHBOARD_MODULE_CONFIG.objectId}`,
        },
      },
      control: 'number',
    },
    isGrouped: {
      name: 'isGrouped',
      defaultValue: DASHBOARD_MODULE_CONFIG.isGrouped,
      description: 'Pass isGrouped to get dashboard api',
      table: {
        type: { summary: 'isGrouped' },
        defaultValue: {
          summary: `${DASHBOARD_MODULE_CONFIG.isGrouped}`,
        },
      },
      control: 'boolean',
    },
    customization: {
      name: 'customization',
      defaultValue: {
        type: 'default',
        showButtonExpand: true,
        showMenuPopover: true,
        items: [],
        activeKey: undefined,
        defaultExpandMenu: undefined,
        onMenuItemClick: undefined,
      },
      description: `Allow to custom left menu

- type: type of left menu
- showButtonExpand: handle show button toggle menu
- showMenuPopover: handle show menu popover when user hover into app menu item
- items: list of custom menu items
- defaultExpandMenu: default expand menu. If value is undefined, the last expand value stored in cookies will be taken
- onMenuItemClick: handle function on click into menu item
      `,
      table: {
        type: { summary: 'customization' },
        defaultValue: {
          summary: `{
            type: 'default',
            showButtonExpand: true,
            showMenuPopover: true,
            items: [],
            activeKey: undefined,
            defaultExpandMenu: undefined,
            onMenuItemClick: undefined,
          }`,
        },
      },
      control: 'object',
    },
    onActiveMenuCodeChange: {
      name: 'onActiveMenuCodeChange',
      defaultValue: '-',
      description: 'Callback function when active menu change',
      table: {
        type: { summary: 'onActiveMenuCodeChange' },
        defaultValue: {
          summary: '-',
        },
      },
      control: undefined,
    },
  },
  parameters: {
    docs: {
      description: {
        component: 'Left Menu. \n',
      },
    },
  },
} as Meta<typeof LeftMenu>;

const Template: StoryFn<typeof LeftMenu> = args => <LeftMenu {...args} />;

export const Default = {
  render: Template,
  args: {},
};

export const BasicUsage: StoryObj<any> = {
  render: () => <LeftMenu appConfig={appConfig} />,

  parameters: {
    docs: {
      description: {
        story: 'Simple Left Menu with actions.',
      },
    },
  },
};

export const Custom: StoryObj<any> = {
  render: () => {
    const [activeKey, setActiveKey] = useState<string>('EXPLORE');

    // Handlers
    const onMenuItemClick = (key: string, keyPath: string[]) => {
      setActiveKey(key);
      console.log({ key, keyPath });
    };

    return (
      <div style={{ width: '100%', height: '100%', backgroundColor: '#F6F8FC' }}>
        <LeftMenu
          appConfig={appConfig}
          customization={{
            type: 'customization',
            showButtonExpand: true,
            activeKey,
            items: CUSTOM_MENU_ITEMS,
            onMenuItemClick,
          }}
        />
      </div>
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Custom Left Menu with actions.',
      },
    },
  },
};

export const HideButtonExpandAndMenuPopover: StoryObj<any> = {
  render: () => {
    const [activeKey, setActiveKey] = useState<string>('EXPLORE');

    // Handlers
    const onMenuItemClick = (key: string, keyPath: string[]) => {
      setActiveKey(key);
      console.log({ key, keyPath });
    };

    return (
      <div style={{ width: '100%', height: '100%', backgroundColor: '#F6F8FC' }}>
        <LeftMenu
          appConfig={appConfig}
          customization={{
            type: 'customization',
            showButtonExpand: false,
            showMenuPopover: false,
            defaultExpandMenu: false,
            activeKey,
            items: CUSTOM_MENU_ITEMS,
            onMenuItemClick,
          }}
        />
      </div>
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Hide button expand menu and menu popover of Left Menu with actions.',
      },
    },
  },
};

export const Recommendation: StoryObj<any> = {
  render: () => (
    <div style={{ width: '100%', height: '100%', backgroundColor: '#F6F8FC' }}>
      <MenuMapping appConfig={appConfig} />
    </div>
  ),

  parameters: {
    docs: {
      description: {
        story: 'Recommendation Menu with actions.',
      },
    },
  },
};
