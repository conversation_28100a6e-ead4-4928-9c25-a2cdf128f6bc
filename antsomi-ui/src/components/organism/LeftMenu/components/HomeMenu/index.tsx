// Libraries
import React, { memo } from 'react';

// Components
import { ChildMenu } from '../common';
import { Flex } from '../../../../atoms';

// Constants
import Icon from '@antscorp/icons';

// Styled
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Home<PERSON>enuWrapper, ModalWrapper } from './styled';

// Hooks
import { useHomeMenu } from './useHomeMenu';

// const MAP_TITLE = {
//   REMOVE_ACCESS: {
//     HEADING: getTranslateMessage(TRANSLATE_KEY._TITL_REMOV_ACCESS_TAB, 'Remove access to this tab'),
//     BODY: getTranslateMessage(
//       TRANSLATE_KEY.REMOV_ACCESS_TAB,
//       'This action will remove your access to this tab. You will not see it anymore. Are you sure you want to continue?',
//     ),
//     HEADING_DASHBOARD: getTranslateMessage(
//       TRANSLATE_KEY._TITL_REMOV_ACCESS_DB,
//       'Remove access to this dashboard',
//     ),
//     BODY_DASHBOARD: getTranslateMessage(
//       TRANSLATE_KEY.REMOV_ACCESS_DB,
//       'This action will remove your access to selected dashboard. You will not see it anymore. Are you sure you want to continue?',
//     ),
//   },
//   DELETE_TAB: {
//     HEADING: getTranslateMessage(TRANSLATE_KEY._TITL_DELETE_TAB, 'Remove this tab'),
//     BODY: getTranslateMessage(
//       TRANSLATE_KEY.DELETE_TAB_MESS,
//       'Removing this tab will delete it permanently. Are you sure you want to perform this action?',
//     ),
//     HEADING_DASHBOARD: getTranslateMessage(TRANSLATE_KEY._TITL_DELETE_DB, 'Remove this dashboard'),
//     BODY_DASHBOARD: getTranslateMessage(
//       TRANSLATE_KEY.DELETE_DB_MESS,
//       'Removing this dashboard will delete it permanently. Are you sure you want to perform this action?',
//     ),
//   },
//   'my-report-template': {
//     HEADING: getTranslateMessage(TRANSLATE_KEY._TITL_SHARE_RP, 'Share Report'),
//     BODY: getTranslateMessage(
//       TRANSLATE_KEY._SHARE_RP_MESS,
//       'Sharing this tab with Public access will make your report public for everyone on the portal.',
//     ),
//   },
// };

export interface HomeMenuProps {
  isHover?: boolean;
}

export const HomeMenu: React.FC<HomeMenuProps> = memo(props => {
  const {
    children,
    removedDashboardId,
    isDashboardRemoving,
    type,
    showCreateButton,
    setRemovedDashboardId,
    onCreateNewReport,
    // onMenuClick,
    onRemoveDashboard,
  } = useHomeMenu(props);

  return (
    <>
      <HomeMenuWrapper gap={10} vertical>
        {type !== 'recommendation' && showCreateButton && (
          <CreateButton type="primary" onClick={onCreateNewReport}>
            <Flex gap={10} align="center">
              <Icon type="icon-ants-plus-slim" style={{ fontSize: 14 }} />
              <span>Create</span>
            </Flex>
          </CreateButton>
        )}
        <div className="menu-list">
          <ChildMenu items={children} /* onMenuClick={onMenuClick} */ />
        </div>
      </HomeMenuWrapper>
      <ModalWrapper
        open={!!removedDashboardId}
        centered
        onCancel={() => setRemovedDashboardId('')}
        title="Remove this dashboard"
        okText="Confirm"
        cancelText="Cancel"
        closable={false}
        okButtonProps={{ loading: isDashboardRemoving }}
        onOk={onRemoveDashboard}
      >
        Removing this dashboard will delete it permanently. Are you sure you want to perform this
        action?
      </ModalWrapper>
    </>
  );
});
