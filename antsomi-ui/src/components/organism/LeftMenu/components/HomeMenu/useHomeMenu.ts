// Libraries
import { use<PERSON>allback, useMemo, useState } from 'react';

// Constants
import { CONFIG_OPTIONS } from './constants';
import { HOME_REPORT_ROUTES, HOME_ROUTE } from '../../constants';
import { API_RESPONSE_CODE, CDP_API, CDP_ROUTE } from '@antscorp/antsomi-ui/es/constants';
import { MENU_CODE } from '@antscorp/antsomi-ui/es/components/template/Layout/constants';
import { MENU_PERMISSION } from '@antscorp/antsomi-ui/es/components/molecules/ShareAccess/constants';

// Utils
import { getGeneratePath, getMenuItem } from '../../utils';

// Types
import { TRemoveDashboardArgs } from '@antscorp/antsomi-ui/es/services/LeftMenu';

// Hooks;
import { useLeftMenuContext } from '../../contexts';
import { useLayoutStore } from '@antscorp/antsomi-ui/es/components/template';
import { useCustomRouter } from '@antscorp/antsomi-ui/es/hooks';
import { usePermission } from '../../hooks';

// Queries
import { useRemoveDashboard } from '@antscorp/antsomi-ui/es/queries/LeftMenu';
import { HomeMenuProps } from '.';
import { ENV } from '@antscorp/antsomi-ui/es/config';

export const useHomeMenu = (props: HomeMenuProps) => {
  const { isHover } = props;
  const { pathname, search, origin } = window.location;
  const { navigate, replace } = useCustomRouter();
  const appMenuChildren = useLeftMenuContext(store => store.appMenuChildren);
  const appHoverMenuChildren = useLeftMenuContext(store => store.appHoverMenuChildren);
  const type = useLeftMenuContext(store => store.type);
  const auth = useLeftMenuContext(store => store.appConfig?.auth);
  const env = useLeftMenuContext(store => store.appConfig?.env);
  const dashboardParams = useLeftMenuContext(store => store.dashboardParams);
  const dashboardList = useLeftMenuContext(store => store?.dashboardList || []);
  const onClickCreateDashboard = useLayoutStore(
    store => store.state.leftMenu.onClickCreateDashboard,
  );
  const { flattenMenuPermission } = usePermission();

  const [removedDashboardId, setRemovedDashboardId] = useState<string>('');
  const urlParams = useMemo(() => new URLSearchParams(search), [search]);
  const showCreateButton = useMemo(() => {
    const dashboardMenu = flattenMenuPermission.find(
      menu => menu.menu_code === MENU_CODE.DASHBOARD,
    );

    return !!dashboardMenu && dashboardMenu.selected_edit !== MENU_PERMISSION.NONE;
  }, [flattenMenuPermission]);

  const { mutateAsync: removeDashboard, isLoading: isDashboardRemoving } = useRemoveDashboard();

  const onOptionCallback = useCallback(
    (args: { menuItemKey: string; optionKey: string }) => {
      const { optionKey, menuItemKey } = args;
      switch (optionKey) {
        case CONFIG_OPTIONS.CONFIGURE.key: {
          const newPathName = getGeneratePath(HOME_REPORT_ROUTES.DETAIL, {
            ...auth,
            dashboardId: menuItemKey,
          });
          navigate(newPathName);
          urlParams.set('type', HOME_REPORT_ROUTES.CONFIGURE);
          replace({ search: urlParams.toString() });
          break;
        }
        case CONFIG_OPTIONS.REMOVE.key:
          setRemovedDashboardId(menuItemKey);
          break;
        default:
          break;
      }
    },
    [auth, navigate, replace, urlParams],
  );

  const customChildren = useMemo(
    () =>
      appMenuChildren?.map(appMenuItem =>
        getMenuItem({
          ...appMenuItem,
          children: appMenuItem?.children?.map(item => ({
            ...item,
            ...(item?.dashboardEditable
              ? { options: Object.values(CONFIG_OPTIONS), optionCallback: onOptionCallback }
              : {}),
          })),
        }),
      ),
    [appMenuChildren, onOptionCallback],
  );

  const customHoverChildren = useMemo(
    () =>
      appHoverMenuChildren?.map(appMenuItem =>
        getMenuItem({
          ...appMenuItem,
          children: appMenuItem?.children,
        }),
      ),
    [appHoverMenuChildren],
  );

  const onCreateNewReport: React.MouseEventHandler<HTMLElement> = useCallback(
    e => {
      if (onClickCreateDashboard) {
        onClickCreateDashboard(e);
      } else {
        urlParams.set('type', HOME_REPORT_ROUTES.CREATE);
        if (env !== ENV.DEV && origin !== CDP_ROUTE?.[`${env}`]) {
          window.location.assign(
            `${CDP_ROUTE?.[`${env}`]}${getGeneratePath(HOME_ROUTE, auth)}?${urlParams.toString()}`,
          );
        } else {
          navigate(getGeneratePath(HOME_ROUTE, auth));
          replace({ search: urlParams.toString() });
        }
      }
    },
    [auth, env, navigate, onClickCreateDashboard, origin, replace, urlParams],
  );

  const onMenuClick = useCallback(
    (key: string, _keyPath: string[]) => {
      if (type !== 'recommendation') {
        const newPathName = getGeneratePath(HOME_REPORT_ROUTES.DETAIL, {
          ...auth,
          dashboardId: key,
        });
        if (newPathName !== pathname) {
          navigate(newPathName);
        }
      }
    },
    [type, auth, pathname, navigate],
  );

  const onRemoveDashboard = useCallback(async () => {
    if (removedDashboardId && auth && env) {
      const params: TRemoveDashboardArgs = {
        params: {
          ...dashboardParams,
          dashboardId: +removedDashboardId,
        },
        auth: { ...auth, url: `${CDP_API?.[env]}/hub` },
      };

      const response = await removeDashboard(params);
      if (response?.code === API_RESPONSE_CODE.SUCCESS) {
        let nextDashboardId: number | undefined;
        for (const [index, item] of dashboardList.entries()) {
          if (item.dashboardId === +removedDashboardId) {
            const newDashboardList = dashboardList?.filter(
              item => item.dashboardId !== +removedDashboardId,
            );
            nextDashboardId =
              newDashboardList?.[index + 1]?.dashboardId || newDashboardList?.[0]?.dashboardId;
            break;
          }
        }

        const newPathName = nextDashboardId
          ? getGeneratePath(HOME_REPORT_ROUTES.DETAIL, {
              ...auth,
              dashboardId: String(nextDashboardId),
            })
          : getGeneratePath(HOME_ROUTE, {
              ...auth,
            });

        if (newPathName !== pathname) {
          navigate(newPathName);
        }

        setRemovedDashboardId('');
      }
    }
  }, [
    removedDashboardId,
    auth,
    env,
    dashboardParams,
    removeDashboard,
    dashboardList,
    pathname,
    navigate,
  ]);

  return {
    children: isHover ? customHoverChildren : customChildren,
    removedDashboardId,
    isDashboardRemoving,
    type,
    showCreateButton,
    setRemovedDashboardId,
    onCreateNewReport,
    onOptionCallback,
    onMenuClick,
    onRemoveDashboard,
  };
};
