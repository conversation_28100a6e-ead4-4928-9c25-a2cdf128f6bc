// Libraries
import React, { memo, useState } from 'react';
import Icon from '@antscorp/icons';
import { isEmpty, isNil, uniq } from 'lodash';
import classNames from 'classnames';
import { Link } from 'react-router-dom';

// Components
import { Dropdown, Menu, MenuProps } from 'antd';
import { MenuItemImage } from './components';
import { Flex } from '../../../../../atoms';

// Styled
import { LabelCustom, LabelWrapper, MenuWrapper, OptionDropdownWrapper } from './styled';
import { IconWrapper } from '../../../styled';

// Constants
import { HOME_MENU_ITEMS, ICON_SIZE } from '../../../constants';

// Types
import { TMenuItem } from '../../../types';

// Utils
import {
  findActiveAppCodeByUrl,
  findLastMatchedItemByUrl,
  getMenuItem,
  renderMenuIcon,
} from '../../../utils';
import {
  handleActiveRecommendationItem,
  recursiveFindItemByKey,
  recursiveFindParentOfActiveItem,
} from './utils';

// Hooks
import { useNavigatePath } from '../../../hooks';
import { useCustomRouter, useDeepCompareEffect } from '@antscorp/antsomi-ui/es/hooks';

// Contexts
import { useLeftMenuContext } from '../../../contexts';
import { THEME } from '@antscorp/antsomi-ui/es/constants';

const noDashboardItem: TMenuItem = {
  key: 'no_dashboard',
  label: 'Click Create to make a dashboard',
  disabled: true,
  icon: null,
  style: { fontSize: '10px', color: THEME?.token?.bw8 },
};

const styles: Record<string, React.CSSProperties> = {
  dropdownMenu: { width: '130px', padding: '8px 0' },
  dotIcon: { marginTop: '2px', zIndex: 1000, flexShrink: 0, fontSize: 16 },
  expandIcon: {
    fontSize: ICON_SIZE,
    color: 'inherit',
  },
  menuItemTitle: { flex: '1 1 0%', maxWidth: '100%', overflow: 'hidden' },
};

interface ChildMenuProps {
  items?: TMenuItem[];
  onMenuClick?: (key: string, keyPath: string[]) => void;
}

export const ChildMenu: React.FC<ChildMenuProps> = memo(props => {
  const { items = [], onMenuClick } = props;
  const auth = useLeftMenuContext(store => store.appConfig?.auth);
  const env = useLeftMenuContext(store => store.appConfig?.env);
  const activeAppCode = useLeftMenuContext(store => store.activeAppCode);
  const menuItems = useLeftMenuContext(store => store.menuItems);
  const customItems = useLeftMenuContext(store => store.customItems);
  const type = useLeftMenuContext(store => store.type);
  const customActiveCurrentKey = useLeftMenuContext(store => store.customActiveCurrentKey);
  const setLeftMenuState = useLeftMenuContext(store => store.setState);
  const { navigate } = useCustomRouter();

  const [currentActiveItem, setCurrentActiveItem] = useState<string>('');
  const [openKeys, setOpenKeys] = useState<string[]>([]);

  const { pathname, hash } = window.location;
  const { isPushDifferentDomain, getPath, getPathWithDomain, navigatePath } = useNavigatePath();

  useDeepCompareEffect(() => {
    setOpenKeys(items?.map(item => item.key) || []);
  }, [items]);

  useDeepCompareEffect(() => {
    switch (type) {
      case 'customization': {
        setCurrentActiveItem(customActiveCurrentKey);
        // Find parent key of active menu item to open it
        const parentKey = recursiveFindParentOfActiveItem({
          activeMenuItem: customActiveCurrentKey,
          menuItems: customItems,
        });
        if (parentKey) {
          setOpenKeys(prev => uniq([...prev, parentKey]));
        }
        break;
      }
      case 'recommendation': {
        const menuActiveKey = handleActiveRecommendationItem({
          customActiveCurrentKey,
          currentActiveItem,
          items,
        });

        if (menuActiveKey) {
          setCurrentActiveItem(menuActiveKey);
          /** Find parent key of active menu item to open it */
          const parentKey = recursiveFindParentOfActiveItem({
            activeMenuItem: menuActiveKey,
            menuItems: menuItems?.map(item => ({
              ...getMenuItem(item),
              key: item?.permission_code || '',
            })),
          });

          if (parentKey) {
            setOpenKeys(prev => uniq([...prev, parentKey]));
          }
        }

        break;
      }
      default: {
        const url = `${pathname}${hash}`;

        // Active menu item by current url
        const activeMenuItem = findLastMatchedItemByUrl({
          url,
          menuItems,
          auth,
          env,
        });

        const activeMenuItemCode = activeMenuItem?.menu_item_code;

        if (activeMenuItemCode && activeMenuItemCode !== currentActiveItem) {
          setCurrentActiveItem(activeMenuItemCode);

          // Find parent key of active menu item to open it
          const parentKey = recursiveFindParentOfActiveItem({
            activeMenuItem: activeMenuItemCode,
            menuItems: menuItems?.map(item => getMenuItem(item)),
          });
          if (parentKey) {
            setOpenKeys(prev => uniq([...prev, parentKey]));
          }

          // Active App Item having code matching url
          const matchAppCode = findActiveAppCodeByUrl({ menuItems, url, auth });
          if (matchAppCode && matchAppCode !== activeAppCode) {
            setLeftMenuState({ activeAppCode: matchAppCode });
          }
        }
        break;
      }
    }
  }, [
    auth,
    currentActiveItem,
    hash,
    items,
    pathname,
    activeAppCode,
    menuItems,
    type,
    customActiveCurrentKey,
    customItems,
    env,
    setLeftMenuState,
  ]);

  const onClick: MenuProps['onClick'] = e => {
    if (type === 'recommendation') {
      const [permissionCode, permissionCodePath] = (() => {
        if (e.key === 'WEB_PERSONALIZE') {
          return ['WEB_PERSONALIZE-JOURNEY', ['WEB_PERSONALIZE-JOURNEY']];
        }
        const permissionCode = recursiveFindItemByKey({
          activeKey: e.key,
          menuItems: items || [],
        })?.permission_code;

        const permissionCodePath = (() => {
          const array: string[] = [];
          e.keyPath?.forEach(item => {
            const permissionCode = recursiveFindItemByKey({
              activeKey: item,
              menuItems: items || [],
            })?.permission_code;

            if (permissionCode) array.push(permissionCode);
          });
          return uniq(array);
        })();
        return [permissionCode, permissionCodePath];
      })();

      if (permissionCode) onMenuClick?.(permissionCode, permissionCodePath);
      setCurrentActiveItem(e.key);
    } else {
      onMenuClick?.(e.key, e.keyPath);
    }
  };

  const customMenuItems = (args: { parent?: TMenuItem; items?: TMenuItem[] }) =>
    args?.items?.map(item => {
      const {
        key,
        label,
        children,
        icon,
        logo_url,
        menu_item_path = null,
        menu_item_domain = null,
        options,
        disabled,
        style,
        optionCallback,
      } = item;

      const isOwnerDashboardKey = key === HOME_MENU_ITEMS.DASHBOARD;

      const renderLabel = () => (
        <LabelWrapper gap={10} align="center" justify="space-between" style={style}>
          <LabelCustom ellipsis={{ tooltip: label }}>{label}</LabelCustom>
          {options?.length && type !== 'recommendation' && (
            <OptionDropdownWrapper align="center">
              <Dropdown
                menu={{
                  items: options,
                  onClick: e => {
                    e.domEvent.stopPropagation();
                    e.domEvent.preventDefault();
                    optionCallback?.({ menuItemKey: key, optionKey: e.key });
                  },
                  style: styles.dropdownMenu,
                }}
                trigger={['click']}
              >
                <Icon
                  className="child-menu-item-icon"
                  type="icon-ants-three-dot-vertical"
                  style={styles.dotIcon}
                  onClick={e => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                />
              </Dropdown>
            </OptionDropdownWrapper>
          )}
          {!children?.length && type === 'recommendation' && !!menu_item_path && (
            <div
              className="icon-link"
              onClick={() => {
                navigatePath(menu_item_domain, menu_item_path, true);
              }}
            >
              <Icon type="icon-ants-open-in-new-window" />
            </div>
          )}
        </LabelWrapper>
      );

      const renderIcon = () =>
        !!icon || !!logo_url ? (
          <Flex style={{ flexShrink: 0 }} align="center">
            {!isNil(icon) ? (
              renderMenuIcon(icon)
            ) : !isNil(logo_url) ? (
              <MenuItemImage
                imageUrl={logo_url}
                fallbackIcon={args?.parent?.icon}
                className={classNames({ isActive: currentActiveItem === key })}
              />
            ) : null}
          </Flex>
        ) : null;

      const renderTitle = () =>
        !menu_item_path || type === 'recommendation' ? (
          renderLabel()
        ) : (
          <div className="menu-link">{renderLabel()}</div>
        );

      let Label = (
        <Flex gap={5} align="center" justify="space-between" style={{ height: '100%' }}>
          {renderIcon()}
          <Flex style={styles.menuItemTitle} align="center">
            {renderTitle()}
          </Flex>
        </Flex>
      );

      // Check if type not recommendation then render with link wrapper
      if (type !== 'recommendation' && !!menu_item_path) {
        Label = isPushDifferentDomain(menu_item_domain, menu_item_path) ? (
          <a href={getPathWithDomain(menu_item_domain, menu_item_path)}>{Label}</a>
        ) : (
          <Link to={getPath(menu_item_path, key)}>{Label}</Link>
        );
      }

      return {
        key,
        label: Label,
        disabled,
        children: customMenuItems({
          parent: item,
          items: isOwnerDashboardKey && isEmpty(children) ? [noDashboardItem] : children,
        }),
      };
    });

  return (
    <MenuWrapper>
      <Menu
        motion={{
          motionAppear: false,
          motionEnter: false,
          motionLeave: false,
        }}
        selectedKeys={[currentActiveItem]}
        defaultOpenKeys={[currentActiveItem]}
        openKeys={uniq(openKeys)}
        mode="inline"
        items={customMenuItems({ items })}
        inlineIndent={12}
        onOpenChange={openKeys => {
          setOpenKeys(uniq(openKeys));
        }}
        expandIcon={({ isOpen }) => (
          <Icon
            type="icon-ants-expand-more"
            style={{
              ...styles.expandIcon,
              transform: `rotate(${isOpen ? '180deg' : '0deg'})`,
            }}
          />
        )}
        onClick={onClick}
      />
    </MenuWrapper>
  );
});
