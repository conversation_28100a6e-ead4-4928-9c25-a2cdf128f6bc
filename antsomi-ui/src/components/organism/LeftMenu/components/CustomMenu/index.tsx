// Libraries
import React, { memo } from 'react';

// Components
import { ChildMenu } from '../common';

// Hooks
import { useLeftMenuContext } from '../../contexts';

interface CustomMenuProps {
  isHover?: boolean;
}

export const CustomMenu: React.FC<CustomMenuProps> = memo(({ isHover }) => {
  const appCustomMenuChildren = useLeftMenuContext(store => store.customMenuChildren);
  const appCustomHoverMenuChildren = useLeftMenuContext(store => store.customHoverMenuChildren);
  const onMenuClick = useLeftMenuContext(store => store.onMenuClick);
  return (
    <ChildMenu
      items={isHover ? appCustomHoverMenuChildren : appCustomMenuChildren}
      onMenuClick={onMenuClick}
    />
  );
});
