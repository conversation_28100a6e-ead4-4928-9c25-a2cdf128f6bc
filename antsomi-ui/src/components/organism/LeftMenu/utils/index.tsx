// Libraries
import React, { Suspense } from 'react';
import { cloneDeep, isArray, isEmpty, isNil, sortBy } from 'lodash';
// Types
import {
  FeatureMenuPermission,
  TDashboard,
  TDestinationChannel,
  TFeatureMenu,
} from '@antscorp/antsomi-ui/es/models/LeftMenu';
import { PayloadInfo } from '@antscorp/antsomi-ui/es/types';
import { TMenuFeatureItem, TMenuItem } from '../types';
import { TAccountPermission } from '@antscorp/antsomi-ui/es/models/AccountListing';
import { TEnv } from '@antscorp/antsomi-ui/es/types/config';

// Components
import { LazyIcon as Icons } from '@antscorp/antsomi-ui/es/components/icons/LazyIcon';
/** Use this Icons for test storybook */
// import * as Icons from '@antscorp/antsomi-ui/es/components/icons';
import { Icon } from '@antscorp/antsomi-ui/es/components/atoms/Icon';

// Constants
import {
  APP_KEYS,
  MARKETING_CHANNEL_KEY,
  HOME_MENU_ITEMS,
  MARKETING_ROUTES,
  MENU_ITEM_TYPE,
  RENDER_OPTION,
  HOME_REPORT_ROUTES,
  SETTING_APP,
  APP_EXCLUDED_KEYS,
  MAP_MENU_TO_PARENT_APP_CODE,
} from '../constants';
import { MARKETING_CHANNEL_CODE, KEEP_HASH_PATH_DEV } from '../../../template/Layout/constants';
import { ENV } from '@antscorp/antsomi-ui/es/config';
import { USER_PERMISSIONS } from '@antscorp/antsomi-ui/es/constants';

// Utils
import { handleError } from '@antscorp/antsomi-ui/es/utils';

// Styled
import { IconWrapper } from '../styled';
import { PUBLIC_ROLE } from '../../../molecules/ShareAccess/constants';

const PATH = 'src/components/organism/LeftMenu/utils/index.ts';

/**
 * Converts a feature menu item to a menu item suitable for rendering in the UI.
 * @param {TMenuFeatureItem} featureMenu - The feature menu item to convert.
 * @returns {TMenuItem} The converted menu item.
 */
export const getMenuItem = (featureMenu: TMenuFeatureItem): TMenuItem => {
  const {
    menu_item_code,
    component_icon_name,
    menu_item_name,
    children,
    icon_name,
    ...restOfFeatureMenu
  } = featureMenu;
  return {
    key: menu_item_code,
    label: menu_item_name,
    icon: component_icon_name || icon_name,
    children: children?.map(item => getMenuItem(item)),
    ...restOfFeatureMenu,
  };
};

/**
 * Flatten a nested array of menu items by flatten @alias childKey into a single-level array.
 * @template T - The type of menu items.
 * @param {T[]} menuItems - The array of menu items to flatten.
 * @param {string} childKey - The key representing the children array in each menu item.
 * @returns {T[]} The flattened array of menu items.
 */
export const flattenMenuArray = (
  menuItems: FeatureMenuPermission[],
  childKey: string,
  parentAppCode?: FeatureMenuPermission['parent_app_code'],
): FeatureMenuPermission[] => {
  let result: FeatureMenuPermission[] = [];
  for (const item of menuItems) {
    // If item has app_code and the app code is in the excluded list, skip it
    if (!!item.app_code && APP_EXCLUDED_KEYS.includes(item.app_code)) {
      continue;
    }

    const children: FeatureMenuPermission[] = item?.[childKey] || [];
    result.push({
      ...item,
      ...(parentAppCode ? { parent_app_code: parentAppCode } : {}),
      [childKey]: [],
    });

    if (children.length > 0) {
      result = [...result, ...flattenMenuArray(children, childKey, item.app_code)];
    }
  }

  return result;
};

/**
 * Recursively filters menu items based on permissions.
 * check permission if menu item has menu_type of @alias MENU_ITEM_TYPE.MENU
 */
export const recursivePermissionMenu = (
  menuItems: TFeatureMenu[],
  menuPermissions: FeatureMenuPermission[],
) => {
  const menu: TFeatureMenu[] = menuItems
    ?.filter(({ permission_code, menu_item_type, menu_item_code }) => {
      if (Number(menu_item_type) !== MENU_ITEM_TYPE.MENU) {
        return true;
      }

      return !!menuPermissions?.find(({ menu_code = '', parent_app_code }) => {
        if (permission_code) {
          const parentAppCode = MAP_MENU_TO_PARENT_APP_CODE[menu_item_code];

          // If parent app code is exist in the MAP_MENU_TO_PARENT_APP_CODE then check with parent app code
          if (parentAppCode && parent_app_code) {
            return parentAppCode === parent_app_code && [menu_code].includes(permission_code);
          }

          // Else just check with menu_code
          return [menu_code].includes(permission_code);
        }

        return false;
      });
    })
    ?.map(item => ({
      ...item,
      ...(isArray(item.children)
        ? {
            children: recursivePermissionMenu(
              sortBy(item.children || [], ['level_position']),
              menuPermissions,
            ),
          }
        : {}),
    }))
    ?.filter(item => !(isArray(item.children) && isEmpty(item.children)));

  return menu;
};

/**
 * Generate a new path by replacing parameters in the given path.
 * @returns {string} The generated path with replaced parameters.
 */
export const getGeneratePath = (
  path: string,
  params?: Partial<PayloadInfo> & { dashboardId?: string; channelId?: number },
): string => {
  try {
    const { portalId, userId, accountId, dashboardId, channelId } = params || {};

    const paramsObjects = {
      // NOTE: Hot fix for account_id (login account)
      user_id: accountId ?? userId ?? '-1',
      networkId: portalId ?? -1,
      portalId: portalId ?? -1,
      dashboardId: dashboardId ?? '-1',
      channelId: channelId ?? -1,
    };

    Object.keys(paramsObjects).forEach(key => {
      path = path.replace(`:${key}`, paramsObjects[key]);
    });

    return path;
  } catch (error) {
    handleError(error, {
      path: PATH,
      name: getGeneratePath.name,
      args: { path, params },
    });
    return '';
  }
};

/**
 * This is used to delete the hash of the path returned from the api,
 * the purpose of which is to compare with the url to activate the app
 */
export const getComparePath = (path: string, env?: string) => {
  if (env !== ENV.DEV) {
    return path;
  }

  if (path?.includes('#')) {
    const customPath = path?.split('#')?.[1] || '';
    return customPath;
  }

  return path;
};

/**
 * Creates an initial feature menu item object with default values.
 * @param {Partial<TFeatureMenu>} featureMenuItem - The partial feature menu item object.
 * @returns {TFeatureMenu} The initial feature menu item object.
 */
export const getInitialFeatureMenuItem = (
  featureMenuItem: Partial<TFeatureMenu>,
): TFeatureMenu => ({
  menu_item: '',
  network_id: -1,
  menu_item_name: '',
  component_icon_name: null,
  level_position: 0,
  icon_name: null,
  menu_item_path: null,
  menu_item_code: '',
  permission_code: null,
  menu_item_type: 3,
  menu_item_parent: 0,
  render_option: null,
  menu_item_domain: null,
  total_row: 0,
  ctime: null,
  utime: null,
  ...featureMenuItem,
});

export const recursiveGetInitialFeatureMenuItem = (featureMenuItem: Partial<TFeatureMenu>) => ({
  menu_item: '',
  network_id: -1,
  menu_item_name: '',
  level_position: 0,
  icon_name: null,
  menu_item_path: null,
  menu_item_code: '',
  permission_code: null,
  menu_item_type: 3,
  menu_item_parent: 0,
  render_option: null,
  menu_item_domain: null,
  total_row: 0,
  ctime: null,
  utime: null,
  ...featureMenuItem,
});

/**
 * Recursively converts a feature menu item to a menu item, including children,
 * based on permissions.
 *
 * @param {TFeatureMenu} featureMenuItem - The feature menu item to convert.
 * @returns {TMenuItem} - The converted menu item with permissions.
 */
export const recursiveGetMenuItemByPermission = (featureMenuItem: TFeatureMenu): TMenuItem => ({
  key: featureMenuItem.permission_code || featureMenuItem.menu_item_code || 'null',
  label: featureMenuItem.menu_item_name,
  icon: featureMenuItem.icon_name,
  logo_url: featureMenuItem.logo_url,
  children: featureMenuItem?.children?.map(child => recursiveGetMenuItemByPermission(child)),
});

/** Map children to each App Item */
export const getMappingAppChildren = (args: {
  menuList: TFeatureMenu[];
  auth?: Partial<PayloadInfo>;
  dashboardList?: TDashboard[];
  destinationChannelEntries?: TDestinationChannel[];
  destinationChannelGen2Entries?: TDestinationChannel[];
  isRecommendation?: boolean;
  userPermission?: TAccountPermission;
}): TFeatureMenu[] => {
  const {
    menuList,
    dashboardList,
    auth,
    destinationChannelEntries,
    destinationChannelGen2Entries,
    isRecommendation,
    userPermission,
  } = args;

  const recursiveAddRenderOption = (menuItem: TFeatureMenu) => {
    const cloneMenuItem = cloneDeep(menuItem);

    switch (cloneMenuItem?.render_option) {
      case RENDER_OPTION.CHANNEL: {
        const path = (() => {
          switch (menuItem.menu_item_code) {
            case SETTING_APP.CHANNEL_INTEGRATION.code:
              return SETTING_APP.CHANNEL_INTEGRATION.path;
            default:
              return '';
          }
        })();

        cloneMenuItem.children =
          destinationChannelEntries?.map(item => ({
            ...getInitialFeatureMenuItem({
              menu_item_code: item.channelCode,
              menu_item_name: item.translateLabel,
              menu_item_domain: cloneMenuItem.menu_item_domain,
              logo_url: item.logoUrl,
              menu_item_parent: menuItem.menu_item,
              menu_item_path: getGeneratePath(path, {
                ...auth,
                channelId: item.channelId,
              }),
            }),
          })) || [];

        break;
      }
      case RENDER_OPTION.CHANNEL_GEN2: {
        const path = (() => {
          switch (menuItem.menu_item_code) {
            case MARKETING_CHANNEL_CODE.CAMPAIGN:
              return MARKETING_ROUTES.CHANNEL;
            default:
              return '';
          }
        })();

        cloneMenuItem.children =
          destinationChannelGen2Entries?.map(item => ({
            ...getInitialFeatureMenuItem({
              menu_item_domain: cloneMenuItem.menu_item_domain,
              menu_item_code: `${menuItem.menu_item_code}_${item.channelCode}`,
              menu_item_name: item.translateLabel,
              logo_url: item.logoUrl,
              menu_item_parent: menuItem.menu_item,
              menu_item_path: getGeneratePath(path, {
                ...auth,
                channelId: item.channelId,
              }),
            }),
          })) || [];
        break;
      }
      default:
        break;
    }

    if (cloneMenuItem?.children) {
      cloneMenuItem.children = cloneMenuItem.children?.map(item => recursiveAddRenderOption(item));
    }
    return cloneMenuItem;
  };

  return menuList?.map(appItem => {
    switch (appItem.menu_item_code) {
      case APP_KEYS.HOME: {
        appItem.children = appItem.children?.map(childMenuItem => ({
          ...childMenuItem,
          children: (childMenuItem.menu_item_code === HOME_MENU_ITEMS.DASHBOARD
            ? dashboardList
            : undefined
          )?.map(item => {
            const { shareAccess } = item || {};
            const dashboardEditable = (() => {
              switch (userPermission?.permissions?.edit) {
                case USER_PERMISSIONS.EVERYTHING:
                  return true;
                case USER_PERMISSIONS.NONE:
                  return false;
                default: {
                  if (isEmpty(shareAccess)) return true;

                  const { is_public, public_role } = shareAccess || {};

                  // Check view public before list access
                  if (!!is_public && public_role === PUBLIC_ROLE.ONLY_EDITOR) {
                    return true;
                  }

                  return Boolean(
                    Number(
                      shareAccess?.list_access?.find(item => item.user_id === auth?.userId)
                        ?.allow_edit || '0',
                    ),
                  );
                }
              }
            })();

            return {
              ...getInitialFeatureMenuItem({
                page_title: appItem.page_title,
                menu_item_domain: childMenuItem.menu_item_domain,
                menu_item: String(item.dashboardId),
                menu_item_code: String(item.dashboardId),
                menu_item_name: item.dashboardName,
                menu_item_parent: childMenuItem.menu_item,
                menu_item_path: getGeneratePath(HOME_REPORT_ROUTES.DETAIL, {
                  ...auth,
                  dashboardId: String(item.dashboardId),
                }),
                dashboardEditable,
              }),
            };
          }),
        }));
        break;
      }
      case APP_KEYS.MARKETING: {
        appItem.children = appItem.children?.map(childMenuItem => {
          switch (childMenuItem.menu_item_code) {
            case MARKETING_CHANNEL_CODE.ALL_CHANNEL:
              childMenuItem.menu_item_path = getGeneratePath(childMenuItem.menu_item_path || '', {
                ...auth,
                channelId: MARKETING_CHANNEL_KEY.ALL_CHANNEL,
              });
              break;
            case MARKETING_CHANNEL_CODE.ORCHESTRATION:
              childMenuItem.menu_item_path = getGeneratePath(childMenuItem.menu_item_path || '', {
                ...auth,
                channelId: MARKETING_CHANNEL_KEY.JOURNEY_ORCHESTRATION,
              });
              break;
            default:
              break;
          }
          return childMenuItem;
        });
        break;
      }
      default:
        break;
    }

    if (!isRecommendation) {
      appItem = recursiveAddRenderOption(appItem);
    }
    return appItem;
  });
};

/**
 * Finds the app code of the active menu item based on the provided URL within the given menu items.
 * @param {object} args - The arguments object.
 * @param {string} args.url - The URL to match against menu item paths.
 * @param {TFeatureMenu[]} args.menuItems - The array of menu items to search within.
 * @param {Partial<PayloadInfo>} [args.auth] - Additional authentication information.
 * @returns {string | undefined} The app code of the active menu item or undefined if not found.
 */
export const findActiveAppCodeByUrl = (args: {
  url: string;
  menuItems: TFeatureMenu[];
  auth?: Partial<PayloadInfo>;
  env?: string;
}): string | undefined => {
  try {
    const { url, menuItems, auth, env } = args;
    let matchAppCode: string | undefined;
    const recursiveCheckHasActiveChild = (items: TFeatureMenu[]): boolean => {
      let matchAppCode: boolean = false;

      for (const item of items) {
        if (item?.menu_item_path) {
          const comparePath = KEEP_HASH_PATH_DEV.includes(item.menu_item_code)
            ? getGeneratePath(item.menu_item_path, auth)
            : getComparePath(getGeneratePath(item.menu_item_path, auth), env);
          if (url.includes(comparePath)) {
            matchAppCode = true;
            break;
          }
        }

        if (item?.children && item.children.length) {
          matchAppCode = recursiveCheckHasActiveChild(item.children);
          if (matchAppCode) {
            break;
          }
        }
      }

      return matchAppCode;
    };

    for (const menuItem of menuItems) {
      if (
        !!menuItem?.menu_item_path &&
        url.includes(getComparePath(getGeneratePath(menuItem.menu_item_path, auth), env))
      ) {
        matchAppCode = menuItem.menu_item_code;
        break;
      }

      const hasActiveChild = recursiveCheckHasActiveChild(menuItem?.children || []);
      if (hasActiveChild) {
        matchAppCode = menuItem.menu_item_code;
        break;
      }
    }
    return matchAppCode;
  } catch (error) {
    handleError(error, {
      path: PATH,
      name: findActiveAppCodeByUrl.name,
      args,
    });
  }
};

/**
 * Finds the last matched menu item based on the provided URL.
 *
 * @param {Object} args - The arguments object.
 * @param {string} args.url - The URL to match against menu items.
 * @param {TFeatureMenu[]} args.menuItems - The array of menu items to search through.
 * @param {Partial<PayloadInfo>} [args.auth] - Optional authorization information for generating paths.
 * @param {TEnv} [args.env='development'] - Optional environment parameter to adjust path comparison.
 * @returns {TFeatureMenu | undefined} - The last matched menu item, or undefined if not found.
 */
export const findLastMatchedItemByUrl = (args: {
  url: string;
  menuItems: TFeatureMenu[];
  auth?: Partial<PayloadInfo>;
  env?: TEnv;
}): TFeatureMenu | undefined => {
  try {
    const { url, menuItems, auth, env = 'development' } = args;
    const matchedItems: TFeatureMenu[] = [];
    const recursiveFindActiveItem = (items: TFeatureMenu[], parentId?: string) => {
      for (const item of items) {
        if (item?.menu_item_path) {
          const comparePath = KEEP_HASH_PATH_DEV.includes(item.menu_item_code)
            ? getGeneratePath(item.menu_item_path, auth)
            : getComparePath(getGeneratePath(item.menu_item_path, auth), env);

          if (url.includes(comparePath) && (!parentId || item.menu_item_parent === parentId)) {
            matchedItems.push(item);
          }
        }

        if (item?.children && item.children.length) {
          recursiveFindActiveItem(item.children, String(item.menu_item));
        }
      }
    };
    recursiveFindActiveItem(menuItems || []);
    const recommendationItem = matchedItems.find(
      item => item.menu_item_code === HOME_MENU_ITEMS.RECOMMENDATION,
    );

    if (recommendationItem) {
      return recommendationItem;
    }
    return matchedItems?.pop();
  } catch (error) {
    handleError(error, {
      path: PATH,
      name: findLastMatchedItemByUrl.name,
      args,
    });
  }
};

/**
 * Finds the path of the active menu item in a nested menu structure.
 *
 * @param {Object} args - The arguments object.
 * @param {TFeatureMenu} args.activeMenuItem - The active menu item to find the path for.
 * @param {TFeatureMenu[]} args.menuItems - The array of menu items to search through.
 * @returns {TFeatureMenu[]} - An array representing the path of the active menu item.
 */
export const findPathOfActiveItem = (args: {
  activeMenuItem: TFeatureMenu;
  menuItems: TFeatureMenu[];
}): TFeatureMenu[] => {
  try {
    const { activeMenuItem, menuItems } = args;
    const recursiveFindPathOfActiveItem = (menuItems: TFeatureMenu[], parentId?: string) => {
      let path: TFeatureMenu[] = [];
      for (const item of menuItems) {
        if (
          item.menu_item_code === activeMenuItem?.menu_item_code &&
          (!parentId || activeMenuItem?.menu_item_parent === parentId)
        ) {
          path.push(item);
          break;
        }
        const childPath = recursiveFindPathOfActiveItem(
          item?.children || [],
          String(item.menu_item),
        );
        if (childPath.length) {
          path = [item, ...childPath];
          break;
        }
      }

      return path;
    };

    return recursiveFindPathOfActiveItem(menuItems);
  } catch (error) {
    handleError(error, {
      path: PATH,
      name: findPathOfActiveItem.name,
      args,
    });
    return [];
  }
};

/**
 * Finds the app code of the menu item or its parent based on the provided active key.
 *
 * @param {Object} args - The arguments object.
 * @param {string} args.activeCurrentKey - The key of the currently active menu item.
 * @param {TMenuItem[]} args.menuItems - The array of menu items to search through.
 * @returns {string | undefined} - The app code of the matching menu item or its parent, or undefined if not found.
 */
export const findActiveAppCodeByChild = (args: {
  activeCurrentKey: string;
  menuItems: TMenuItem[];
  isRecommendation?: boolean;
}): string | undefined => {
  try {
    const { activeCurrentKey, menuItems, isRecommendation } = args;
    let activeAppCode: string | undefined;
    const recursiveCheckHasActiveChild = (items: TMenuItem[], parentKey: string): boolean => {
      let matchedAppCode: boolean = false;

      for (const item of items) {
        if (isRecommendation && activeCurrentKey.includes('DATASOURCES')) {
          const [activeKey, activeParentKey] = activeCurrentKey.split('-');
          if (item.key === activeKey && parentKey === activeParentKey) {
            matchedAppCode = true;
            break;
          }
        } else {
          if (item.key === activeCurrentKey) {
            matchedAppCode = true;
            break;
          }
        }

        if (item?.children && item.children.length) {
          matchedAppCode = recursiveCheckHasActiveChild(item.children, item.key);
          if (matchedAppCode) {
            break;
          }
        }
      }

      return matchedAppCode;
    };

    for (const menuItem of menuItems) {
      if (menuItem.key === activeCurrentKey) {
        activeAppCode = activeCurrentKey;
        break;
      }

      const hasActiveChild = recursiveCheckHasActiveChild(menuItem?.children || [], menuItem.key);
      if (hasActiveChild) {
        activeAppCode = menuItem.key;
        break;
      }
    }
    return activeAppCode;
  } catch (error) {
    handleError(error, {
      path: PATH,
      name: findActiveAppCodeByChild.name,
      args,
    });
  }
};

/**
 * Retrieves the custom active application code based on the active key and menu items.
 *
 * @param {Object} args - The arguments object.
 * @param {string} args.activeKey - The active key to search for.
 * @param {TMenuItem[]} args.menuItems - The array of menu items to search through.
 * @returns {string | undefined} - The active application code if found, otherwise undefined.
 */
export const getCustomActiveAppCode = (args: {
  activeKey: string;
  menuItems: TMenuItem[];
}): string | undefined => {
  const { activeKey, menuItems } = args;
  let activeAppCode: string | undefined;

  const recursiveCheckHasActiveChild = (items: TMenuItem[]): boolean => {
    let matchedAppCode: boolean = false;

    for (const item of items) {
      if (item.key === activeKey) {
        matchedAppCode = true;
        break;
      }

      if (item?.children && item.children.length) {
        matchedAppCode = recursiveCheckHasActiveChild(item.children);
        if (matchedAppCode) {
          break;
        }
      }
    }

    return matchedAppCode;
  };

  for (const menuItem of menuItems) {
    if (menuItem.key === activeKey) {
      activeAppCode = activeKey;
      break;
    }

    const hasActiveChild = recursiveCheckHasActiveChild(menuItem?.children || []);
    if (hasActiveChild) {
      activeAppCode = menuItem.key;
      break;
    }
  }
  return activeAppCode;
};

/**
 * Render Menu Icon
 * @param {string} iconName - name of icon which is expected to be present in LazyIcon object
 * @returns {JSX.Element} - rendered icon component
 */
export const renderMenuIcon = (iconName: string): JSX.Element => {
  try {
    // const ComponentIcon = iconName && Icons[iconName];
    const ComponentIcon = iconName && Icons[iconName];

    return ComponentIcon ? (
      <Suspense fallback={null}>
        <ComponentIcon className="menu-item__icon" size={30} />
      </Suspense>
    ) : (
      <IconWrapper>{isNil(iconName) ? null : <Icon type={iconName} />}</IconWrapper>
    );
  } catch (error) {
    return <IconWrapper>{isNil(iconName) ? null : <Icon type={iconName} />}</IconWrapper>;
  }
};
