// Libraries
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { isArray, isBoolean, isNil } from 'lodash';
import { Cookies as ReactCookies } from 'react-cookie';

// Utils
import {
  findActiveAppCodeByChild,
  findActiveAppCodeByUrl,
  recursiveGetMenuItemByPermission,
} from '../utils';
import { flatTree, handleError } from '@antscorp/antsomi-ui/es/utils';

// Store
import { useLeftMenuContext } from '../contexts';

// Types
import { LeftMenuProps } from '..';

// Constants
import { DASHBOARD_MODULE_CONFIG } from '../../../template/Layout/constants';

// Hooks
import { usePermission } from './usePermission';
import { useCustomRouter, useDeepCompareEffect } from '@antscorp/antsomi-ui/es/hooks';
import { TFeatureMenu } from '@antscorp/antsomi-ui/es/models/LeftMenu';
import { useNavigatePath } from './useNavigatePath';

interface TState {
  hoverItem: string;
  isExpandMenu: boolean;
  isShowPopover: boolean;
}

const cookies = new ReactCookies();

export const useLeftMenu = (props: LeftMenuProps) => {
  const {
    objectType = DASHBOARD_MODULE_CONFIG.objectType,
    objectId = DASHBOARD_MODULE_CONFIG.objectId,
    isGrouped = DASHBOARD_MODULE_CONFIG.isGrouped,
    appConfig,
    customization,
    onActiveMenuCodeChange,
  } = props;
  const { defaultExpandMenu, type, items, activeKey, onMenuItemClick } = customization || {};

  const mappedActiveKey = useMemo(() => {
    if (type !== 'recommendation') return activeKey;

    switch (activeKey) {
      case 'BUSINESS_OBJECT':
      case 'BUSINESS_OBJECT-APP_API_HUB':
        return 'DATA_OBJECT';
      case 'BUSINESS_OBJECT-PERMISSION_ACCOUNT':
        return 'BUSINESS_OBJECT';
      case 'ANALYSIS':
        return 'SQL_WORKSPACE';
      case 'DATASOURCES-APP_ANTALYSER':
        return 'DATASOURCES-L_REPORTS';
      case 'WEB_PERSONALIZE-JOURNEY':
        return 'WEB_PERSONALIZE';
      default:
        return activeKey;
    }
  }, [activeKey, type]);

  const { pathname, hash, origin } = window.location;

  const { navigate } = useCustomRouter();

  const { isPushDifferentDomain, getPath, navigatePath } = useNavigatePath();
  const activeAppCode = useLeftMenuContext(store => store.activeAppCode);
  const customActiveAppKey = useLeftMenuContext(store => store.customActiveAppKey);
  const setLeftMenuContextState = useLeftMenuContext(store => store.setState);
  const { auth, env } = appConfig || {};

  const timeoutPopover = useRef<any>(null);
  const openLeftMenuCookies = cookies.get('_leftmenu_state');
  const isOpenMenu = !isNil(openLeftMenuCookies) ? Boolean(openLeftMenuCookies) : false;

  const [state, setState] = useState<TState>({
    hoverItem: '',
    isShowPopover: false,
    isExpandMenu: isBoolean(defaultExpandMenu) ? defaultExpandMenu : isOpenMenu,
  });

  const {
    mappingChildrenMenu,
    flattenMenuPermission,
    menuListPermission,
    permissionMenu,
    activeItemPath,
  } = usePermission();

  const onMenuClick = useCallback(
    (key: string, keyPath: string[]) => {
      if (type === 'recommendation') {
        setLeftMenuContextState({ customActiveCurrentKey: key });
      }

      onMenuItemClick?.(key, [...keyPath, customActiveAppKey]);
    },
    [customActiveAppKey, onMenuItemClick, setLeftMenuContextState, type],
  );

  useDeepCompareEffect(() => {
    if (defaultExpandMenu === undefined) {
      try {
        cookies.set('_leftmenu_state', state.isExpandMenu ? 1 : 0, {
          domain: `.${origin.split('.').splice(-2).join('.')}`,
          path: '/',
          sameSite: 'none',
          secure: true,
        });
      } catch (error) {
        handleError({ args: { origin }, error, func: 'useLeftMenu set _leftmenu_state' });
      }
    }
  }, [defaultExpandMenu, origin, state.isExpandMenu]);

  useDeepCompareEffect(() => {
    setLeftMenuContextState({
      appConfig,
      type,
      dashboardParams: { objectId, isGrouped, objectType },
      onMenuClick,
    });
  }, [appConfig, type, isGrouped, objectId, objectType, onMenuClick, setLeftMenuContextState]);

  // Active Custom App Key when active key is changed
  useDeepCompareEffect(() => {
    switch (type) {
      case 'customization':
        setLeftMenuContextState({
          customActiveAppKey:
            findActiveAppCodeByChild({
              activeCurrentKey: activeKey || '',
              menuItems: items || [],
            }) || '',
          customActiveCurrentKey: activeKey,
        });
        break;
      case 'recommendation': {
        setLeftMenuContextState({
          customActiveAppKey:
            findActiveAppCodeByChild({
              activeCurrentKey: mappedActiveKey || '',
              menuItems: mappingChildrenMenu?.map(item => recursiveGetMenuItemByPermission(item)),
              isRecommendation: true,
            }) || '',
          customActiveCurrentKey: mappedActiveKey,
        });
        break;
      }
      default:
        break;
    }
  }, [
    activeKey,
    appConfig,
    items,
    mappedActiveKey,
    mappingChildrenMenu,
    type,
    setLeftMenuContextState,
  ]);

  // Change list menu children when hovering or activating items, giving priority to hovering items
  useDeepCompareEffect(() => {
    const appActiveKey = ['customization', 'recommendation'].includes(`${type}`)
      ? customActiveAppKey
      : activeAppCode;
    switch (type) {
      case 'customization': {
        setLeftMenuContextState({
          customItems: items,
          customMenuChildren: items?.find(item => item.key === appActiveKey)?.children || [],
          customHoverMenuChildren: state.hoverItem
            ? items?.find(item => item.key === state.hoverItem)?.children || []
            : [],
        });
        break;
      }
      case 'recommendation': {
        setLeftMenuContextState({
          menuItems: mappingChildrenMenu,
          appMenuChildren:
            mappingChildrenMenu?.find(item => item.permission_code === appActiveKey)?.children ||
            [],
          appHoverMenuChildren: state.hoverItem
            ? mappingChildrenMenu?.find(item => item.permission_code === state.hoverItem)
                ?.children || []
            : [],
        });
        break;
      }
      default: {
        if (isArray(mappingChildrenMenu)) {
          setLeftMenuContextState({
            menuItems: mappingChildrenMenu,
            appMenuChildren:
              mappingChildrenMenu?.find(item => item.menu_item_code === appActiveKey)?.children ||
              [],
            appHoverMenuChildren: state.hoverItem
              ? mappingChildrenMenu?.find(item => item.menu_item_code === state.hoverItem)
                  ?.children || []
              : [],
          });
        }
        break;
      }
    }
  }, [
    activeAppCode,
    customActiveAppKey,
    items,
    mappingChildrenMenu,
    state.hoverItem,
    type,
    setLeftMenuContextState,
  ]);

  useDeepCompareEffect(() => {
    if (type !== 'customization') {
      const url = `${pathname}${hash}`;
      // Active App Item having code matching url
      const matchAppCode = findActiveAppCodeByUrl({
        url,
        menuItems: mappingChildrenMenu,
        auth,
        env,
      });

      if (matchAppCode && matchAppCode !== activeAppCode) {
        setLeftMenuContextState({ activeAppCode: matchAppCode });
      }
    }
  }, [
    activeAppCode,
    auth,
    hash,
    mappingChildrenMenu,
    pathname,
    env,
    type,
    setLeftMenuContextState,
  ]);

  useEffect(() => {
    if (!['customization', 'recommendation'].includes(`${type}`)) {
      // Callback function using at Layout when url change
      onActiveMenuCodeChange?.(activeItemPath, flattenMenuPermission, menuListPermission);
    }
  }, [activeItemPath, flattenMenuPermission, menuListPermission, onActiveMenuCodeChange, type]);

  useEffect(() => clearTimeout(timeoutPopover?.current), [timeoutPopover]);

  const onShowPopover = useCallback(
    (hoverItem?: string) => {
      if (timeoutPopover) clearTimeout(timeoutPopover?.current);
      if (!state.isShowPopover) setState(prev => ({ ...prev, isShowPopover: true }));
      if (hoverItem) setState(prev => ({ ...prev, hoverItem }));
    },
    [state.isShowPopover],
  );

  const onToggleChildMenu = useCallback(() => {
    setState(prev => ({ ...prev, isExpandMenu: !prev.isExpandMenu }));
  }, []);

  const onMouseEnter = useCallback(
    (key: string) => {
      if (state.hoverItem !== key) {
        onShowPopover(key);
      }
    },
    [state.hoverItem, onShowPopover],
  );

  const onMouseLeave = useCallback(() => {
    timeoutPopover.current = setTimeout(() => {
      setState(prev => ({ ...prev, hoverItem: '', isShowPopover: false }));
    }, 200);
  }, []);

  const onHoverMenuBar = useCallback(() => {
    onMouseEnter(type === 'customization' ? customActiveAppKey : activeAppCode);
  }, [activeAppCode, customActiveAppKey, onMouseEnter, type]);

  const onClickFeatureMenuItem = useCallback(
    (item: TFeatureMenu) => {
      // Destructure permission_code from the item object
      const { permission_code } = item;

      // Initialize navigateInfo object with menu_item_domain and menu_item_path from the item
      let navigateInfo = {
        menu_item_domain: item.menu_item_domain,
        menu_item_path: item.menu_item_path,
      };

      // Check if permission_code exists
      if (permission_code) {
        // Flatten the tree structure of item.children into a single array, filtering out items that have children
        const flattenChildMenu = flatTree(item.children, 'children').filter(item => !item.children);

        // Find the first child menu item that matches the permission_code or fallback to the first item in the array or the original item
        const matchedChildMenu =
          flattenChildMenu?.find(child => child.permission_code === permission_code) ||
          flattenChildMenu?.[0] ||
          item;

        // If a matching child menu item is found, update navigateInfo with its domain and path
        if (matchedChildMenu) {
          navigateInfo = {
            menu_item_domain: matchedChildMenu.menu_item_domain,
            menu_item_path: matchedChildMenu.menu_item_path,
          };
        }
      }

      // Check if navigating to a different domain
      if (!isPushDifferentDomain(navigateInfo.menu_item_domain, navigateInfo.menu_item_path)) {
        // If not, navigate to the path using getPath and return
        return navigate(getPath(navigateInfo.menu_item_path));
      }

      // If navigating to a different domain, use navigatePath with domain and path
      navigatePath(navigateInfo.menu_item_domain, navigateInfo.menu_item_path);
    },
    [getPath, isPushDifferentDomain, navigate, navigatePath], // Dependencies for useCallback
  );

  return {
    state,
    activeAppCode,
    permissionMenu,
    customActiveAppKey,
    onToggleChildMenu,
    onMouseEnter,
    onMouseLeave,
    onHoverMenuBar,
    onShowPopover,
    onClickFeatureMenuItem,
  };
};
