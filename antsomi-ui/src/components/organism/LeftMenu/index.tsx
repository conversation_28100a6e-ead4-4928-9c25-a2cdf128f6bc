/* eslint-disable react/function-component-definition */
// Libraries
import React, { memo, useRef } from 'react';
import { StoreApi } from 'zustand';
import isEqual from 'react-fast-compare';
import { CookiesProvider } from 'react-cookie';

// Providers
import { LeftMenuProvider } from './providers';

// Store
import { LeftMenuState, createLeftMenuStore } from './stores';

// Types
import { LeftMenuComponent } from './LeftMenu';
import { AppConfigProviderProps } from '@antscorp/antsomi-ui/es/providers';
import { FeatureMenuPermission, TFeatureMenu } from '@antscorp/antsomi-ui/es/models/LeftMenu';
import { TLeftMenuType, TMenuItem } from './types';

export interface LeftMenuProps {
  show?: boolean;
  appConfig?: AppConfigProviderProps;
  objectType?: string;
  objectId?: number;
  isGrouped?: boolean;
  style?: React.CSSProperties;
  className?: string;
  showLogo?: boolean;
  customization?: {
    type?: TLeftMenuType;
    showButtonExpand?: boolean;
    showMenuPopover?: boolean;
    items?: TMenuItem[];
    activeKey?: string;
    defaultExpandMenu?: boolean;
    onMenuItemClick?: (key: string, keyPath: string[]) => void;
  };
  onClickLogo?: () => void;
  onActiveMenuCodeChange?: (
    activeItemPath: TFeatureMenu[],
    flattenPermissionList?: FeatureMenuPermission[],
    menuListPermission?: FeatureMenuPermission[],
  ) => void;
}

export const LeftMenu = memo(
  (props: LeftMenuProps) => {
    const storeRef = useRef<StoreApi<LeftMenuState>>();
    if (!storeRef.current) {
      storeRef.current = createLeftMenuStore(props);
    }

    if (!storeRef.current) return null;

    return (
      <LeftMenuProvider store={storeRef.current}>
        <CookiesProvider>
          <LeftMenuComponent {...props} />
        </CookiesProvider>
      </LeftMenuProvider>
    );
  },
  (prevProps, nextProps) => isEqual(prevProps, nextProps),
);
