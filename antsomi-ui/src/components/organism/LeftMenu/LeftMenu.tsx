// Libraries
import React, { memo, ReactNode, startTransition, useCallback, useMemo } from 'react';
import Icon from '@antscorp/icons';
import { Typography } from 'antd';
import classNames from 'classnames';
import { isEmpty, isNil } from 'lodash';
import isEqual from 'react-fast-compare';

// Styled
import {
  ChildMenuWrapper,
  ExpandWrapper,
  FeatureMenu,
  FeatureMenuItem,
  LeftMenuNav,
  LeftMenuNavWrapper,
  PopoverWrapper,
  NavLogoWrapper,
  FeatureMenuWrapper,
  IconWrapper,
} from './styled';

// Constants
import { APP_KEYS } from './constants';

// Components
import { CustomMenu, CommonMenu, HomeMenu } from './components';

// Hooks
import { useLeftMenu } from './hooks';

// Types
import { TFeatureMenu } from '@antscorp/antsomi-ui/es/models/LeftMenu/FeatureMenu';
import { LeftMenuProps } from '../../..';
import { TMenuItem } from './types';

// Contexts
import { useLeftMenuContext } from './contexts';
import { renderMenuIcon } from './utils';

const SubLogoAntsomi = 'https://st-media-template.antsomi.com/icons/antsomi/antsomi.png';
const homePermissionCode = 'DASHBOARD';

export const LeftMenuComponent: React.FC<LeftMenuProps> = memo(
  props => {
    const { show = true, style, className, customization, showLogo = true, onClickLogo } = props;
    const {
      showMenuPopover = true,
      showButtonExpand = true,
      type,
      items = [],
      onMenuItemClick,
    } = customization || {};

    const appHoverMenuChildren = useLeftMenuContext(store => store.appHoverMenuChildren);
    const customHoverMenuChildren = useLeftMenuContext(store => store.customHoverMenuChildren);

    const hoverMenuChildren = useMemo(() => {
      if (type === 'customization') return customHoverMenuChildren;
      return appHoverMenuChildren;
    }, [appHoverMenuChildren, customHoverMenuChildren, type]);

    const {
      state,
      activeAppCode,
      permissionMenu,
      customActiveAppKey,
      onToggleChildMenu,
      onMouseEnter,
      onMouseLeave,
      onHoverMenuBar,
      onShowPopover,
      onClickFeatureMenuItem,
    } = useLeftMenu(props);

    const childActiveMenu = useMemo(() => {
      const appActiveKey = ['customization', 'recommendation'].includes(`${type}`)
        ? customActiveAppKey
        : activeAppCode;

      if (type === 'customization') return <CustomMenu />;

      switch (appActiveKey) {
        case type === 'recommendation' ? homePermissionCode : APP_KEYS.HOME:
          return <HomeMenu />;
        default:
          return <CommonMenu />;
      }
    }, [activeAppCode, customActiveAppKey, type]);

    const childHoverMenu = useMemo(() => {
      const appActiveKey = state.hoverItem;

      if (type === 'customization') return <CustomMenu isHover />;
      switch (appActiveKey) {
        case type === 'recommendation' ? homePermissionCode : APP_KEYS.HOME:
          return <HomeMenu isHover={type !== 'recommendation'} />;
        default:
          return <CommonMenu isHover={type !== 'recommendation'} />;
      }
    }, [state.hoverItem, type]);

    const renderFeatureMenuItem = useCallback(
      (item: TFeatureMenu) => {
        const { menu_item_name, menu_item, icon_name, component_icon_name, menu_item_code } = item;
        const isActive = activeAppCode === menu_item_code;
        const isHover = state.hoverItem === menu_item_code;
        let Label: ReactNode = null;

        Label = (
          <li role="menuitem" className={classNames({ isActive, isHover })}>
            {renderMenuIcon(component_icon_name || icon_name || '')}
            <Typography.Text>{menu_item_name}</Typography.Text>
            {showMenuPopover && <div className="popup-triangle" />}
          </li>
        );

        return (
          <FeatureMenuItem
            key={menu_item}
            onClick={() => onClickFeatureMenuItem(item)}
            onMouseEnter={() => onMouseEnter(menu_item_code)}
          >
            {Label}
          </FeatureMenuItem>
        );
      },
      [activeAppCode, state.hoverItem, showMenuPopover, onMouseEnter, onClickFeatureMenuItem],
    );

    const renderRecommendationMenuItem = useCallback(
      (item: TFeatureMenu) => {
        const { menu_item_name, menu_item, component_icon_name, icon_name, permission_code } = item;
        const isActive = customActiveAppKey === permission_code;
        const isHover = state.hoverItem === permission_code;

        const renderLabel = () => (
          <li
            role="menuitem"
            className={classNames({ isActive, isHover })}
            onClick={() =>
              onMenuItemClick?.(permission_code || 'null', [permission_code || 'null'])
            }
          >
            {renderMenuIcon(component_icon_name || icon_name || '')}
            <Typography.Text>{menu_item_name}</Typography.Text>
          </li>
        );

        return (
          <FeatureMenuItem
            key={menu_item}
            onMouseEnter={() => onMouseEnter(permission_code || 'null')}
          >
            {renderLabel()}
          </FeatureMenuItem>
        );
      },
      [customActiveAppKey, onMenuItemClick, onMouseEnter, state.hoverItem],
    );

    const renderCustomizedMenuItems = useCallback(
      (items: TMenuItem[]) =>
        items?.map(item => {
          const { key, icon, label } = item;
          const isActive = key === customActiveAppKey;
          const isHover = key === state.hoverItem;

          return (
            <FeatureMenuItem
              key={key}
              onClick={() => onMenuItemClick?.(key, [key])}
              onMouseEnter={() => onMouseEnter(key)}
            >
              <li role="menuitem" className={classNames({ isActive, isHover })}>
                {renderMenuIcon(icon || '')}
                <Typography.Text>{label}</Typography.Text>
                {showMenuPopover && <div className="popup-triangle" />}
              </li>
            </FeatureMenuItem>
          );
        }),
      [customActiveAppKey, state.hoverItem, showMenuPopover, onMenuItemClick, onMouseEnter],
    );

    const renderAppMenuItems = useCallback(
      () =>
        type === 'customization'
          ? renderCustomizedMenuItems(items)
          : permissionMenu
              ?.filter(item => item.menu_item_code !== APP_KEYS.SETTINGS)
              ?.map(item =>
                type === 'recommendation'
                  ? renderRecommendationMenuItem(item)
                  : renderFeatureMenuItem(item),
              ),
      [
        items,
        permissionMenu,
        renderCustomizedMenuItems,
        renderFeatureMenuItem,
        renderRecommendationMenuItem,
        type,
      ],
    );

    const renderSettings = () => {
      if (type !== 'customization') {
        const settingApp = permissionMenu?.find(item => item.menu_item_code === APP_KEYS.SETTINGS);
        if (settingApp)
          return type === 'recommendation'
            ? renderRecommendationMenuItem(settingApp)
            : renderFeatureMenuItem(settingApp);
      }
      return null;
    };

    return (
      <LeftMenuNavWrapper
        $show={show}
        style={style}
        $isExpandMenu={state.isExpandMenu}
        className={className}
      >
        <LeftMenuNav className="left-menu-nav">
          <FeatureMenuWrapper
            vertical
            onMouseEnter={() => onShowPopover()}
            onMouseLeave={onMouseLeave}
          >
            {showLogo && (
              <NavLogoWrapper
                align="center"
                justify="center"
                onMouseEnter={onHoverMenuBar}
                onClick={onClickLogo}
              >
                <div className="image-wrapper">
                  <img src={SubLogoAntsomi} alt="Antsomi sub logo" />
                </div>
              </NavLogoWrapper>
            )}
            <FeatureMenu role="menu" className="antsomi-scroll-box">
              <div className="menu-content scroll-content">{renderAppMenuItems()}</div>
              <div className="nav-blank" onMouseEnter={onHoverMenuBar} />
              {showMenuPopover && (
                <PopoverWrapper
                  show={state.isShowPopover && !isEmpty(hoverMenuChildren)}
                  hasMarginTop={type !== 'recommendation'}
                  className="antsomi-scroll-box antsomi-child-menu-popover"
                >
                  <div className="scroll-content" style={{ width: '200px', maxHeight: '100%' }}>
                    {childHoverMenu}
                  </div>
                </PopoverWrapper>
              )}
            </FeatureMenu>
            <div style={{ flexShrink: 0 }}>{renderSettings()}</div>
          </FeatureMenuWrapper>
          {showButtonExpand && (
            <ExpandWrapper onMouseEnter={onMouseLeave}>
              <Icon
                type="icon-ants-expand-more"
                style={{
                  cursor: 'pointer',
                  fontSize: '20px',
                  transform: `rotate(${state.isExpandMenu ? '90deg' : '270deg'})`,
                }}
                onClick={onToggleChildMenu}
              />
            </ExpandWrapper>
          )}
        </LeftMenuNav>
        <ChildMenuWrapper
          isExpand={state.isExpandMenu}
          isMarginTop={showLogo}
          className="antsomi-scroll-box"
        >
          <div className="scroll-content" style={{ width: '200px', maxHeight: '100%' }}>
            {childActiveMenu}
          </div>
        </ChildMenuWrapper>
      </LeftMenuNavWrapper>
    );
  },
  (prevProps, nextProps) => isEqual(prevProps, nextProps),
);
