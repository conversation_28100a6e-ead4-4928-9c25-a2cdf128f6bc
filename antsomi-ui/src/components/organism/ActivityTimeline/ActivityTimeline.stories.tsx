import React from 'react';
import { Meta, StoryObj } from '@storybook/react';
import { ActivityTimeline } from './ActivityTimeline';
import TIMELINE_MOCK from './__mocks__/timeline.json';
import EVENT_TRACKING_MOCK from './__mocks__/event_tracking.json';
import { getPortalTimeZone } from '../../../utils/portal';

const meta = {
  component: ActivityTimeline,
  title: 'Molecules/ActivityTimeline',
} satisfies Meta<typeof ActivityTimeline>;

export default meta;

type Story = StoryObj<typeof ActivityTimeline>;

export const Default: Story = {
  render: () => (
    <ActivityTimeline
      header={<div>HEADER</div>}
      timelines={TIMELINE_MOCK.default}
      eventTracking={EVENT_TRACKING_MOCK}
      onFetchMore={() => {
        console.log('fetch more');
      }}
    />
  ),
  args: {},
  parameters: {},
};

export const SeachProductEventWithSlide: Story = {
  render: () => (
    <>
      <h2 style={{ marginBottom: 10 }}>Timezone: {getPortalTimeZone()}</h2>

      <ActivityTimeline
        timelines={TIMELINE_MOCK.whatsapp_promotion_code_send}
        eventTracking={EVENT_TRACKING_MOCK}
      />
    </>
  ),
};

export const Empty: Story = {
  render: () => <ActivityTimeline />,
};
