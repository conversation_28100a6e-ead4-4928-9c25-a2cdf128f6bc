/* eslint-disable react/function-component-definition */
// Libraries
import React from 'react';

// Types
import { DataTableProps, TGridViewKeys } from './types';

// Hooks
import { useDataTable } from './hooks';
import { useDeepCompareMemo } from '@antscorp/antsomi-ui/es/hooks';

// Styled
import { DataTableContent, DataTableWrapper } from './styled';

// Constants
import { GRID_VIEW_KEYS } from './constants';

// Components
import { Table, Pagination, Toolbar, GridView } from './components';

// Providers
import { DataTableProvider } from './providers/DataTableProvider';
import { AntdTableConfigProvider } from './providers';

// Store
import { useDataTableStore } from './hooks/useDataTableStore';
import clsx from 'clsx';

const { GRID, TABLE } = GRID_VIEW_KEYS;

const RENDER_VIEW_COMPONENTS = {
  [GRID]: <GridView />,
  [TABLE]: <Table />,
};

function DataTableComponent<TTableDataType>(props: DataTableProps<TTableDataType>) {
  const { contentRender, className } = props || {};

  // Hooks
  const { pagination, toolbar, setPagination } = useDataTable(props);
  const { show: showToolbar = true, actionButtons } = toolbar || {};
  const { GRID_VIEW } = actionButtons || {};

  const renderModeView = useDeepCompareMemo(() => {
    // Check if has content render then return this content
    if (contentRender) {
      return contentRender();
    }

    const modeView = GRID_VIEW?.mode || 'TABLE';
    const renderContentWrapper = (key: TGridViewKeys = 'TABLE', children: React.ReactNode) => (
      <DataTableContent
        style={{
          display: key === modeView ? 'flex' : 'none',
        }}
      >
        {children}
      </DataTableContent>
    );

    return Object.keys(RENDER_VIEW_COMPONENTS).map(key => (
      <React.Fragment key={key}>
        {renderContentWrapper(key as any, RENDER_VIEW_COMPONENTS[key])}
      </React.Fragment>
    ));
  }, [GRID_VIEW?.mode, contentRender]);

  return (
    <DataTableWrapper className={clsx(className, 'antsomi-data-table-root')}>
      {!!showToolbar && <Toolbar />}
      {renderModeView}
      <Pagination
        id="data-table-pagination"
        {...pagination}
        onChange={payload => {
          setPagination(payload);

          pagination?.onChange?.(payload);
        }}
      />
    </DataTableWrapper>
  );
}

export function DataTable<TTableDataType>(props: DataTableProps<TTableDataType>) {
  const { store, ...restOfProps } = props;

  const defaultStore = useDataTableStore(restOfProps);

  return (
    <AntdTableConfigProvider>
      <DataTableProvider store={store || defaultStore}>
        <DataTableComponent {...restOfProps} />
      </DataTableProvider>
    </AntdTableConfigProvider>
  );
}

DataTable.useDataTableStore = useDataTableStore;
