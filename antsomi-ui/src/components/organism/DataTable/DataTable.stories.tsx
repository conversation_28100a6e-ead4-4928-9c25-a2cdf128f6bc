// Libraries
import React from 'react';
import { <PERSON>a, StoryObj } from '@storybook/react';

// Components
import {
  Button,
  DataTable,
  Flex,
  Select,
  Typography,
  /* DataTableProps, */ useDataTableListing,
} from '../..';
import styled from 'styled-components';
import { Table } from '../Table';

const meta: Meta<typeof DataTable> = {
  component: DataTable,
  title: 'Organism/DataTable',
  argTypes: {},
  parameters: {
    docs: {
      description: {
        component: 'This component supports displaying data in a table format',
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof DataTable>;

// Styled
const TableWrapper = styled.div`
  height: 80vh;
`;

// Types
type SurveyProject = {
  survey_id: number;
  network_id: number;
  survey_name: string;
  parent_id: number | null;
  c_user_id: number;
  u_user_id: number;
  status: number;
  is_starred: number;
  ctime: string;
  utime: string;
  user_id: number;
  type: number;
  latest_publish_version: number;
  responses: string | null;
};

const surveyConfig = {
  //  NOTES: This is no need pass auth props when had
  // Provider wrapper
  env: 'production',
  auth: {
    token: '5474r2x214z284d4w2b4y4g5d4w5o216d494k413g5l5',
    userId: '**********',
    accountId: '**********',
    portalId: *********,
  },
  api: {
    listing: {
      url: 'https://survey.antsomi.com/api/v1/survey/performance',
    },
    filter: {
      url: 'https://column.ants.tech/api/filter',
    },
    column: {
      url: 'https://column.ants.tech/api/column',
    },
    search: {
      url: `https://survey.antsomi.com/api/v1/survey/performance`,
      params: {
        limit: 100,
        page: 1,
      },
    },
    matchesAny: {
      general: {
        url: 'https://survey.antsomi.com/api/v1/survey/filter/get-value-matches-any',
        params(filterItem) {
          return {
            page: 1,
            limit: 20,
            metric_code: filterItem?.column?.toString().toLowerCase(),
          };
        },
      },
      survey_id: {
        url: 'https://survey.antsomi.com/api/v1/survey/performance',
        params() {
          return {
            page: 1,
            limit: 20,
          };
        },
      },
    },
  },
  object: {
    type: 5, // Survey,
    objectId: -1, // NOTES: will note detail when to use objectId, current is no need
  },
};

// Renders
const RenderSurveyDataTable: React.FC<any> = () => {
  // Hooks
  const { filter, modifyColumn, pagination, table, search } = useDataTableListing<
    SurveyProject,
    SurveyProject
  >({
    name: 'survey-project-test-2',
    config: surveyConfig,
    search: {
      itemMapKeys: {
        id: 'survey_id',
        label: 'survey_name',
      },
      isClientSearch: false,
      addFilterInfo: {
        metricId: 'survey_name',
        operator: 'contain',
      },
      searchItemProps: {},
      link(record) {
        return `/survey/${record.survey_id}`;
      },
      formatData(response) {
        return response?.body || [];
      },
    },
    queryOptions: {
      getTableListing: {
        refetchOnMount: 'always',
      },
    },
    filter: {
      externalFilters: [],
      matchesAny: {
        general: {
          formatData(response) {
            return response?.rows?.map(record => ({
              key: record.value,
              title: record.label,
            }));
          },
        },
        survey_id: {
          formatData(response: { body: SurveyProject[] }) {
            return response?.body?.map(record => ({
              key: record.survey_id,
              title: record.survey_id,
            }));
          },
          total(response) {
            return response?.total || 0;
          },
        },
      },
    },
    table: {
      mainColumnKey: 'survey_id',
      // expandColumnKey: 'survey_name',
      onRow: record => ({
        onClick: () => {
          console.log('record', record);
        },
      }),
    },
  });

  return (
    <TableWrapper>
      <DataTable<SurveyProject>
        table={{
          ...table,
        }}
        filter={filter}
        toolbar={{
          addButton: {
            onClick: () => {
              console.log('hello world');
            },
          },
          actionButtons: {
            SEARCH: search,
            COLUMN: modifyColumn,
          },
          selectedRowButtons: {
            ACTION: {
              label: 'Action',
              dropdownProps: {
                menu: {
                  items: [
                    {
                      label: 'Action 1',
                      key: '1',
                    },
                  ],
                },
              },
            },
          },
        }}
        pagination={pagination}
      />
    </TableWrapper>
  );
};

const RenderNestedTable: React.FC<any> = () => {
  // Hooks
  const { filter, modifyColumn, pagination, table, search } = useDataTableListing<
    SurveyProject,
    SurveyProject
  >({
    name: 'survey-project',
    config: surveyConfig,
    search: {
      itemMapKeys: {
        id: 'survey_id',
        label: 'survey_name',
      },
      isClientSearch: false,
      searchItemProps: {},
    },
    queryOptions: {
      getTableListing: {
        refetchOnMount: 'always',
      },
    },
    filter: {
      externalFilters: [],
      matchesAny: {
        general: {
          formatData(response) {
            return response?.rows?.map(record => ({
              key: record.value,
              title: record.label,
            }));
          },
        },
        survey_id: {
          formatData(response: { body: SurveyProject[] }) {
            return response?.body?.map(record => ({
              key: record.survey_id,
              title: record.survey_id,
            }));
          },
          total(response) {
            return response?.total || 0;
          },
        },
      },
    },
    table: {
      mainColumnKey: 'survey_id',
      expandColumnKey: 'survey_name',
      onRow: record => ({
        onClick: () => {
          console.log('record', record);
        },
      }),
      expandable: {
        onExpand(expanded, record) {
          console.log('onExpand', expanded, record);
        },
        expandedRowRender: record => (
          <Table
            columns={[
              {
                key: 'action',
                dataIndex: 'action',
                title: 'Action',
              },
              {
                key: 'created_on',
                dataIndex: 'created_on',
                title: 'Created on',
              },
              {
                key: 'error_info',
                dataIndex: 'error_info',
                title: 'Error Info',
              },
              {
                key: 'start_time',
                dataIndex: 'start_time',
                title: 'Start Time',
              },
            ]}
            dataSource={[
              {
                key: 1,
                action: `Add Data (40517165) of ${record.survey_name}`,
                created_on: 'Success',
                error_info: 'Error Info 1',
                start_time: '07/06/2016 11:13:27',
              },
            ]}
            bordered
            pagination={false}
          />
        ),
      },
    },
  });

  return (
    <TableWrapper>
      <DataTable<SurveyProject>
        table={{
          ...table,
        }}
        filter={filter}
        toolbar={{
          actionButtons: {
            SEARCH: search,
            COLUMN: modifyColumn,
          },
          selectedRowButtons: {
            ACTION: {
              label: 'Action',
              dropdownProps: {
                menu: {
                  items: [
                    {
                      label: 'Action 1',
                      key: '1',
                    },
                  ],
                },
              },
            },
          },
        }}
        pagination={pagination}
      />
    </TableWrapper>
  );
};

const RenderGridViewMode: React.FC<any> = () => {
  // Hooks
  const { filter, modifyColumn, pagination, table, search, gridView } = useDataTableListing<
    SurveyProject,
    SurveyProject
  >({
    name: 'survey-project-2',
    config: surveyConfig,
    search: {
      itemMapKeys: {
        id: 'survey_id',
        label: 'survey_name',
      },
      isClientSearch: false,
      searchItemProps: {},
    },
    queryOptions: {
      getTableListing: {
        refetchOnMount: 'always',
      },
    },
    filter: {
      externalFilters: [],
      matchesAny: {
        general: {
          formatData(response) {
            return response?.rows?.map(record => ({
              key: record.value,
              title: record.label,
            }));
          },
        },
        survey_id: {
          formatData(response: { body: SurveyProject[] }) {
            return response?.body?.map(record => ({
              key: record.survey_id,
              title: record.survey_id,
            }));
          },
          total(response) {
            return response?.total || 0;
          },
        },
      },
    },
    table: {
      mainColumnKey: 'survey_id',
    },
    gridView: {
      itemMapKeys: {
        id: 'survey_id',
        name: 'survey_name',
        thumbnail: 'survey_name',
      },
      name(record) {
        return record.survey_name;
      },
      thumbnail(record) {
        return `https://picsum.photos/200/300?random=${record.survey_id}`;
      },
      itemProps: {
        removable: true,
        showButton: true,
        onRemove(record) {
          console.log('🚀 ~ record:', record);
        },
        removeModalProps: {
          title: 'Remove Survey',
          content: (
            <div>
              <p>Are you sure you want to remove the survey?</p>
              <p>This action can not be undone.</p>
            </div>
          ),
        },
        buttonProps: {
          text: 'View',
          onClick(record) {
            console.log('🚀 ~ record:', record);
          },
        },
      },
    },
  });

  return (
    <TableWrapper>
      <DataTable<SurveyProject>
        table={{
          ...table,
        }}
        filter={filter}
        toolbar={{
          actionButtons: {
            SEARCH: search,
            COLUMN: modifyColumn,
            GRID_VIEW: gridView,
          },
          selectedRowButtons: {
            ACTION: {
              label: 'Action',
              dropdownProps: {
                menu: {
                  items: [
                    {
                      label: 'Action 1',
                      key: '1',
                    },
                  ],
                },
              },
            },
          },
        }}
        pagination={pagination}
      />
    </TableWrapper>
  );
};

const RenderCustomContentRender: React.FC<any> = () => {
  // Hooks
  const { filter, modifyColumn, pagination, table, search, gridView } = useDataTableListing<
    SurveyProject,
    SurveyProject
  >({
    name: 'content-render',
    config: surveyConfig,
    search: {
      itemMapKeys: {
        id: 'survey_id',
        label: 'survey_name',
      },
      isClientSearch: false,
      searchItemProps: {},
    },
    queryOptions: {
      getTableListing: {
        refetchOnMount: 'always',
      },
    },
    filter: {
      externalFilters: [],
      matchesAny: {
        general: {
          formatData(response) {
            return response?.rows?.map(record => ({
              key: record.value,
              title: record.label,
            }));
          },
        },
        survey_id: {
          formatData(response: { body: SurveyProject[] }) {
            return response?.body?.map(record => ({
              key: record.survey_id,
              title: record.survey_id,
            }));
          },
          total(response) {
            return response?.total || 0;
          },
        },
      },
    },
    table: {
      mainColumnKey: 'survey_id',
    },
    gridView: {
      itemMapKeys: {
        id: 'survey_id',
        name: 'survey_name',
        thumbnail: 'survey_name',
      },
      name(record) {
        return record.survey_name;
      },
      thumbnail(record) {
        return `https://picsum.photos/200/300?random=${record.survey_id}`;
      },
      itemProps: {
        showButton: true,
        buttonProps: {
          text: 'View',
          onClick(record) {
            console.log('🚀 ~ record:', record);
          },
        },
      },
    },
  });

  const contentRender = () => {
    const { columns, dataSource } = table;
    const { page, pageSize, total } = pagination;

    return (
      <Flex vertical gap={10}>
        <Typography.Text strong>
          This is a custom content render, here is some information you can use to custom render
        </Typography.Text>

        <Typography.Text>Pagination: </Typography.Text>
        <Typography.Text>
          {page} - {pageSize} - {total}
        </Typography.Text>

        <Typography.Text>Columns: </Typography.Text>
        <Typography.Text>{columns?.map(column => column?.key).join(', ')}</Typography.Text>

        <Typography.Text>Data Source: </Typography.Text>
        <Typography.Text>
          {JSON.stringify(dataSource?.map(record => record?.survey_id))}
        </Typography.Text>
      </Flex>
    );
  };

  return (
    <TableWrapper>
      <DataTable<SurveyProject>
        table={{
          ...table,
        }}
        filter={filter}
        toolbar={{
          actionButtons: {
            SEARCH: search,
            COLUMN: modifyColumn,
            GRID_VIEW: gridView,
          },
          selectedRowButtons: {
            ACTION: {
              label: 'Action',
              dropdownProps: {
                menu: {
                  items: [
                    {
                      label: 'Action 1',
                      key: '1',
                    },
                  ],
                },
              },
            },
          },
        }}
        pagination={pagination}
        contentRender={contentRender}
      />
    </TableWrapper>
  );
};

const RenderGroupByColumn: React.FC<any> = () => {
  // Hooks
  const { filter, modifyColumn, pagination, table, search, groupBy } = useDataTableListing<
    SurveyProject,
    SurveyProject
  >({
    name: 'group-by-data-table',
    config: surveyConfig,
    search: {
      itemMapKeys: {
        id: 'survey_id',
        label: 'survey_name',
      },
      isClientSearch: false,
      searchItemProps: {},
    },
    queryOptions: {
      getTableListing: {
        refetchOnMount: 'always',
      },
    },
    filter: {
      externalFilters: [],
      matchesAny: {
        general: {
          formatData(response) {
            return response?.rows?.map(record => ({
              key: record.value,
              title: record.label,
            }));
          },
        },
        survey_id: {
          formatData(response: { body: SurveyProject[] }) {
            return response?.body?.map(record => ({
              key: record.survey_id,
              title: record.survey_id,
            }));
          },
          total(response) {
            return response?.total || 0;
          },
        },
      },
    },
    table: {
      mainColumnKey: 'survey_id',
    },
    groupBy: {
      columnKeys: ['c_user_id', 'u_user_id'],
      defaultSelectedKey: 'c_user_id',
    },
  });

  return (
    <TableWrapper>
      <DataTable<SurveyProject>
        table={{
          ...table,
          rowSelection: undefined,
        }}
        filter={filter}
        toolbar={{
          actionButtons: {
            SEARCH: search,
            COLUMN: modifyColumn,
            GROUP_BY: groupBy,
          },
          selectedRowButtons: {
            ACTION: {
              label: 'Action',
              dropdownProps: {
                menu: {
                  items: [
                    {
                      label: 'Action 1',
                      key: '1',
                    },
                  ],
                },
              },
            },
          },
        }}
        pagination={pagination}
      />
    </TableWrapper>
  );
};

const RenderExtraRightContentOfToolbar: React.FC<any> = () => {
  // Hooks
  const { filter, modifyColumn, pagination, table, search } = useDataTableListing<
    SurveyProject,
    SurveyProject
  >({
    name: 'extra-right-content-data-table',
    config: surveyConfig,
    search: {
      itemMapKeys: {
        id: 'survey_id',
        label: 'survey_name',
      },
      isClientSearch: false,
      searchItemProps: {},
    },
    queryOptions: {
      getTableListing: {
        refetchOnMount: 'always',
      },
    },
    filter: {
      externalFilters: [],
      matchesAny: {
        general: {
          formatData(response) {
            return response?.rows?.map(record => ({
              key: record.value,
              title: record.label,
            }));
          },
        },
        survey_id: {
          formatData(response: { body: SurveyProject[] }) {
            return response?.body?.map(record => ({
              key: record.survey_id,
              title: record.survey_id,
            }));
          },
          total(response) {
            return response?.total || 0;
          },
        },
      },
    },
    table: {
      mainColumnKey: 'survey_id',
    },
  });

  return (
    <TableWrapper>
      <DataTable<SurveyProject>
        table={{
          ...table,
          rowSelection: undefined,
        }}
        filter={filter}
        toolbar={{
          rightContent: (
            <Flex align="center" gap={10} style={{ marginRight: 10 }}>
              <Button type="default">Today</Button>
              <Select
                options={[
                  { label: 'Month', value: 'month' },
                  { label: 'Year', value: 'year' },
                ]}
                defaultValue="year"
                style={{ width: 50 }}
              />
            </Flex>
          ),
          actionButtons: {
            SEARCH: search,
            COLUMN: modifyColumn,
          },
          selectedRowButtons: {
            ACTION: {
              label: 'Action',
              dropdownProps: {
                menu: {
                  items: [
                    {
                      label: 'Action 1',
                      key: '1',
                    },
                  ],
                },
              },
            },
          },
        }}
        pagination={pagination}
      />
    </TableWrapper>
  );
};

const RenderWithDefaultFilterAndNoCache: React.FC<any> = () => {
  // Hooks
  const { filter, modifyColumn, pagination, table, search } = useDataTableListing<
    SurveyProject,
    SurveyProject
  >({
    name: 'survey-project-no-cache',
    config: { ...surveyConfig, disabledCache: true },
    search: {
      itemMapKeys: {
        id: 'survey_id',
        label: 'survey_name',
      },
      isClientSearch: false,
      addFilterInfo: {
        metricId: 'survey_name',
        operator: 'contain',
      },
      searchItemProps: {},
      link(record) {
        return `/survey/${record.survey_id}`;
      },
      formatData(response) {
        return response?.body || [];
      },
    },
    queryOptions: {
      getTableListing: {
        refetchOnMount: 'always',
      },
    },
    filter: {
      externalFilters: [],
      defaultFilters: [
        {
          column: 'SURVEY_NAME',
          operator: 'contains',
          value: 'vi',
          dataType: 'string',
        },
      ],
      matchesAny: {
        general: {
          formatData(response) {
            return response?.rows?.map(record => ({
              key: record.value,
              title: record.label,
            }));
          },
        },
        survey_id: {
          formatData(response: { body: SurveyProject[] }) {
            return response?.body?.map(record => ({
              key: record.survey_id,
              title: record.survey_id,
            }));
          },
          total(response) {
            return response?.total || 0;
          },
        },
      },
    },
    table: {
      mainColumnKey: 'survey_id',
    },
  });

  return (
    <TableWrapper>
      <DataTable<SurveyProject>
        table={{
          ...table,
        }}
        filter={filter}
        toolbar={{
          actionButtons: {
            SEARCH: search,
            COLUMN: modifyColumn,
          },
          selectedRowButtons: {
            ACTION: {
              label: 'Action',
              dropdownProps: {
                menu: {
                  items: [
                    {
                      label: 'Action 1',
                      key: '1',
                    },
                  ],
                },
              },
            },
          },
        }}
        pagination={pagination}
      />
    </TableWrapper>
  );
};

export const SurveyDataTable: Story = {
  render: args => <RenderSurveyDataTable {...args} />,
  parameters: {
    docs: {
      source: {
        state: 'open',
        type: 'code',
        code: `
// Libraries
import React from 'react';

// Components
import { DataTable, useDataTableListing } from '@antscorp/antsomi-ui';

// Styled
import { TableTestWrapper } from './styled';

// Types
type SurveyProject = {
  survey_id: number;
  network_id: number;
  survey_name: string;
  parent_id: number | null;
  c_user_id: number;
  u_user_id: number;
  status: number;
  is_starred: number;
  ctime: string;
  utime: string;
  user_id: number;
  type: number;
  latest_publish_version: number;
  responses: string | null;
};

export const DataTableTest = () => {
  // Hooks
  const { filter, modifyColumn, pagination, table, search } = useDataTableListing<
    SurveyProject,
    SurveyProject
  >({
    name: 'survey-project',
    config: {
      //  NOTES: This is no need pass auth props when had
      // Provider wrapper
      env: 'development',
      auth: {
        token: '5474r2x214z284d4w2b4y4z594l534v5m4j4t2f4n5m5',
        userId: '**********',
        accountId: '**********',
        portalId: 33167,
      },
      api: {
        listing: {
          url: 'https://sandbox-survey.antsomi.com/api/v1/survey/performance',
        },
        filter: {
          url: 'https://column.ants.tech/api/filter',
        },
        column: {
          url: 'https://column.ants.tech/api/column',
        },
        search: {
          url: 'https://sandbox-survey.antsomi.com/api/v1/survey/performance',
          params: {
            limit: 100,
            page: 1,
          },
        },
        matchesAny: {
          general: {
            url: 'https://sandbox-survey.antsomi.com/api/v1/survey/filter/get-value-matches-any',
            params(filterItem) {
              return {
                metric_code: filterItem?.column?.toString().toLowerCase(),
              };
            },
          },
          survey_id: {
            url: 'https://sandbox-survey.antsomi.com/api/v1/survey/filter/get-value-matches-any',
            params(filterItem) {
              return {
                test: 'survey_id',
                metric_code: filterItem?.column?.toString().toLowerCase(),
              };
            },
          },
        },
      },
      object: {
        type: 5, // Survey,
        objectId: -1, // NOTES: will note detail when to use objectId, current is no need
      },
    },
    search: {
      itemMapKeys: {
        id: 'survey_id',
        label: 'survey_name',
      },
      isClientSearch: false,
      searchItemProps: {
        style: {
          color: 'red',
        },
      },
      formatData(response) {
        return response?.body || [];
      },
    },
    queryOptions: {
      getTableListing: {
        refetchOnMount: 'always',
      },
    },
    filter: {
      matchesAny: {
        general: {
          formatData(response) {
            return response?.rows.map(record => ({
              key: record.value,
              title: record.label,
            }));
          },
        },
        survey_id: {
          formatData(response) {
            return response?.rows.map(record => ({
              key: record.value,
              title: record.value,
            }));
          },
        },
      },
    },
  });

  return (
    <TableTestWrapper>
      <DataTable<SurveyProject>
        table={table}
        filter={filter}
        toolbar={{
          actionButtons: {
            SEARCH: search,
            COLUMN: modifyColumn,
          },
          selectedRowButtons: {
            ACTION: {
              label: 'Action',
              dropdownProps: {
                menu: {
                  items: [
                    {
                      label: 'Action 1',
                      key: '1',
                    },
                  ],
                },
              },
            },
          },
        }}
        pagination={pagination}
      />
    </TableTestWrapper>
  );
};
        `,
      },
      description: {
        component:
          'This is a sample of Basic DataTable, you will pass config and component auto handle display for you',
      },
    },
  },
};

export const WithNestedTable: Story = {
  render: args => <RenderNestedTable {...args} />,
  parameters: {
    docs: {
      source: {
        state: 'open',
        type: 'code',
        code: `
const RenderNestedTable: React.FC<any> = () => {
  // Hooks
  const { filter, modifyColumn, pagination, table, search } = useDataTableListing<
    SurveyProject,
    SurveyProject
  >({
    name: 'survey-project',
    config: {
      //  NOTES: This is no need pass auth props when had
      // Provider wrapper
      env: 'development',
      auth: {
        token: '5474r2x214z284d4w2b4y4z594l534v5m4j4t2f4n5m5',
        userId: '**********',
        accountId: '**********',
        portalId: 33167,
      },
      api: {
        listing: {
          url: 'https://sandbox-survey.antsomi.com/api/v1/survey/performance',
        },
        filter: {
          url: 'https://column.ants.tech/api/filter',
        },
        column: {
          url: 'https://column.ants.tech/api/column',
        },
        search: {
          url: 'https://sandbox-survey.antsomi.com/api/v1/survey/performance',
          params: {
            limit: 100,
            page: 1,
          },
        },
        matchesAny: {
          general: {
            url: 'https://sandbox-survey.antsomi.com/api/v1/survey/filter/get-value-matches-any',
            params(filterItem) {
              return {
                page: 1,
                limit: 20,
                metric_code: filterItem?.column?.toString().toLowerCase(),
              };
            },
          },
          survey_id: {
            url: 'https://sandbox-survey.antsomi.com/api/v1/survey/performance',
            params() {
              return {
                page: 1,
                limit: 20,
              };
            },
          },
        },
      },
      object: {
        type: 5, // Survey,
        objectId: -1, // NOTES: will note detail when to use objectId, current is no need
      },
    },
    search: {
      itemMapKeys: {
        id: 'survey_id',
        label: 'survey_name',
      },
      isClientSearch: false,
      searchItemProps: {},
    },
    queryOptions: {
      getTableListing: {
        refetchOnMount: 'always',
      },
    },
    filter: {
      externalFilters: [],
      matchesAny: {
        general: {
          formatData(response) {
            return response?.rows?.map(record => ({
              key: record.value,
              title: record.label,
            }));
          },
        },
        survey_id: {
          formatData(response: { body: SurveyProject[] }) {
            return response?.body?.map(record => ({
              key: record.survey_id,
              title: record.survey_id,
            }));
          },
          total(response) {
            return response?.total || 0;
          },
        },
      },
    },
    table: {
      mainColumnKey: 'survey_id',
      expandColumnKey: 'survey_name',
      onRow: record => ({
        onClick: () => {
          console.log('record', record);
        },
      }),
      expandable: {
        onExpand(expanded, record) {
          console.log('onExpand', expanded, record);
        },
        expandedRowRender: record => (
          <Table
            columns={[
              {
                key: 'action',
                dataIndex: 'action',
                title: 'Action',
              },
              {
                key: 'created_on',
                dataIndex: 'created_on',
                title: 'Created on',
              },
              {
                key: 'error_info',
                dataIndex: 'error_info',
                title: 'Error Info',
              },
              {
                key: 'start_time',
                dataIndex: 'start_time',
                title: 'Start Time',
              },
            ]}
            dataSource={[
              {
                key: 1,
                action: 'Add Data (40517165) of' + record.survey_name,
                created_on: 'Success',
                error_info: 'Error Info 1',
                start_time: '07/06/2016 11:13:27',
              },
            ]}
            bordered
            pagination={false}
          />
        ),
      },
    },
  });

  return (
    <TableWrapper>
      <DataTable<SurveyProject>
        table={{
          ...table,
        }}
        filter={filter}
        toolbar={{
          actionButtons: {
            SEARCH: search,
            COLUMN: modifyColumn,
          },
          selectedRowButtons: {
            ACTION: {
              label: 'Action',
              dropdownProps: {
                menu: {
                  items: [
                    {
                      label: 'Action 1',
                      key: '1',
                    },
                  ],
                },
              },
            },
          },
        }}
        pagination={pagination}
      />
    </TableWrapper>
  );
};
        `,
      },
      description: {
        component: 'This is a sample of nested table',
      },
    },
  },
};

export const GridViewMode: Story = {
  render: args => <RenderGridViewMode {...args} />,
  parameters: {
    docs: {
      source: {
        state: 'open',
        type: 'code',
        code: `
const RenderGridViewMode: React.FC<any> = () => {
  // Hooks
  const { filter, modifyColumn, pagination, table, search, gridView } = useDataTableListing<
    SurveyProject,
    SurveyProject
  >({
    name: 'survey-project-2',
    config: {
      //  NOTES: This is no need pass auth props when had
      // Provider wrapper
      env: 'development',
      auth: {
        token: '5474r2x214z284d4w2b4y4z594l534v5m4j4t2f4n5m5',
        userId: '**********',
        accountId: '**********',
        portalId: 33167,
      },
      api: {
        listing: {
          url: 'https://sandbox-survey.antsomi.com/api/v1/survey/performance',
        },
        filter: {
          url: 'https://column.ants.tech/api/filter',
        },
        column: {
          url: 'https://column.ants.tech/api/column',
        },
        search: {
          url: 'https://sandbox-survey.antsomi.com/api/v1/survey/performance',
          params: {
            limit: 100,
            page: 1,
          },
        },
        matchesAny: {
          general: {
            url: 'https://sandbox-survey.antsomi.com/api/v1/survey/filter/get-value-matches-any',
            params(filterItem) {
              return {
                page: 1,
                limit: 20,
                metric_code: filterItem?.column?.toString().toLowerCase(),
              };
            },
          },
          survey_id: {
            url: 'https://sandbox-survey.antsomi.com/api/v1/survey/performance',
            params() {
              return {
                page: 1,
                limit: 20,
              };
            },
          },
        },
      },
      object: {
        type: 5, // Survey,
        objectId: -1, // NOTES: will note detail when to use objectId, current is no need
      },
    },
    search: {
      itemMapKeys: {
        id: 'survey_id',
        label: 'survey_name',
      },
      isClientSearch: false,
      searchItemProps: {},
    },
    queryOptions: {
      getTableListing: {
        refetchOnMount: 'always',
      },
    },
    filter: {
      externalFilters: [],
      matchesAny: {
        general: {
          formatData(response) {
            return response?.rows?.map(record => ({
              key: record.value,
              title: record.label,
            }));
          },
        },
        survey_id: {
          formatData(response: { body: SurveyProject[] }) {
            return response?.body?.map(record => ({
              key: record.survey_id,
              title: record.survey_id,
            }));
          },
          total(response) {
            return response?.total || 0;
          },
        },
      },
    },
    table: {
      mainColumnKey: 'survey_id',
    },
    gridView: {
      itemMapKeys: {
        id: 'survey_id',
        name: 'survey_name',
        thumbnail: 'survey_name',
      },
      name(record) {
        return record.survey_name;
      },
      thumbnail(record) {
        return "https://picsum.photos/200/300?random=" + record.survey_id;
      },
      itemProps: {
        removable: true,
        showButton: true,
        onRemove(record) {
          console.log('🚀 ~ record:', record);
        },
        removeModalProps: {
          title: 'Remove Survey',
          content: (
            <div>
              <p>Are you sure you want to remove the survey?</p>
              <p>This action can not be undone.</p>
            </div>
          ),
        },
        buttonProps: {
          text: 'View',
          onClick(record) {
            console.log('🚀 ~ record:', record);
          },
        },
      },
    },
  });

  return (
    <TableWrapper>
      <DataTable<SurveyProject>
        table={{
          ...table,
        }}
        filter={filter}
        toolbar={{
          actionButtons: {
            SEARCH: search,
            COLUMN: modifyColumn,
            GRID_VIEW: gridView,
          },
          selectedRowButtons: {
            ACTION: {
              label: 'Action',
              dropdownProps: {
                menu: {
                  items: [
                    {
                      label: 'Action 1',
                      key: '1',
                    },
                  ],
                },
              },
            },
          },
        }}
        pagination={pagination}
      />
    </TableWrapper>
  );
};
        `,
      },
      description: {
        component: 'This is a sample of grid view mode',
      },
    },
  },
};

export const CustomContentRender: Story = {
  render: args => <RenderCustomContentRender {...args} />,
  parameters: {
    docs: {
      description: {
        component: 'This is a sample of grid view mode',
      },
    },
  },
};

export const GroupByColumn: Story = {
  render: args => <RenderGroupByColumn {...args} />,
  parameters: {
    docs: {
      source: {
        state: 'open',
        type: 'code',
        code: `
const RenderGroupByColumn: React.FC<any> = () => {
  // Hooks
  const { filter, modifyColumn, pagination, table, search, groupBy } = useDataTableListing<
    SurveyProject,
    SurveyProject
  >({
    name: 'group-by-data-table',
    config: surveyConfig,
    search: {
      itemMapKeys: {
        id: 'survey_id',
        label: 'survey_name',
      },
      isClientSearch: false,
      searchItemProps: {},
    },
    queryOptions: {
      getTableListing: {
        refetchOnMount: 'always',
      },
    },
    filter: {
      externalFilters: [],
      matchesAny: {
        general: {
          formatData(response) {
            return response?.rows?.map(record => ({
              key: record.value,
              title: record.label,
            }));
          },
        },
        survey_id: {
          formatData(response: { body: SurveyProject[] }) {
            return response?.body?.map(record => ({
              key: record.survey_id,
              title: record.survey_id,
            }));
          },
          total(response) {
            return response?.total || 0;
          },
        },
      },
    },
    table: {
      mainColumnKey: 'survey_id',
    },
    groupBy: {
      columnKeys: ['c_user_id', 'u_user_id'],
      defaultSelectedKey: 'c_user_id',
    },
  });

  return (
    <TableWrapper>
      <DataTable<SurveyProject>
        table={{
          ...table,
          rowSelection: undefined,
        }}
        filter={filter}
        toolbar={{
          actionButtons: {
            SEARCH: search,
            COLUMN: modifyColumn,
            GROUP_BY: groupBy,
          },
          selectedRowButtons: {
            ACTION: {
              label: 'Action',
              dropdownProps: {
                menu: {
                  items: [
                    {
                      label: 'Action 1',
                      key: '1',
                    },
                  ],
                },
              },
            },
          },
        }}
        pagination={pagination}
      />
    </TableWrapper>
  );
};
        `,
      },
      description: {
        component: 'This is a sample of group by column',
      },
    },
  },
};

export const ExtraRightContentOfToolbar: Story = {
  render: args => <RenderExtraRightContentOfToolbar {...args} />,
  parameters: {
    docs: {
      source: {
        state: 'open',
        type: 'code',
        code: `
const RenderExtraRightContentOfToolbar: React.FC<any> = () => {
  // Hooks
  const { filter, modifyColumn, pagination, table, search } = useDataTableListing<
    SurveyProject,
    SurveyProject
  >({
    name: 'group-by-data-table',
    config: surveyConfig,
    search: {
      itemMapKeys: {
        id: 'survey_id',
        label: 'survey_name',
      },
      isClientSearch: false,
      searchItemProps: {},
    },
    queryOptions: {
      getTableListing: {
        refetchOnMount: 'always',
      },
    },
    filter: {
      externalFilters: [],
      matchesAny: {
        general: {
          formatData(response) {
            return response?.rows?.map(record => ({
              key: record.value,
              title: record.label,
            }));
          },
        },
        survey_id: {
          formatData(response: { body: SurveyProject[] }) {
            return response?.body?.map(record => ({
              key: record.survey_id,
              title: record.survey_id,
            }));
          },
          total(response) {
            return response?.total || 0;
          },
        },
      },
    },
    table: {
      mainColumnKey: 'survey_id',
    },
    groupBy: {
      columnKeys: ['c_user_id', 'u_user_id'],
      defaultColumnKey: 'c_user_id',
    },
  });

  return (
    <TableWrapper>
      <DataTable<SurveyProject>
        table={{
          ...table,
          rowSelection: undefined,
        }}
        filter={filter}
        toolbar={{
          rightContent: (
            <Flex align="center" gap={10} style={{ marginRight: 10 }}>
              <Button type="default">Today</Button>
              <Select
                options={[
                  { label: 'Month', value: 'month' },
                  { label: 'Year', value: 'year' },
                ]}
                defaultValue="year"
                style={{ width: 50 }}
              />
            </Flex>
          ),
          actionButtons: {
            SEARCH: search,
            COLUMN: modifyColumn,
          },
          selectedRowButtons: {
            ACTION: {
              label: 'Action',
              dropdownProps: {
                menu: {
                  items: [
                    {
                      label: 'Action 1',
                      key: '1',
                    },
                  ],
                },
              },
            },
          },
        }}
        pagination={pagination}
      />
    </TableWrapper>
  );
};
        `,
      },
      description: {
        component: 'This is a sample of extra right content of toolbar',
      },
    },
  },
};

export const WithDefaultFilterAndNoCache: Story = {
  render: args => <RenderWithDefaultFilterAndNoCache {...args} />,
  parameters: {
    docs: {
      source: {
        state: 'open',
        type: 'code',
        code: `
const RenderWithDefaultFilterAndNoCache: React.FC<any> = () => {
  // Hooks
  const { filter, modifyColumn, pagination, table, search } = useDataTableListing<
    SurveyProject,
    SurveyProject
  >({
    name: 'survey-project-no-cache',
    config: { ...surveyConfig, disabledCache: true },
    search: {
      itemMapKeys: {
        id: 'survey_id',
        label: 'survey_name',
      },
      isClientSearch: false,
      addFilterInfo: {
        metricId: 'survey_name',
        operator: 'contain',
      },
      searchItemProps: {},
      formatData(response) {
        return response?.body || [];
      },
    },
    queryOptions: {
      getTableListing: {
        refetchOnMount: 'always',
      },
    },
    filter: {
      externalFilters: [],
      defaultFilters: [
        {
          column: 'SURVEY_NAME',
          operator: 'contains',
          value: 'vi',
          dataType: 'string',
        },
      ],
      matchesAny: {
        general: {
          formatData(response) {
            return response?.rows?.map(record => ({
              key: record.value,
              title: record.label,
            }));
          },
        },
        survey_id: {
          formatData(response: { body: SurveyProject[] }) {
            return response?.body?.map(record => ({
              key: record.survey_id,
              title: record.survey_id,
            }));
          },
          total(response) {
            return response?.total || 0;
          },
        },
      },
    },
    table: {
      mainColumnKey: 'survey_id',
    },
  });

  return (
    <TableWrapper>
      <DataTable<SurveyProject>
        table={{
          ...table,
        }}
        filter={filter}
        toolbar={{
          actionButtons: {
            SEARCH: search,
            COLUMN: modifyColumn,
          },
          selectedRowButtons: {
            ACTION: {
              label: 'Action',
              dropdownProps: {
                menu: {
                  items: [
                    {
                      label: 'Action 1',
                      key: '1',
                    },
                  ],
                },
              },
            },
          },
        }}
        pagination={pagination}
      />
    </TableWrapper>
  );
};
        `,
      },
      description: {
        component: 'This case handle for set a init default filter and no cache',
      },
    },
  },
};
