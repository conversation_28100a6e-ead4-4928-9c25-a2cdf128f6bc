// Libraries
import React, { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';
import { flatMap, isEmpty, pick, xor, groupBy as lodashGroupBy } from 'lodash';
import { useQueryClient } from '@tanstack/react-query';

// Types
import { useAppConfigContext } from '@antscorp/antsomi-ui/es/providers';

// Components
import { Typography, Switch, Flex } from '@antscorp/antsomi-ui/es/components/atoms';

// Constants
import {
  COLUMN_DOMAIN,
  OPERATORS_CODE,
  QUERY_KEYS,
  globalToken,
} from '@antscorp/antsomi-ui/es/constants';
import {
  MODIFY_COLUMN_DISABLE_EDITABLE,
  MODIFY_COLUMN_DISABLE_REMOVE,
  SAVED_FILTER_DEFAULT,
  TABLE_LISTING_PREFIX,
} from './constants';
import {
  DEFAULT_CELL_EMPTY,
  DEFAULT_PAGE_SIZE,
  DEFAULT_TOGGLE_WIDTH,
  SORT_MAP,
} from '../../constants';

// Hooks
import { useDeepCompareEffect, useDeepCompareMemo } from '@antscorp/antsomi-ui/es/hooks';
import {
  useCreateModifyColumn,
  useDeleteModifyColumn,
  useDeleteSavedFilter,
  useGetColumnMetrics,
  useGetFilterMetricList,
  useGetInfiniteMatchesAnyList,
  useGetModifyColumnList,
  useGetSavedFilterList,
  useGetSearchListing,
  useGetTableListing,
  useSaveFilter,
  useUpdateFilter,
  useUpdateModifyColumn,
} from '@antscorp/antsomi-ui/es/queries';

// Types
import { ModifyColumnMetric } from '../../../ModifyColumnModal';
import { DataTableLocalStorage, UseDataTableListingProps } from './types';
import { ColumnMetric, FilterMetric } from '@antscorp/antsomi-ui/es/models/DataTable';
import { SavedModifyColumnItem, TOnSaveModifyColumnArgs } from '../../../ModifyColumnModal/types';
import {
  TColumnActionButton,
  TGridViewActionButton,
  TSearchActionButton,
  TSearchItem,
  TableProps,
  TGridViewKeys,
  TGroupByActionButton,
} from '../../types';

import {
  FilterItem,
  FilterMetricItem,
  FilterProps,
  SavedFilterItem,
  TOnSaveFilterArgs,
} from '@antscorp/antsomi-ui/es/components/organism/Filter';

import { PaginationProps, TChangePageSizePayload } from '../../components';
import { SorterResult } from 'antd/es/table/interface';

// Utils
import {
  flatTree,
  mapResponseSearchToGeneral,
  parseJSONFromLocalStorage,
  reorder,
} from '@antscorp/antsomi-ui/es/utils';
import { METRIC_MAP_NUMBER_TYPE } from '../../constants/filter';
import { mapFiltersToApiFilters, mapFiltersToRules, mapRulesToFilters } from '../../utils';
import { MatchesAnyItem } from '@antscorp/antsomi-ui/es/components/molecules';
import {
  AddRadiusIcon,
  ExpandLessIcon,
  SubtractRadiusIcon,
} from '@antscorp/antsomi-ui/es/components/icons';
import { DataTableHeaderItem } from '@antscorp/antsomi-ui/es/models/DataTable/DataTableListing';
import { TTemplateItem } from '@antscorp/antsomi-ui/es/components/template';
import { ItemType } from 'antd/es/menu/hooks/useItems';
import { GroupByExpandButton } from '../../styled';

const { Text } = Typography;

const { MATCHES, MATCHES_ANY, NOT_MATCHES } = OPERATORS_CODE;

interface TUseDataTableListing<TTableType = any> {
  modifyColumn: TColumnActionButton;
  filter: FilterProps;
  pagination: PaginationProps;
  table: TableProps<TTableType>;
  search: TSearchActionButton;
  gridView: TGridViewActionButton;
  groupBy: TGroupByActionButton;

  /**
   * This selectedRowKeys is deprecated. Use selectedRow.keys instead.
   * @deprecated
   */
  selectedRowKeys: React.Key[];
  selectedRow: {
    keys: React.Key[];
    rows: TTableType[];
  };

  // Queries
  queries: {
    getTableListing: ReturnType<typeof useGetTableListing>;
    getModifyColumnList: ReturnType<typeof useGetModifyColumnList>;
    getSavedFilterList: ReturnType<typeof useGetSavedFilterList>;
    getColumnMetrics: ReturnType<typeof useGetColumnMetrics>;
    getFilterMetricList: ReturnType<typeof useGetFilterMetricList>;
    getSearchListing: ReturnType<typeof useGetSearchListing>;
  };

  // Handles
  /**
   * Function to refetch table listing
   * @returns void
   */
  refetchTableListing: () => void;
  onChangeSelectedRow: (selectedRowKeys: React.Key[], selectedRows?: TTableType[]) => void;
  setDatTableListingState: React.Dispatch<React.SetStateAction<TState<TTableType>>>;
}

type TState<TTableType = any> = {
  filters?: FilterItem[];
  selectedFilterId?: string | number;
  pagination: PaginationProps;
  table: TableProps<TTableType>;
  groupBy: {
    selectedKey?: string;
    expandedKeys?: Record<any, boolean>;
  };
  selectedRowKeys: React.Key[];
  selectedRow: {
    keys: React.Key[];
    rows: TTableType[];
  };
  searchValue?: string;
  sorter?: SorterResult<TTableType>;
  selectedFilterItem?: FilterItem;
  expandedRowKeys?: React.Key[];
  /** Mode view of data table listing */
  modeView?: TGridViewKeys;
};

const initialState: TState<any> = {
  filters: [],
  selectedFilterId: SAVED_FILTER_DEFAULT[0].id,
  pagination: {
    page: 1,
    pageSize: DEFAULT_PAGE_SIZE,
    total: 0,
  },
  table: {
    loading: false,
    columnWidths: {},
  },
  selectedRowKeys: [],
  selectedRow: {
    keys: [],
    rows: [],
  },
  searchValue: '',
  sorter: {},
  selectedFilterItem: undefined,
  expandedRowKeys: [],
  modeView: 'TABLE',
  groupBy: {
    selectedKey: undefined,
  },
};

export function useDataTableListing<TTableType = Record<string, any>, TSearchType = any>(
  props: UseDataTableListingProps<TTableType, TSearchType>,
): TUseDataTableListing<TTableType> {
  // Props
  const {
    config,
    name = 'default',
    table: tableProps,
    search: searchProps,
    filter: filterProps,
    gridView: gridViewProps,
    groupBy: groupByProps,
    queryOptions,
  } = props || {};

  // Hooks
  const queryClient = useQueryClient();
  const { appConfig } = useAppConfigContext();

  // State
  const [state, setState] = useState<TState<TTableType>>(initialState);

  // Variables
  const { env = appConfig?.env, auth = appConfig?.auth, api, object, disabledCache } = config;
  const {
    column: apiColumn,
    filter: apiFilter,
    listing: apiListing,
    search: apiSearch,
    matchesAny: apiMatchesAny,
  } = api || {};
  const {
    url: columnUrl = `${COLUMN_DOMAIN[env || 'development']}/api/column`,
    enabled: enabledApiColumn = true,
    ...restOfApiColumnRequest
  } = apiColumn || {};
  const {
    url: filterUrl = `${COLUMN_DOMAIN[env || 'development']}/api/filter`,
    enabled: enabledApiFilter = true,
    ...restOfApiFilterRequest
  } = apiFilter || {};
  const {
    url: listingUrl = '',
    enabled: enabledApiListing = true,
    ...restOfApiListingRequest
  } = apiListing || {};
  const {
    url: searchUrl = '',
    enabled: enabledApiSearch = true,
    ...restOfApiSearchRequest
  } = apiSearch || {};
  const {
    filters,
    selectedFilterId,
    pagination,
    selectedRowKeys,
    searchValue,
    table,
    sorter,
    selectedRow,
    selectedFilterItem,
    expandedRowKeys,
    groupBy,
    modeView,
  } = state;
  const {
    externalFilters,
    defaultFilters,
    includeDataType = false,
    matchesAny,
    formatFilterValue,
  } = filterProps || {};
  const { sortDirectionKey = 'az', mainColumnKey, expandColumnKey, expandable } = tableProps || {};
  const { itemProps: gridViewItemProps } = gridViewProps || {};
  const { columnKeys: groupByColumnKeys, defaultSelectedKey: groupByDefaultSelectedKey } =
    groupByProps || {};
  const modifyColumnAuth = {
    ...auth,
    url: columnUrl,
  };
  const filterAuth = {
    ...auth,
    url: filterUrl,
  };
  const listingAuth = {
    ...auth,
    url: listingUrl,
  };
  const searchAuth = {
    ...auth,
    url: searchUrl,
  };
  /**
   * If has api config for specific selectedFilterItem, use it
   * Otherwise use general api config
   */
  const matchesAnyRequest = apiMatchesAny?.[`${selectedFilterItem?.column}`.toLowerCase()]
    ? apiMatchesAny?.[`${selectedFilterItem?.column}`.toLowerCase()]
    : apiMatchesAny?.general;

  /**
   * If `selectedFilterItem` has a column, the function will attempt to find a matching property in the `matchesAny`
   * object by converting the column name to lowercase. If such a property does not exist, it will default to using
   * the `general` property from the `matchesAny` object.
   */
  const matchesAnyProps = matchesAny?.[`${selectedFilterItem?.column}`.toLowerCase()]
    ? matchesAny?.[`${selectedFilterItem?.column}`.toLowerCase()]
    : matchesAny?.general;

  // Memos
  const mapObject = useDeepCompareMemo(
    () => ({
      objType: object?.type,
      objId: object?.objectId,
    }),
    [object?.objectId, object?.type],
  );

  // Memoized function to determine if "fetch matches any" is enabled.
  const isEnabledFetchMatchesAny = useMemo(
    () =>
      // Check if a filter item is selected.
      !!selectedFilterItem &&
      // Check if the selected filter item's operator is one of: MATCHES_ANY, NOT_MATCHES, or MATCHES.
      [MATCHES_ANY, NOT_MATCHES, MATCHES].includes(selectedFilterItem.operator as any) &&
      // Check if the matchesAnyRequest object exists.
      !!matchesAnyRequest &&
      // Check if matchesAnyRequest.enabled is a boolean and true, or if it is not set (defaulting to true).
      (typeof matchesAnyRequest.enabled === 'boolean' ? matchesAnyRequest.enabled : true) &&
      // Check if matchesAnyProps.options does not exist.
      !matchesAnyProps?.options,
    // Dependencies for the useMemo hook: recalculate if matchesAnyProps.options, matchesAnyRequest, or selectedFilterItem changes.
    [matchesAnyProps?.options, matchesAnyRequest, selectedFilterItem],
  );

  const localStorageKey = useDeepCompareMemo(() => {
    const { userId, portalId } = auth || {};

    return `${TABLE_LISTING_PREFIX}${userId}-${portalId}-${name}`;
  }, [name, auth]);

  const localStorageValues: DataTableLocalStorage<TTableType> = useDeepCompareMemo(
    () => parseJSONFromLocalStorage(localStorageKey) || {},
    [localStorageKey],
  );

  // Queries
  const getColumnMetrics = useGetColumnMetrics({
    args: {
      auth: modifyColumnAuth,
      params: mapObject,
      request: restOfApiColumnRequest,
    },
    options: {
      enabled: enabledApiColumn,
      refetchOnMount: 'always',
      ...queryOptions?.getColumnMetricList,
    },
  });
  const getModifyColumnList = useGetModifyColumnList({
    args: {
      auth: modifyColumnAuth,
      params: mapObject,
      request: restOfApiColumnRequest,
    },
    options: {
      enabled: enabledApiColumn,
      ...queryOptions?.getModifyColumnList,
    },
  });
  const getSavedFilterList = useGetSavedFilterList({
    args: {
      auth: filterAuth,
      params: mapObject,
      request: restOfApiFilterRequest,
    },
    options: {
      enabled: enabledApiFilter,
      ...queryOptions?.getSavedFilterList,
    },
  });
  const getFilterMetricList = useGetFilterMetricList({
    args: {
      auth: filterAuth,
      params: mapObject,
      request: restOfApiFilterRequest,
    },
    options: {
      enabled: enabledApiFilter,
      refetchOnMount: 'always',
      ...queryOptions?.getFilterMetricList,
    },
  });
  const getInfiniteMatchesAnyList = useGetInfiniteMatchesAnyList({
    args: {
      auth,
      params: matchesAnyRequest?.params?.(selectedFilterItem),
      request: matchesAnyRequest,
    },
    options: {
      enabled: isEnabledFetchMatchesAny,
      keepPreviousData: true,
      retry: false,
      getNextPageParam(lastPage, allPages) {
        // Extract the total number of items from the last fetched page
        const total =
          (typeof matchesAnyProps?.total === 'function'
            ? matchesAnyProps?.total(lastPage)
            : matchesAnyProps?.total) || lastPage.total;

        // Check if the total number of items is defined
        if (total) {
          // Calculate the current total number of items retrieved so far
          // Process all pages and their data using formatData function and flatten the results
          const currentTotalItems = flatMap(
            allPages?.map(page => matchesAnyProps?.formatData?.(page) || []),
          );

          // If the total number of items fetched is less than the total available,
          // return the next page number (allPages.length + 1). Otherwise, return undefined.
          return (currentTotalItems?.length || 0) < total ? allPages?.length + 1 : undefined;
        }

        // If the total number of items is not defined, return undefined
        return undefined;
      },
    },
  });

  // Variables
  const { data: modifyColumnsData, isLoading: isModifyColumnsLoading } = getModifyColumnList || {};

  const modifyColumnSelected: string = useDeepCompareMemo(
    () => modifyColumnsData?.find(({ isLasted }) => +isLasted === 1)?.modifyColId || '',
    [modifyColumnsData],
  );

  const getTableListing = useGetTableListing<TTableType>({
    args: {
      auth: listingAuth,
      params: {
        limit: pagination.pageSize,
        page: pagination.page,
        filter: JSON.stringify(
          mapFiltersToApiFilters(
            [...(filters || []), ...(externalFilters || [])],
            includeDataType,
            formatFilterValue,
          ),
        ),
        columns: JSON.stringify(
          modifyColumnsData
            ?.find(modifyColumnItem => modifyColumnItem.modifyColId === modifyColumnSelected)
            ?.columns?.map(col => col.metricCode?.toUpperCase()) || [],
        ),
        ...(sorter?.order &&
          sorter?.columnKey && {
            sort: sorter.columnKey.toString(),
            [sortDirectionKey]: SORT_MAP[sorter.order],
          }),
      },
      request: restOfApiListingRequest,
    },
    options: {
      queryKey: [name],
      keepPreviousData: true,
      enabled: !!listingAuth?.url && !!listingAuth?.token && enabledApiListing,
      ...queryOptions?.getTableListing,
    },
  });
  const getSearchListing = useGetSearchListing({
    args: {
      auth: searchAuth,
      params: {
        search: searchValue,
      },
      request: restOfApiSearchRequest,
    },
    options: {
      keepPreviousData: true,
      enabled: !!searchAuth?.url && !!searchAuth?.token && enabledApiSearch,
      ...queryOptions?.getSearchListing,
    },
  });

  // Variables
  const { data: columnMetricsData } = getColumnMetrics || {};
  const { data: savedFiltersData } = getSavedFilterList || {};
  const { data: filterMetricsData } = getFilterMetricList || {};
  const {
    data: tableListing,
    isLoading: isTableListingLoading,
    isRefetching: isTableListingRefetching,
  } = getTableListing || {};
  const { body: tableBody, header: tableHeader, total } = tableListing || {};
  const {
    data: searchListingData,
    isLoading: isSearchListingLoading,
    isFetching: isSearchListingFetching,
  } = getSearchListing || {};
  const {
    data: infiniteMatchesAnyData,
    isFetchingNextPage,
    isLoading: isMatchesAnyListLoading = false,
    isFetching: isMatchesAnyListFetching = false,
    fetchNextPage,
  } = getInfiniteMatchesAnyList || {};
  // Conditionally format search listing data based on the presence of a formatData function in searchProps.
  // If formatData is defined, use it to format searchListingData.
  // Otherwise, use the mapResponseSearchToGeneral function to format searchListingData.
  const searchListingBody = searchProps?.formatData
    ? searchProps.formatData(searchListingData)
    : mapResponseSearchToGeneral<TSearchType>(searchListingData).body || {};

  // Mutations
  const { mutateAsync: updateModifyColumn } = useUpdateModifyColumn({
    auth: modifyColumnAuth,
  });
  const { mutateAsync: deleteModifyColumn } = useDeleteModifyColumn({
    auth: modifyColumnAuth,
  });
  const { mutateAsync: createModifyColumn } = useCreateModifyColumn({
    auth: modifyColumnAuth,
  });
  const { mutateAsync: saveFilter } = useSaveFilter({
    auth: filterAuth,
  });
  const { mutateAsync: updateFilter } = useUpdateFilter({
    auth: filterAuth,
  });
  const { mutateAsync: deleteSavedFilter } = useDeleteSavedFilter({
    auth: filterAuth,
  });

  // Memo
  const columnMetrics: ModifyColumnMetric[] = useDeepCompareMemo(() => {
    const serializeColumnMetrics = (metrics: ColumnMetric[]): ModifyColumnMetric[] => {
      const result: ModifyColumnMetric[] = metrics.map(metric => ({
        id: metric.metricCode || metric.metricId || metric.metricName || -1,
        name: metric.metricName,
        disabled: +metric.fixColumn === 1, // 1 is fixed column
        selectedName: metric.columnLabel,
        // disabled: metric.propertyCode === 390248029392039,
        children: serializeColumnMetrics(metric.child || []),
      }));

      return result;
    };

    return serializeColumnMetrics(columnMetricsData || []);
  }, [columnMetricsData]);

  const modifyColumnList: SavedModifyColumnItem[] = useDeepCompareMemo(
    () =>
      modifyColumnsData
        ?.map(({ modifyName, modifyColId, columns }) => ({
          id: modifyColId,
          name: modifyName,
          editable: !MODIFY_COLUMN_DISABLE_EDITABLE.includes(modifyName),
          removable: !MODIFY_COLUMN_DISABLE_REMOVE.includes(modifyName),
          metrics: columns?.map(({ metricCode, metricId }) => metricCode || metricId || -1),
        }))
        .reverse() || [],
    [modifyColumnsData],
  );

  const filterMetrics: FilterMetricItem[] = useDeepCompareMemo(() => {
    const serializeFilterMetrics = (metrics: FilterMetric[]): FilterMetricItem[] => {
      const result: FilterMetricItem[] = metrics.map(metric => ({
        id: metric.metricCode,
        name: metric.metricName,
        dataType: METRIC_MAP_NUMBER_TYPE[+metric.dataType] || 'string',
        children: serializeFilterMetrics(metric.child || []),
        operators: Array.isArray(metric.operator)
          ? metric.operator?.map(({ name, value }) => ({ label: name, value })) || []
          : [],
      }));

      return result;
    };

    return serializeFilterMetrics(filterMetricsData || []);
  }, [filterMetricsData]);

  const savedFilterList = useDeepCompareMemo(
    () =>
      SAVED_FILTER_DEFAULT.concat(
        savedFiltersData?.map(({ filterId, filterName }) => ({
          id: filterId,
          name: filterName,
          removable: true,
        })) || [],
      ),
    [savedFiltersData],
  );

  const renderColumnWithExpandIcon = useCallback(
    (
      children: ReactNode, // The content of the column
      headerCol: DataTableHeaderItem, // The header configuration for the column
      record: TTableType, // The current record being rendered
    ) => {
      const { name } = headerCol || {}; // Destructure the name from the headerCol, with a fallback to an empty object

      if (expandColumnKey === name) {
        // Check if the current column is the expand column
        const expanded = expandedRowKeys?.includes(
          // Check if the current row is expanded
          (record?.[mainColumnKey as keyof TTableType] as React.Key) || '', // Get the key of the current record
        );

        return (
          <Flex align="center" gap={4}>
            <ExpandLessIcon
              size={20}
              style={{
                flexShrink: 0,
                color: globalToken?.bw8,
                transform: `rotate(${expanded ? 0 : 180}deg)`,
                cursor: 'pointer',
                transition: 'transform 0.3s ease',
              }}
              onClick={() => {
                if (expandable && expandable.onExpand) {
                  expandable.onExpand(!expanded, record);
                }

                setState(prev => ({
                  ...prev,
                  expandedRowKeys: xor(expandedRowKeys, [
                    record[mainColumnKey as keyof TTableType] as React.Key,
                  ]),
                }));
              }}
            />
            {children}
          </Flex>
        );
      }

      return children;
    },
    [expandColumnKey, expandable, expandedRowKeys, mainColumnKey],
  );

  const handleUpdateLocalStorage = useCallback(
    (dataTableLocalStorage: Partial<DataTableLocalStorage>) => {
      if (disabledCache) {
        return;
      }

      const localStorageValues = parseJSONFromLocalStorage(localStorageKey) || {};
      // Set Local Storage
      localStorage.setItem(
        localStorageKey,
        JSON.stringify({
          ...localStorageValues,
          ...dataTableLocalStorage,
        }),
      );
    },
    [disabledCache, localStorageKey],
  );

  const handleUpdateGroupBy = useCallback(
    (groupBy: Partial<TState['groupBy']>) => {
      const localStorageValues = parseJSONFromLocalStorage(localStorageKey) || {};

      setState(prev => ({
        ...prev,
        groupBy: {
          ...prev.groupBy,
          ...groupBy,
        },
      }));

      handleUpdateLocalStorage({
        groupBy: {
          ...localStorageValues.groupBy,
          ...groupBy,
        },
      });
    },
    [handleUpdateLocalStorage, localStorageKey],
  );

  const tableColumns: TableProps<TTableType>['columns'] = useDeepCompareMemo(() => {
    let data: TableProps<TTableType>['columns'] = [];
    const { columns } = tableProps || {};

    // Check if has column toggle then add toggle switch column
    if (columns?.toggle) {
      const { key, dataIndex, valueKey, title, value, disabled, onChange } = columns.toggle || {};
      data.push({
        key: 'toggle' || key,
        fixed: 'left',
        align: 'center',
        dataIndex: 'toggle' || dataIndex,
        title: 'Toggle' || title,
        width: DEFAULT_TOGGLE_WIDTH,
        ...columns.toggle,
        render: (_, record) => {
          const checked = value
            ? typeof value === 'function'
              ? value(record)
              : value
            : !!record[valueKey as keyof TTableType];
          const switchDisabled = typeof disabled === 'function' ? disabled(record) : disabled;

          return (
            <Flex align="center" justify="center">
              <Switch
                checked={checked}
                disabled={switchDisabled}
                onChange={checked => {
                  if (onChange) {
                    onChange(checked, record);
                  }
                }}
              />
            </Flex>
          );
        },
      });
    }

    data = data.concat(
      tableHeader?.map(headerCol => {
        const { name, label, fixColumn, sortable } = headerCol;

        return {
          key: name,
          dataIndex: name,
          title: label,
          fixed: fixColumn ? 'left' : undefined,
          sorter: !!sortable,
          sortOrder: sorter?.columnKey === name ? sorter?.order : undefined,
          ...columns?.[name],
          render(value, record, index) {
            const { link } = columns?.[name] || {};

            const RenderComponent = (
              <Text
                ellipsis={{ tooltip: true }}
                style={{
                  ...(link
                    ? {
                        fontWeight: globalToken?.fontWeightStrong,
                        color: globalToken?.colorPrimary,
                      }
                    : {}),
                }}
              >
                {typeof value !== 'object' ? value || DEFAULT_CELL_EMPTY : DEFAULT_CELL_EMPTY}
              </Text>
            );

            if (columns?.[name]?.render) {
              return renderColumnWithExpandIcon(
                columns[name].render(value, record, index, RenderComponent),
                headerCol,
                record,
              );
            }

            if (link) {
              return renderColumnWithExpandIcon(
                <Link to={typeof link === 'function' ? link(record) : link}>
                  {RenderComponent}
                </Link>,
                headerCol,
                record,
              );
            }

            return renderColumnWithExpandIcon(RenderComponent, headerCol, record);
          },
        };
      }) || [],
    );

    // Handle Group by
    if (groupBy.selectedKey) {
      data.unshift({
        key: 'groupBy',
        fixed: 'left',
        align: 'center',
        dataIndex: 'groupBy',
        title: '',
        width: 50,
        render(value, record) {
          const { groupTotal = 0 } = (record as any) || {};
          const groupKey = record?.[groupBy.selectedKey || ''];
          const isExpanded =
            typeof groupBy.expandedKeys?.[groupKey] === 'boolean'
              ? groupBy.expandedKeys?.[groupKey]
              : true;

          return (
            groupKey &&
            groupTotal > 1 && (
              <GroupByExpandButton
                onClick={() =>
                  handleUpdateGroupBy({
                    expandedKeys: {
                      ...groupBy.expandedKeys,
                      [groupKey]: !isExpanded,
                    },
                  })
                }
              >
                {isExpanded ? (
                  <SubtractRadiusIcon color={globalToken?.colorPrimary} />
                ) : (
                  <AddRadiusIcon color={globalToken?.colorPrimary} />
                )}
              </GroupByExpandButton>
            )
          );
        },
      });

      const groupByColumnIndex = data.findIndex(col => col.key === groupBy.selectedKey);

      // Reorder group by column next to expand icon
      if (groupByColumnIndex !== -1) {
        data[groupByColumnIndex].fixed = 'left';

        data = reorder(data, groupByColumnIndex, 1);
      }
    }

    return data;
  }, [
    tableProps,
    tableHeader,
    groupBy.selectedKey,
    groupBy.expandedKeys,
    sorter?.columnKey,
    sorter?.order,
    renderColumnWithExpandIcon,
    handleUpdateGroupBy,
  ]);

  const tableData: TTableType[] = useDeepCompareMemo(() => {
    let data =
      (Array?.isArray(tableBody) &&
        tableBody?.map((row: any) => ({
          ...row,
          key: row?.[tableProps?.mainColumnKey],
        }))) ||
      [];

    // Handle group by
    if (groupBy.selectedKey) {
      const groupByData = lodashGroupBy(data, groupBy.selectedKey) || [];

      data =
        Object.entries(groupByData)
          .map(
            ([groupByKey, rows]) =>
              Array.isArray(rows) &&
              rows
                .filter((_, index) => {
                  // First row always show
                  if (!index) {
                    return true;
                  }

                  const isExpanded =
                    typeof groupBy.expandedKeys?.[groupByKey] === 'boolean'
                      ? groupBy.expandedKeys?.[groupByKey]
                      : true;

                  return isExpanded;
                })
                .map((row, index) => ({
                  ...row,
                  key: row?.[tableProps?.mainColumnKey],
                  ...(index !== 0 && { [groupBy.selectedKey || '']: '' }),
                  groupTotal: rows.length,
                })),
          )
          .flat() || [];
    }

    return data;
  }, [groupBy.expandedKeys, groupBy.selectedKey, tableBody, tableProps?.mainColumnKey]);

  const searchList: TSearchItem[] = useDeepCompareMemo(() => {
    const { itemMapKeys, icon, link } = searchProps || {};

    return (
      searchListingBody?.map(
        searchItem =>
          ({
            ...Object.entries(itemMapKeys || {}).reduce(
              (acc, [key, value]) => ({
                ...acc,
                [key]: searchItem[value],
              }),
              {},
            ),
            icon: typeof icon === 'function' ? icon(searchItem) : icon,
            link: typeof link === 'function' ? link(searchItem) : link,
          }) as any,
      ) || []
    );
  }, [searchListingData, searchProps]);

  const matchesAnyList: MatchesAnyItem[] = useDeepCompareMemo(() => {
    const { formatData, options } = matchesAnyProps || {};

    if (options) return options;

    if (formatData) {
      return flatMap(infiniteMatchesAnyData?.pages?.map(page => formatData?.(page) || []));
    }

    return [];
  }, [matchesAny, infiniteMatchesAnyData, selectedFilterItem?.column]);

  const gridViewList: TTemplateItem[] = useDeepCompareMemo(() => {
    if (gridViewProps?.itemMapKeys) {
      const { name, thumbnail } = gridViewProps || {};
      return (
        tableData?.map(
          record =>
            Object.entries(gridViewProps?.itemMapKeys || {}).reduce(
              (acc, [key, value]) => ({
                ...acc,
                [key]: record[value],
                ...(thumbnail && {
                  thumbnail: typeof thumbnail === 'function' ? thumbnail(record) : thumbnail,
                }),
                ...(name && { name: typeof name === 'function' ? name(record) : name }),
              }),
              {},
            ) as TTemplateItem,
        ) || []
      );
    }

    return [];
  }, [gridViewProps, tableData]);

  const groupByOptions: ItemType[] = useDeepCompareMemo(
    () =>
      (groupByColumnKeys
        ?.map(key => {
          const column = tableColumns.find(column => column.key === key);

          if (column) {
            return {
              key,
              label: column.title,
            };
          }

          return null;
        })
        .filter(Boolean) || []) as ItemType[],
    [groupByColumnKeys, tableColumns],
  );

  // Effects
  /**
   * Update values from local storage to state
   */
  useDeepCompareEffect(() => {
    const { filter, pagination, table, sorter, groupBy, modeView } = localStorageValues || {};

    // If disabled cache then do nothing
    if (disabledCache) {
      return;
    }

    if (!isEmpty(filter)) {
      const { filters, selectedFilterId } = filter || {};
      setState(prev => ({
        ...prev,
        selectedFilterId,
        filters,
      }));
    }

    if (!isEmpty(pagination)) {
      setState(prev => ({ ...prev, pagination }));
    }

    if (!isEmpty(table)) {
      setState(prev => ({ ...prev, table }));
    }

    if (!isEmpty(sorter)) {
      setState(prev => ({ ...prev, sorter }));
    }

    if (!isEmpty(groupBy)) {
      setState(prev => ({ ...prev, groupBy }));
    }

    if (!!modeView) {
      setState(prev => ({ ...prev, modeView }));
    }
  }, [localStorageValues]);

  /**
   * Set default groupBy column
   */
  useEffect(() => {
    const { groupBy } = localStorageValues || {};

    if (groupByDefaultSelectedKey && !groupBy?.selectedKey) {
      setState(prev => ({
        ...prev,
        groupBy: { ...prev.groupBy, selectedKey: groupByDefaultSelectedKey as string },
      }));
    }
  }, [localStorageValues, groupByDefaultSelectedKey]);

  // /** Update filters from external filters */
  // useDeepCompareEffect(() => {
  //   if (externalFilters) {
  //     setState(prev => ({ ...prev, filters: [...(filters || []), ...externalFilters] }));
  //   }
  // }, [externalFilters]);

  // Handlers

  /* Modify Column */
  const onRemoveModifyColumn = useCallback(
    async (modifyColumnItem: SavedModifyColumnItem) => {
      const { status } = await deleteModifyColumn({
        params: {
          ...mapObject,
          modifyColumnId: modifyColumnItem.id,
        },
      });

      // Check if selected modify column is deleted
      if (`${modifyColumnItem.id}` === `${modifyColumnSelected}`) {
        // Update custom modify column is selected
        await createModifyColumn({
          data: {
            ...mapObject,
            name: 'Custom',
            isLasted: 1,
          },
        });
      }

      return { success: !!status };
    },
    [createModifyColumn, deleteModifyColumn, mapObject, modifyColumnSelected],
  );

  const onSelectModifyColumn = useCallback(
    (modifyColumnItem: SavedModifyColumnItem) => {
      const { id } = modifyColumnItem || {};

      updateModifyColumn({
        data: {
          ...mapObject,
          modifyColumnId: id,
          isLasted: 1,
        },
      });
    },
    [mapObject, updateModifyColumn],
  );

  const onApplyModifyColumn = useCallback(
    async (args: TOnSaveModifyColumnArgs) => {
      const { selectedMetrics, columnSetName, existColumnSet } = args || {};
      const flattenMetrics = flatTree(columnMetricsData, 'child');
      const selectedColumnMetrics = selectedMetrics
        .map(metricId => flattenMetrics?.find(metric => metric.metricCode === metricId))
        .filter(Boolean);

      let exitModifyColumnId: string = '';

      // Update exits column set
      // columnSetName is empty that mean save for Custom column set
      if (existColumnSet /* || columnSetName === '' */) {
        exitModifyColumnId = existColumnSet?.id || '';

        // if (columnSetName === '') {
        //   exitModifyColumnId =
        //     modifyColumnsData?.find(
        //       modifyColumn => modifyColumn.modifyName === 'Custom', // NOTES: Hard Custom name, will move constants
        //     )?.modifyColId || '';
        // }
      }

      /**
       * Check if exitModifyColumnId is not empty
       * If not empty, that mean update exits column set
       * If empty, that mean create new column set
       */
      if (exitModifyColumnId) {
        const { status } = await updateModifyColumn({
          data: {
            ...mapObject,
            modifyColumnId: exitModifyColumnId,
            columns: JSON.stringify(selectedColumnMetrics),
            isLasted: 1,
          },
        });

        return {
          success: !!status,
        };
      }

      // Create new column set
      const { status } = await createModifyColumn({
        data: {
          ...mapObject,
          name: columnSetName || 'Custom',
          columns: JSON.stringify(selectedColumnMetrics),
          isLasted: 1,
        },
      });

      return {
        success: !!status,
      };
    },
    [columnMetricsData, createModifyColumn, mapObject, updateModifyColumn],
  );

  const handleUpdatePagination = useCallback(
    (pagination: Partial<PaginationProps>) => {
      const localStorageValues = parseJSONFromLocalStorage(localStorageKey) || {};

      // Set State
      setState(prev => ({ ...prev, pagination: { ...prev.pagination, ...pagination } }));

      handleUpdateLocalStorage({
        pagination: {
          ...localStorageValues?.pagination,
          ...pagination,
        },
      });
    },
    [handleUpdateLocalStorage, localStorageKey],
  );

  const handleUpdateFilter = useCallback(
    (filter: Partial<TState>) => {
      const localStorageValues = parseJSONFromLocalStorage(localStorageKey) || {};

      // Set State
      setState(prev => ({ ...prev, ...filter, pagination: { ...prev.pagination, page: 1 } }));

      handleUpdateLocalStorage({
        filter: {
          ...localStorageValues?.filter,
          ...filter,
        },
        // Reset page to 1
        pagination: {
          ...localStorageValues?.pagination,
          page: 1,
        },
      });
    },
    [handleUpdateLocalStorage, localStorageKey],
  );

  const handleUpdateTable = useCallback(
    (table: TableProps<TTableType>) => {
      const localStorageValues = parseJSONFromLocalStorage(localStorageKey) || {};

      setState(prev => ({
        ...prev,
        table: {
          ...prev.table,
          ...table,
        },
      }));

      handleUpdateLocalStorage({
        table: {
          ...localStorageValues?.table,
          ...table,
        },
      });
    },
    [handleUpdateLocalStorage, localStorageKey],
  );

  const handleUpdateSorter = useCallback(
    (sorter: TState['sorter']) => {
      setState(prev => ({
        ...prev,
        sorter,
      }));

      handleUpdateLocalStorage({
        sorter,
      });
    },
    [handleUpdateLocalStorage],
  );

  const handleUpdateModeView = useCallback(
    (modeView: TState['modeView']) => {
      setState(prev => ({ ...prev, modeView }));

      handleUpdateLocalStorage({
        modeView,
      });
    },
    [handleUpdateLocalStorage],
  );

  /* Filter */
  const onChangeFilters = useCallback(
    (filters: FilterItem[]) => {
      handleUpdateFilter({
        filters,
        selectedFilterId: SAVED_FILTER_DEFAULT[0].id,
      });
    },
    [handleUpdateFilter],
  );

  const onChangeFilterCondition = useCallback((filterItem: FilterItem) => {
    setState(prev => ({ ...prev, selectedFilterItem: filterItem }));
  }, []);

  const onSaveFilter = useCallback(
    async (args: TOnSaveFilterArgs) => {
      const { filters, name, existSavedFilter } = args;

      // Update exits filter
      if (existSavedFilter) {
        const { status } = await updateFilter({
          data: {
            ...mapObject,
            filterId: existSavedFilter.id,
            filterName: existSavedFilter.name,
            rules: JSON.stringify(mapFiltersToRules(filters)),
          },
        });

        handleUpdateFilter({
          filters,
          selectedFilterId: existSavedFilter.id,
        });

        return {
          success: !!status,
        };
      }

      // Create new filter
      const { status: filterId } = await saveFilter({
        data: {
          ...mapObject,
          filterName: name,
          rules: JSON.stringify(mapFiltersToRules(filters)),
        },
      });

      if (filterId) {
        handleUpdateFilter({
          filters,
          selectedFilterId: filterId,
        });
      }

      return {
        success: !!filterId,
      };
    },
    [mapObject, saveFilter, updateFilter, handleUpdateFilter],
  );

  const onSelectSavedFilter = useCallback(
    (savedFilter: SavedFilterItem) => {
      const savedFilterInfo = savedFiltersData?.find(
        filter => filter.filterId.toString() === savedFilter?.id.toString(),
      );
      const filters = mapRulesToFilters(savedFilterInfo?.rules || []);

      handleUpdateFilter({
        filters,
        selectedFilterId: savedFilter?.id,
      });
    },
    [savedFiltersData, handleUpdateFilter],
  );

  const onRemoveSavedFilter = useCallback(
    async (savedFilter: SavedFilterItem) => {
      const { status } = await deleteSavedFilter({
        params: {
          filterId: savedFilter?.id,
          ...mapObject,
        },
      });

      if (status && selectedFilterId === savedFilter?.id) {
        handleUpdateFilter({
          selectedFilterId: SAVED_FILTER_DEFAULT[0].id,
        });
      }

      return { success: !!status };
    },
    [deleteSavedFilter, handleUpdateFilter, mapObject, selectedFilterId],
  );

  /* Table */
  const onChangeTable = useCallback(
    (_pagination, _filters, sorter, _extra) => {
      if (sorter) {
        handleUpdateSorter(sorter);
      }
    },
    [handleUpdateSorter],
  );

  /* Pagination */
  const onChangePagination = useCallback(
    ({ page, pageSize }: TChangePageSizePayload) => {
      handleUpdatePagination({
        page,
        pageSize,
      });
    },
    [handleUpdatePagination],
  );

  const onChangeSelectedRow = useCallback(
    (selectedRowKeys: React.Key[], selectedRows?: TTableType[]) => {
      setState(prev => ({
        ...prev,
        selectedRowKeys,
        selectedRow: {
          keys: selectedRowKeys,
          rows:
            selectedRows ||
            selectedRowKeys
              ?.map(
                key =>
                  tableData?.find(
                    row => row[mainColumnKey as keyof TTableType]?.toString() === key.toString(),
                  ) as TTableType,
              )
              .filter(Boolean) ||
            [],
        },
      }));
    },
    [mainColumnKey, tableData],
  );

  /** Refetch */
  const refetchTableListing = useCallback(() => {
    queryClient.invalidateQueries([QUERY_KEYS.GET_DATA_TABLE_LISTING, name], {
      exact: false,
    });
  }, [name, queryClient]);

  const onMatchesAnyLoadMore = useCallback(() => {
    if (!isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, isFetchingNextPage]);

  // Handle Effects
  /**
   * Update filters from default filters
   * */
  useDeepCompareEffect(() => {
    if (defaultFilters) {
      handleUpdateFilter({ filters: defaultFilters || [] });
    }
  }, [defaultFilters, handleUpdateFilter]);

  return {
    /* Modify Column */
    modifyColumn: {
      metrics: columnMetrics,
      savedModifyColumn: {
        list: modifyColumnList,
        selected: modifyColumnSelected,
        loading: isModifyColumnsLoading,
        onRemove: onRemoveModifyColumn,
        onSelect: onSelectModifyColumn,
      },
      onApply: onApplyModifyColumn,
    },

    /* Filter */
    filter: {
      savedFilter: {
        list: savedFilterList,
        selected: selectedFilterId,
        onSelect: onSelectSavedFilter,
        onRemove: onRemoveSavedFilter,
      },
      filters,
      filterMetrics,
      matchesAny: {
        list: matchesAnyList,
        isLoading:
          (isMatchesAnyListLoading || isMatchesAnyListFetching) &&
          !!matchesAnyRequest &&
          isEnabledFetchMatchesAny,
      },
      onSaveFilter,
      onChangeFilters,
      onChangeFilterCondition,
      onMatchesAnyLoadMore,
    },

    /* Pagination */
    pagination: {
      ...pagination,
      total,
      onChange: onChangePagination,
    },

    /* Table */
    table: {
      ...tableProps,
      loading: isTableListingLoading || isTableListingRefetching,
      columns: tableColumns,
      dataSource: tableData,
      columnWidths: table.columnWidths || {},
      expandable: {
        showExpandColumn: false,
        expandedRowKeys: expandedRowKeys || [],
        ...tableProps?.expandable,
      },
      rowSelection: {
        selectedRowKeys: selectedRow.keys || [],
        onChange: onChangeSelectedRow,
      },
      onChange: onChangeTable,
      onColumnResize(data) {
        const { index, width } = data;

        handleUpdateTable({
          columnWidths: {
            ...table.columnWidths,
            [index]: width,
          },
        });
      },
    },

    /* Grid view */
    gridView: {
      mode: modeView,
      onChangeMode: handleUpdateModeView,
      templateListingProps: {
        templatesProps: {
          items: gridViewList,
          loading: isTableListingLoading || isTableListingRefetching,
        },
        templateItemProps: {
          removable: gridViewItemProps?.removable,
          removeModalProps: {
            ...gridViewItemProps?.removeModalProps,
            onOk: async id => {
              const record = tableData?.find(
                item => item?.[gridViewProps?.itemMapKeys.id as any] === id,
              );

              if (gridViewItemProps?.onRemove && record) {
                gridViewItemProps?.onRemove(record);
              }
            },
          },
          previewBtnProps: {
            show: false,
          },
          actionAvailable: gridViewItemProps?.showButton,
          editBtnProps: {
            ...gridViewItemProps?.buttonProps,
            onClick(id) {
              const record = tableData?.find(
                item => item?.[gridViewProps?.itemMapKeys.id as any] === id,
              );
              if (record && gridViewItemProps?.buttonProps?.onClick) {
                gridViewItemProps?.buttonProps?.onClick(record);
              }
            },
          },
        },
      },
    },

    /** Group by */
    groupBy: {
      options: groupByOptions || [],
      selectedKey: groupBy.selectedKey,
      onChange(selectedKey) {
        handleUpdateGroupBy({
          selectedKey,
        });
      },
    },

    /* Selected row */
    selectedRowKeys,
    selectedRow,

    /* Search */
    search: {
      loading: isSearchListingLoading || isSearchListingFetching,
      searchList,
      ...(!!searchProps?.render && {
        itemSearchRender(_item, index, node) {
          return (
            searchProps?.render?.((searchListingBody?.[index] || {}) as TSearchType, index, node) ||
            null
          );
        },
      }),
      onSearch(valueSearch) {
        if (!searchProps?.isClientSearch) {
          setState(prev => ({ ...prev, searchValue: valueSearch }));
        }
      },
      ...(searchProps?.onClickSearchItem && {
        onClickSearchItem(item, index) {
          searchProps?.onClickSearchItem?.(searchListingBody?.[index]);
        },
      }),
      ...(pick(searchProps, [
        'isClientSearch',
        'addFilterInfo',
        'isAddFilterFromSearchItem',
        'searchItemProps',
      ]) as TSearchActionButton),
    },

    /* Queries */
    queries: {
      getTableListing,
      getModifyColumnList,
      getSavedFilterList,
      getColumnMetrics,
      getFilterMetricList,
      getSearchListing,
    },

    // Handles
    onChangeSelectedRow,
    setDatTableListingState: setState,

    // Refetch
    refetchTableListing,
  };
}
