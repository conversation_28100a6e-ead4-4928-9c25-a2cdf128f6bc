// Libraries
import { ReactNode } from 'react';
import { AxiosRequestConfig } from 'axios';

// Types
import { TOperator<PERSON>ey, TServiceAuth } from '@antscorp/antsomi-ui/es/types';
import { TEnv } from '@antscorp/antsomi-ui/es/types/config';
import { TGridViewKeys, TSearchActionButton, TSearchItem, TableProps } from '../../types';
import { PaginationProps } from '../../components';
import { ColumnType } from 'antd/es/table';
import { SorterResult } from 'antd/es/table/interface';
import {
  TGetColumnMetrics,
  TGetFilterMetricList,
  TGetModifyColumnList,
  TGetSavedFilterList,
  TGetSearchListing,
  TGetTableListing,
} from '@antscorp/antsomi-ui/es/queries';
import {
  MatchesAnyItem,
  TRemoveModalProps,
  TThumbnailButton,
} from '@antscorp/antsomi-ui/es/components/molecules';
import { TTemplateItem } from '@antscorp/antsomi-ui/es/components/template';
import { FilterItem } from '../../../Filter/types';

export type TApiGlobal = AxiosRequestConfig<any> & {
  enabled?: boolean;
};

type TMatchesAnyParams = (filterItem?: FilterItem) => any | any;

type TMatchesAnyProps = {
  /** Format data from response of api */
  formatData?: (response: any) => MatchesAnyItem[];
  /** List of matches any instead of api */
  options?: MatchesAnyItem[];
  /** Get total from response */
  total?: (response: any) => number | number;
};

export type TConfig<TTableType = any> = {
  env?: TEnv;
  auth?: TServiceAuth;
  api?: {
    listing?: TApiGlobal;
    column?: TApiGlobal;
    filter?: TApiGlobal;
    search?: TApiGlobal;
    matchesAny?: {
      general?: Omit<TApiGlobal, 'params'> & { params?: TMatchesAnyParams };
    } & Partial<
      Record<keyof TTableType, Omit<TApiGlobal, 'params'> & { params?: TMatchesAnyParams }>
    >;
  };

  // Object just for data table performance
  // Serve for api column, filter
  object?: {
    type: number; // ObjectType of model. Ex: 6 = SURVEY-DATA-TABLE, 5 = SURVEY,
    objectId: number; // Id of model
  };
  /** Disable local storage cache  */
  disabledCache?: boolean;
};

type TGeneralTableColumnType<TTableType> = Omit<ColumnType<TTableType>, 'render'> & {
  link?: ((record: TTableType) => string) | string;
  render?: (value: any, record: TTableType, index: number, node: ReactNode) => ReactNode;
};

type TToggleColumnType<TTableType> = TGeneralTableColumnType<TTableType> & {
  value?: ((record: TTableType) => boolean) | boolean;
  disabled?: ((record: TTableType) => boolean) | boolean;
  valueKey?: keyof TTableType;
  onChange?: (checked: boolean, record: TTableType) => void;
};

type TTableColumnType<TTableType, K extends keyof TTableType | 'toggle'> = K extends 'toggle'
  ? TToggleColumnType<TTableType>
  : TGeneralTableColumnType<TTableType>;

type TSearchProps<TSearchType> = Partial<
  Pick<
    TSearchActionButton,
    'addFilterInfo' | 'isClientSearch' | 'isAddFilterFromSearchItem' | 'searchItemProps'
  >
> & {
  link?: ((record: Partial<TSearchType>) => string) | string;
  icon?: (record: Partial<TSearchType>) => ReactNode | ReactNode;
  itemMapKeys: Record<keyof Omit<TSearchItem, 'icon' | 'link'>, keyof TSearchType>;
  render?: (record: TSearchType, index: number, node: ReactNode) => ReactNode;
  onClickSearchItem?: (record?: Partial<TSearchType>) => void;
  formatData?: (response: any) => Partial<TSearchType>[];
};

type TGridViewProps<TTableType> = {
  itemMapKeys: Record<keyof TTemplateItem, keyof TTableType>;
  name?: (record: TTableType) => ReactNode | ReactNode;
  thumbnail?: (record: TTableType) => string | string;
  itemProps?: {
    removable?: boolean;
    showButton?: boolean;
    onRemove?: (record: TTableType) => void;
    removeModalProps?: TRemoveModalProps;
    buttonProps?: Omit<TThumbnailButton, 'onClick'> & {
      onClick: (record: TTableType) => void;
    };
  };
};

type TGroupByProps<TTableType> = {
  columnKeys?: (keyof TTableType)[];
  defaultSelectedKey?: keyof TTableType;
};

export type TFormatFilterValue = Partial<Record<TOperatorKey, (value: any) => unknown>>;

export interface UseDataTableListingProps<TTableType = any, TSearchType = any> {
  name?: string;
  config: TConfig<TTableType>;
  queryOptions?: {
    getTableListing?: TGetTableListing['options'];
    getColumnMetricList?: TGetColumnMetrics['options'];
    getModifyColumnList?: TGetModifyColumnList['options'];
    getSavedFilterList?: TGetSavedFilterList['options'];
    getFilterMetricList?: TGetFilterMetricList['options'];
    getSearchListing?: TGetSearchListing['options'];
  };
  table?: Omit<TableProps<TTableType>, 'columns'> & {
    /** Main key for map ids */
    mainColumnKey: keyof TTableType;
    /** Expand column key for show icon expand row */
    expandColumnKey?: keyof TTableType;
    /** columns?: Partial<Record<keyof TTableType, ColumnType<TTableType>>>; */
    columns?: {
      [K in keyof TTableType | 'toggle']?: TTableColumnType<TTableType, K>;
    };

    // Mapping sortDirection key for call api listing
    // Default: az
    sortDirectionKey?: string;
  };
  search?: TSearchProps<TSearchType>;
  filter?: {
    /** Init default filters, this filter will show in UI instead of external filters */
    defaultFilters?: FilterItem[];
    externalFilters?: FilterItem[];
    // Include data type in filters condition or not. In survey project must have datatype
    // Ex: If true => [{"SURVEY_NAME":{"contain":"323","dataType":1}}]
    // if false => [{"SURVEY_NAME":{"contain":"323"}}]
    includeDataType?: boolean;
    matchesAny?: {
      general: TMatchesAnyProps;
    } & Partial<Record<keyof TTableType, TMatchesAnyProps>>;

    /** Format filter value to call api
     * Ex: When operator is matches_any then value auto is string array: ["1", "2", "3"]
     * But sometimes you want to be a string join with , -> '1,2,3'
     * Then use formatFilterValue: {matches_any(value): value.join(',')}
     *
     */
    formatFilterValue?: TFormatFilterValue;
  };
  gridView?: TGridViewProps<TTableType>;
  groupBy?: TGroupByProps<TTableType>;
}

export interface DataTableLocalStorage<TTableType = any> {
  filter?: {
    selectedFilterId?: string;
    filters?: FilterItem[];
  };
  modeView?: TGridViewKeys;
  table?: TableProps<TTableType>;
  pagination?: PaginationProps;
  sorter?: SorterResult<TTableType>;
  groupBy?: {
    selectedKey?: string;
  };
}
