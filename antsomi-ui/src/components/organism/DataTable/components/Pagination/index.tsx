// Libraries
import React, { ReactNode, useMemo } from 'react';

// Types
import { FlexProps } from 'antd/lib';

// Components
import { Select } from '@antscorp/antsomi-ui/es/components/molecules';
import { Flex, Icon, Typography } from '@antscorp/antsomi-ui/es/components/atoms';

// Instance
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';

// Locales
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';

// Styled
import { ActionButton, PaginationWrapper } from './styled';
import { DefaultOptionType } from 'antd/es/select';

// Constants
import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE, PAGE_SIZE_OPTIONS } from '../../constants';

export type TChangePageSizePayload = {
  pageSize: number;
  page: number;
};

export interface PaginationProps extends Omit<FlexProps, 'children' | 'onChange'> {
  page?: number;
  pageSize?: number;
  total?: number;

  extraContent?: ReactNode;

  onChange?: (payload: TChangePageSizePayload) => void;
}

const { Text } = Typography;

export const Pagination: React.FC<PaginationProps> = props => {
  // Props
  const {
    pageSize = DEFAULT_PAGE_SIZE,
    page = DEFAULT_PAGE,
    total = 0,
    extraContent,
    onChange,
    ...restProps
  } = props;

  // Instance
  const { t } = i18nInstance;

  // Memo
  const paginationSizeOptions: DefaultOptionType[] = useMemo(
    () => PAGE_SIZE_OPTIONS.map(pageSize => ({ label: pageSize.toString(), value: pageSize })),
    [],
  );

  // Variables
  const disabledNext = page * pageSize >= total;
  const disabledPrevious = page === 1;

  // Handlers
  const onChangePagination = (payload: Partial<TChangePageSizePayload>) => {
    if (onChange) {
      onChange({
        page,
        pageSize,
        ...payload,
      });
    }
  };

  return (
    <PaginationWrapper
      align="center"
      justify={extraContent ? 'space-between' : 'flex-end'}
      gap={10}
      {...restProps}
    >
      {extraContent}

      <Flex
        align="center"
        gap={10}
        style={{ flexShrink: 0, height: 'fit-content', alignSelf: 'flex-end' }}
      >
        {/* Show row */}
        <Flex align="center" gap={10}>
          <Text>{t(translations.pagination.showRow).toString()}:</Text>
          <Select
            defaultValue={DEFAULT_PAGE_SIZE}
            value={pageSize}
            className="show-row-select"
            options={paginationSizeOptions}
            popupMatchSelectWidth={70}
            onChange={value => onChangePagination({ pageSize: value as number, page: 1 })}
          />
        </Flex>

        {/* Pagination */}
        <Text>{`${total ? page * pageSize - pageSize + 1 : 0} - ${Math.min(
          page * pageSize,
          total,
        ).toLocaleString()} of ${total?.toLocaleString()}`}</Text>

        {/* Actions */}
        <Flex align="center" gap={10}>
          <ActionButton onClick={() => onChangePagination({ page: 1 })} disabled={disabledPrevious}>
            <Icon type="icon-ants-first-page" size={12} />
          </ActionButton>

          <ActionButton
            disabled={disabledPrevious}
            onClick={() => onChangePagination({ page: page - 1 })}
          >
            <Icon type="icon-ants-angle-left" size={12} />
          </ActionButton>

          <ActionButton
            disabled={disabledNext}
            onClick={() => onChangePagination({ page: page + 1 })}
          >
            <Icon type="icon-ants-angle-right" size={12} />
          </ActionButton>

          <ActionButton
            onClick={() => onChangePagination({ page: Math.ceil(total / pageSize) })}
            disabled={disabledNext}
          >
            <Icon type="icon-ants-last-page" size={12} />
          </ActionButton>
        </Flex>
      </Flex>
    </PaginationWrapper>
  );
};
