// Libraries
import { ReactNode } from 'react';
import { DropdownProps } from 'antd';
import { ItemType } from 'antd/es/menu/hooks/useItems';

// Constants
import { GRID_VIEW_KEYS, TOOLBAR_ACTION_KEYS } from '../constants';

// Types
import { AddButtonProps } from '../components/AddButton';
import { TActionButton, TActionImageMap, TConfirmResponse } from '@antscorp/antsomi-ui/es/types';
import { TOperatorKey } from '@antscorp/antsomi-ui/es/types/condition';
import { ModifyColumnMetric } from '../../ModifyColumnModal';
import { SavedModifyColumnItem, TOnSaveModifyColumnArgs } from '../../ModifyColumnModal/types';
import { TextProps } from 'antd/es/typography/Text';
import { TemplateListingProps } from '../../../template';

export type TActionToolbarButtonKey<T extends string[] = []> =
  | (typeof TOOLBAR_ACTION_KEYS)[keyof typeof TOOLBAR_ACTION_KEYS]
  | T[number];

export type TGridViewKeys = (typeof GRID_VIEW_KEYS)[keyof typeof GRID_VIEW_KEYS];

export type TSearchItem = {
  id: string | number;
  label: string;
  link?: string;
  icon?: ReactNode;
  [key: string]: any;
};

/* Action Buttons types */
export type TSelectedRowButton = TActionButton & {
  dropdownProps?: DropdownProps;
};

/**
 * Represents a search action button, extending the base functionality of `TActionButton`.
 *
 * @typedef {TActionButton} TSearchActionButton
 *
 * @property {TSearchItem[]} [searchList] - An optional list of search items.
 * @property {string} [objectName] - An optional name for the object being searched show in filter section.
 * @property {(valueSearch: string) => void} [onSearch] - An optional callback function triggered when a search is performed, taking the search value as a parameter.
 */
export type TSearchActionButton = TActionButton & {
  searchList?: TSearchItem[];
  objectName?: string;
  isClientSearch?: boolean;
  isAddFilterFromSearchItem?: boolean;
  addFilterInfo?: {
    metricId?: string | number;
    operator: TOperatorKey;
  };
  searchItemProps?: TextProps;
  loading?: boolean;
  itemSearchRender?: (item: TSearchItem, index: number, node: ReactNode) => React.ReactNode;
  onSearch?: (valueSearch: string) => void;
  onClickSearchItem?: (item: TSearchItem, index: number) => void;
};

export type TColumnActionButton = TActionButton & {
  modalTitle?: string;
  savedModifyColumn?: {
    list: SavedModifyColumnItem[];
    selected?: string | number;
    loading?: boolean;
    onRemove?: (savedFilter: SavedModifyColumnItem) => Promise<TConfirmResponse>;
    onSelect?: (savedFilter: SavedModifyColumnItem) => void;
  };
  metrics?: ModifyColumnMetric[];
  onApply?: (args: TOnSaveModifyColumnArgs) => Promise<TConfirmResponse>;
};

export type TGridViewActionButton = TActionButton & {
  /** The mode of the grid view. Ex: '' */
  mode?: TGridViewKeys;
  templateListingProps?: TemplateListingProps;

  /** The callback function to change the mode of the grid view. */
  onChangeMode?: (mode: TGridViewKeys) => void;
};

export type TGroupByActionButton = TActionButton & {
  selectedKey?: string;
  options: ItemType[];
  onChange: (key: string) => void;
};

export type TToolbarActionButton<
  TActionKey extends string[] = [],
  ActionKey extends TActionToolbarButtonKey<TActionKey> = TActionToolbarButtonKey<TActionKey>,
> = ActionKey extends 'SEARCH'
  ? TSearchActionButton
  : ActionKey extends 'COLUMN'
    ? TColumnActionButton
    : ActionKey extends 'GRID_VIEW'
      ? TGridViewActionButton
      : ActionKey extends 'GROUP_BY'
        ? TGroupByActionButton
        : TActionButton;

export type TCollapse = {
  collapsed?: boolean;
  onCollapse?: (collapsed: boolean) => void;
};

export interface ToolbarProps<TActionKey extends string[] = []> {
  show?: boolean;
  addButton?: AddButtonProps | ((node: ReactNode) => ReactNode);
  actionButtons?: {
    [K in TActionToolbarButtonKey<TActionKey>]?: TToolbarActionButton<TActionKey, K>;
  };
  selectedRowButtons?: Record<string, TSelectedRowButton>;
  collapse?: TCollapse;
  /** The right content of the toolbar. */
  rightContent?: ReactNode;
}
