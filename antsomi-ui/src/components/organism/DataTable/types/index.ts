// Libraries
import { TableProps as AntdTableProps } from 'antd';
import { StoreApi } from 'zustand';

// Types
import { ToolbarProps } from './toolbar';
import { PaginationProps } from '../components';
import { FilterProps } from '../../Filter';

/* Common */
export interface TableProps<T> extends AntdTableProps<T> {
  columnWidths?: Record<number, number>;
  onColumnResize?: (data: { index: number; width: number }) => void;
}

export interface ModifyColumnProps {}

/* Props */
export interface DataTableProps<TTableDataType = any>
  extends Partial<DataTableState<TTableDataType>> {
  contentRender?: () => React.ReactNode;
  store?: StoreApi<DataTableStore<any>> | null;
  className?: string;
}

/* Store */
export interface DataTableState<TTableDataType> {
  name: string;
  modifyColumn: ModifyColumnProps;
  filter: FilterProps;
  toolbar: ToolbarProps;
  table: TableProps<TTableDataType>;
  pagination: PaginationProps;
}

export interface DataTableStore<TTableDataType = any> {
  /* State */
  // state: DataTableState<TTableDataType>;
  name: string;
  modifyColumn: ModifyColumnProps;
  filter: FilterProps;
  toolbar: ToolbarProps;
  table: TableProps<TTableDataType>;
  pagination: PaginationProps;

  /* Action */
  setState: (newState: Partial<DataTableState<TTableDataType>>) => void;
  setModifyColumn: (modifyColumn: RecursivePartial<ModifyColumnProps>) => void;
  setFilter: (filter: Partial<FilterProps>) => void;
  setPagination: (pagination: Partial<PaginationProps>) => void;
  setToolbar: (toolbar: Partial<ToolbarProps>) => void;
  setTable: (table: Partial<TableProps<TTableDataType>>) => void;
}

export * from './theme';
export * from './toolbar';
