import React from 'react';
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';

// Icons
import {
  PivotTableChartIcon,
  SearchIcon,
  TableIcon,
  ViewColumnIcon,
} from '@antscorp/antsomi-ui/es/components/icons';

// Types
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';
import { TActionOptions } from '@antscorp/antsomi-ui/es/types';

export const TOOLBAR_ACTION_KEYS = {
  SEARCH: 'SEARCH',
  COLUMN: 'COLUMN',
  EXPLORE: 'EXPLORE',
  EXPORT: 'EXPORT',
  CALENDAR: 'CALENDAR',
  TABLE: 'TABLE',
  COMPARE: 'COMPARE',
  NODE_VIEW: 'NODE_VIEW',
  GRID_VIEW: 'GRID_VIEW',
  MORE: 'MORE',
  GROUP_BY: 'GROUP_BY',
} as const;

const { SEARCH, COLUMN, EXPLORE, EXPORT, <PERSON><PERSON><PERSON>AR, TABLE, COMPARE, GRID_VIEW, GROUP_BY } =
  TOOLBAR_ACTION_KEYS;

export const TOOLBAR_ACTION_OPTIONS: TActionOptions = {
  [SEARCH]: {
    key: SEARCH,
    label: i18nInstance.t(translations.dataTableToolbar.toolbarActions.search).toString(),
    icon: <SearchIcon />,
  },
  [COLUMN]: {
    key: COLUMN,
    label: 'Column',
    icon: <ViewColumnIcon />,
  },
  [EXPLORE]: {
    key: EXPLORE,
    label: 'Explore',
    icon: 'icon-ants-explore',
  },
  [EXPORT]: {
    key: EXPORT,
    label: 'Export',
    icon: 'file-download',
  },
  [CALENDAR]: {
    key: CALENDAR,
    label: 'Calendar',
    icon: 'file-download',
  },
  [TABLE]: {
    key: TABLE,
    label: 'Table',
    icon: 'file-download',
  },
  [COMPARE]: {
    key: COMPARE,
    label: 'Compare',
    icon: 'file-download',
  },
  [GRID_VIEW]: {
    key: GRID_VIEW,
    label: 'Grid View',
    icon: <TableIcon />,
  },
  [GROUP_BY]: {
    key: GROUP_BY,
    label: 'Group By',
    icon: <PivotTableChartIcon />,
  },
};

export const GRID_VIEW_KEYS = {
  TABLE: 'TABLE',
  GRID: 'GRID',
} as const;
