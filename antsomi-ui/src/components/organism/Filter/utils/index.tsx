// Libraries
import React from 'react';
import { MenuProps } from 'antd';
import { isEmpty } from 'lodash';
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';
import dayjs from 'dayjs';

// Types
import { FilterItem, FilterMetricItem, TApiFilter, TFormatFilterValue } from '../types';
import { SavedFilter } from '@antscorp/antsomi-ui/es/models/DataTable/SavedFilter';

// Components
import { Popover, Typography, Icon, FormatInputNumber, Flex, Input } from '../../../atoms';
import {
  DATE_TIME_FORMAT,
  DATE_TIME_FORMAT_DISPLAY,
  METRIC_TYPE,
} from '@antscorp/antsomi-ui/es/constants';
import { FilterProps, Menu } from '../..';
import { DatePicker, MatchesAnySelect } from '../../../molecules';

// Styled
import { FilterMetricLabel } from '../styled';
import { OPERATORS_CODE } from '@antscorp/antsomi-ui/es/constants/condition';

// Translations
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';
import {
  EXCEPTION_OFF_FILTER_CLASS,
  METRIC_MAP_NUMBER_TYPE,
  METRIC_MAP_STRING_TYPE,
} from '../constants';

type MenuItem = Required<MenuProps>['items'][number];

const { MATCHES, MATCHES_ANY, NOT_MATCHES, ON, BEFORE, BEFORE_DATE, AFTER, AFTER_DATE } =
  OPERATORS_CODE;

type TSerializeFilterMetricsToMenuItems = {
  filterMetrics: FilterMetricItem[];
  level?: number;
  onSelectFilterMetric?: MenuProps['onSelect'];
  onClickFilterMetric?: (filterMetric: FilterMetricItem) => void;
};

type TRenderValueField = {
  filterMetric: FilterMetricItem;
  operator: string;
  value?: any;
  matchesAny?: FilterProps['matchesAny'];
  onChangeValue?: (value: any) => void;
  onLoadMore?: () => void;
};

type TGetFilterMetricByName = {
  filterMetrics: FilterMetricItem[];
  name: string;
};

const { t } = i18nInstance;
const { Text } = Typography;

const MAX_SELECTED_ITEM = 100;
const MAX_LENGTH_EACH_ITEM = 200;

/* Filter */
export const serializeFilterMetricsToMenuItems = ({
  filterMetrics = [],
  level = 0,
  onSelectFilterMetric,
  onClickFilterMetric,
}: TSerializeFilterMetricsToMenuItems): MenuItem[] => {
  const isShowPopover = level >= 1;

  return filterMetrics.map(metric => {
    const isHasChild = Array.isArray(metric.children) && metric.children.length;

    /* Format label of filter metric */
    const label = (
      <FilterMetricLabel align="center" justify="space-between">
        <Typography.Text className="ants-w-full" ellipsis={{ tooltip: true }}>
          {metric.name}
        </Typography.Text>
        {isHasChild && isShowPopover ? <Icon type="icon-ants-expand-more" size={20} /> : null}
      </FilterMetricLabel>
    );

    const menuItem: MenuItem = {
      key: metric.id,
      label:
        !isEmpty(metric.children) && isShowPopover ? (
          <Popover
            content={
              <Menu
                items={serializeFilterMetricsToMenuItems({
                  filterMetrics: metric.children || [],
                  level: level + 1,
                  onSelectFilterMetric,
                  onClickFilterMetric,
                })}
                mode="inline"
                inlineIndent={5}
              />
            }
            placement="rightTop"
            arrow={false}
            align={{ offset: [10, -10] }}
            overlayInnerStyle={{ width: 250, padding: 0 }}
          >
            {label}
          </Popover>
        ) : (
          label
        ),
    };

    return {
      ...menuItem,
      ...(!!metric.children &&
        !isEmpty(metric.children) &&
        !level && {
          children: serializeFilterMetricsToMenuItems({
            filterMetrics: metric.children || [],
            level: level + 1,
            onSelectFilterMetric,
            onClickFilterMetric,
          }),
        }),
      onClick: () => onClickFilterMetric && !isHasChild && onClickFilterMetric(metric),
    };
  });
};

/**
 * Flattens an array of filter metrics, including their nested children, into a single-level array.
 *
 * @param {FilterMetric[]} filterMetrics - The array of filter metrics to flatten. Each filter metric can contain
 *                                         nested children which will also be included in the flattened result.
 * @returns {FilterMetric[]} A single-level array of filter metrics, including all nested children.
 *
 * @example
 * // Given the following input:
 * const filterMetrics = [
 *   {
 *     id: 1,
 *     name: 'Metric 1',
 *     children: [
 *       {
 *         id: 2,
 *         name: 'Metric 1.1',
 *         children: []
 *       },
 *       {
 *         id: 3,
 *         name: 'Metric 1.2',
 *         children: [
 *           {
 *             id: 4,
 *             name: 'Metric 1.2.1',
 *             children: []
 *           }
 *         ]
 *       }
 *     ]
 *   },
 *   {
 *     id: 5,
 *     name: 'Metric 2',
 *     children: []
 *   }
 * ];
 *
 * // The output will be:
 * const result = flatFilterMetrics(filterMetrics);
 * // result will be:
 * // [
 * //   { id: 2, name: 'Metric 1.1', children: [] },
 * //   { id: 4, name: 'Metric 1.2.1', children: [] },
 * //   { id: 3, name: 'Metric 1.2', children: [...] },
 * //   { id: 1, name: 'Metric 1', children: [...] },
 * //   { id: 5, name: 'Metric 2', children: [] }
 * // ]
 */
export const flatFilterMetrics = (filterMetrics: FilterMetricItem[]): FilterMetricItem[] =>
  filterMetrics.reduce<FilterMetricItem[]>((acc, cur) => {
    if (Array.isArray(cur.children) && cur.children.length) {
      return [...acc, ...flatFilterMetrics(cur.children), cur];
    }
    return [...acc, cur];
  }, []);

/* Render Value Field */
export const renderValueField = ({
  filterMetric,
  operator,
  value,
  matchesAny,
  onLoadMore = () => {},
  onChangeValue = () => {},
}: TRenderValueField) => {
  const { dataType, name } = filterMetric;
  switch (operator) {
    case OPERATORS_CODE.GREATER_THAN:
    case OPERATORS_CODE.GREATER_THAN_EQUAL:
    case OPERATORS_CODE.LESS_THAN:
    case OPERATORS_CODE.LESS_THAN_EQUAL: {
      return (
        <FormatInputNumber
          value={value}
          placeholder={`${t(translations.format)}: 1,234.560`}
          onChange={onChangeValue}
          style={{ width: '100%' }}
        />
      );
    }
    case OPERATORS_CODE.BETWEEN: {
      const renderBetweenValue = () => {
        const [firstValue, secondValue] = typeof value === 'string' ? value?.split(' AND ') : [];

        switch (dataType) {
          case METRIC_TYPE.DATE:
          case METRIC_TYPE.DATE_TIME: {
            return (
              <>
                <DatePicker
                  placeholder={DATE_TIME_FORMAT_DISPLAY.FILTER_CONDITION}
                  format={DATE_TIME_FORMAT_DISPLAY.FILTER_CONDITION}
                  allowClear={false}
                  popupClassName={EXCEPTION_OFF_FILTER_CLASS}
                  value={dayjs(firstValue).isValid() ? dayjs(firstValue) : undefined}
                  onChange={value => onChangeValue(`${value || ''} AND ${secondValue || ''}`)}
                />
                <Text style={{ flexShrink: 0, textTransform: 'lowercase' }}>
                  {t(translations.and.title).toString()}
                </Text>
                <DatePicker
                  placeholder={DATE_TIME_FORMAT_DISPLAY.FILTER_CONDITION}
                  format={DATE_TIME_FORMAT_DISPLAY.FILTER_CONDITION}
                  allowClear={false}
                  popupClassName={EXCEPTION_OFF_FILTER_CLASS}
                  value={dayjs(secondValue).isValid() ? dayjs(secondValue) : undefined}
                  onChange={value => onChangeValue(`${value || ''} AND ${secondValue || ''}`)}
                />
              </>
            );
          }

          default: {
            return (
              <>
                <FormatInputNumber
                  value={firstValue || undefined}
                  max={secondValue}
                  style={{ width: '100%' }}
                  onChange={value => onChangeValue(`${value || ''} AND ${secondValue || ''}`)}
                />
                <Text style={{ flexShrink: 0, textTransform: 'lowercase' }}>
                  {t(translations.and.title).toString()}
                </Text>
                <FormatInputNumber
                  value={secondValue || undefined}
                  min={firstValue}
                  style={{ width: '100%' }}
                  onChange={value => onChangeValue(`${firstValue || ''} AND ${value || ''}`)}
                />
              </>
            );
          }
        }
      };

      return (
        <Flex vertical gap={20}>
          {renderBetweenValue()}
        </Flex>
      );
    }
    case OPERATORS_CODE.EQUALS:
    case OPERATORS_CODE.IS:
    case OPERATORS_CODE.NOT_EQUALS: {
      switch (dataType) {
        // Return InputNumber Field
        case METRIC_TYPE.NUMBER:
          return (
            <FormatInputNumber
              value={value}
              style={{ width: '100%' }}
              onChange={value => onChangeValue(value)}
            />
          );

        // Return Input Field
        default:
          return (
            <Input
              placeholder={t(translations.inputYourValue.title)}
              onChange={e => onChangeValue(e.target.value)}
            />
          );
      }
    }
    case OPERATORS_CODE.CONTAINS:
    case OPERATORS_CODE.CONTAIN:
    case OPERATORS_CODE.DOES_NOT_CONTAIN:
    case OPERATORS_CODE.DOESNT_CONTAIN:
    case OPERATORS_CODE.START_WITH:
    case OPERATORS_CODE.END_WITH:
    case OPERATORS_CODE.NOT_END_WITH:
    case OPERATORS_CODE.NOT_START_WITH: {
      return (
        <Input
          value={value}
          placeholder={t(translations.inputYourValue.title)}
          onChange={e => onChangeValue(e.target.value)}
        />
      );
    }
    case OPERATORS_CODE.MATCHES:
    case OPERATORS_CODE.MATCHES_ANY:
    case OPERATORS_CODE.NOT_MATCHES: {
      const { list, isLoading } = matchesAny || {};

      return (
        <MatchesAnySelect
          maxSelectedItem={MAX_SELECTED_ITEM}
          maxLengthEachItem={MAX_LENGTH_EACH_ITEM}
          selectedItems={Array.isArray(value) ? value : []}
          popupClassName={EXCEPTION_OFF_FILTER_CLASS}
          objectName={name}
          items={list}
          loading={isLoading}
          onChange={onChangeValue}
          onLoadMore={onLoadMore}
        />
      );
    }
    case OPERATORS_CODE.AFTER:
    case OPERATORS_CODE.AFTER_DATE:
    case OPERATORS_CODE.BEFORE:
    case OPERATORS_CODE.ON:
    case OPERATORS_CODE.BEFORE_DATE: {
      return (
        <DatePicker
          placeholder={DATE_TIME_FORMAT_DISPLAY.FILTER_CONDITION}
          format={DATE_TIME_FORMAT_DISPLAY.FILTER_CONDITION}
          allowClear={false}
          getPopupContainer={trigger => trigger.parentElement as HTMLElement}
          placement="bottomLeft"
          popupClassName={EXCEPTION_OFF_FILTER_CLASS}
          value={value && dayjs(value).isValid() ? dayjs(value) : undefined}
          onChange={onChangeValue}
        />
      );
    }

    default:
      return null;
  }
};

/**
 * Retrieves a filter metric by its name from a collection of filter metrics.
 *
 * @typedef {Object} Metric
 * @property {string} name - The name of the metric.
 * @property {any} [otherProperties] - Other properties of the metric.
 *
 * @typedef {Object} TGetFilterMetricByName
 * @property {Metric[]} filterMetrics - An array of filter metrics.
 * @property {string} name - The name of the metric to find.
 *
 * @param {TGetFilterMetricByName} params - The parameters containing filter metrics and the name of the metric to find.
 * @returns {Metric|undefined} The metric with the specified name, or undefined if not found.
 */
export const getFilterMetricByName = ({
  filterMetrics,
  name,
}: TGetFilterMetricByName): FilterMetricItem | undefined => {
  const flattenMetrics = flatFilterMetrics(filterMetrics);

  return flattenMetrics.find(metric => metric.name === name);
};

export const mapFiltersToRules = (filters: FilterItem[]): SavedFilter['rules'] =>
  filters.map(({ column, operator, value, dataType }) => ({
    dataType: METRIC_MAP_STRING_TYPE[dataType || 'string'],
    metricCode: column,
    operatorValue: operator,
    filterValue: value,
  }));

export const mapRulesToFilters = (rules: SavedFilter['rules']): FilterItem[] =>
  rules.map(({ dataType, metricCode, operatorValue, filterValue }) => ({
    dataType: METRIC_MAP_NUMBER_TYPE[dataType || 1],
    column: metricCode,
    operator: operatorValue,
    value: filterValue as any,
  }));

export const mapFiltersToApiFilters = (
  filters: FilterItem[],
  includeDataType: boolean = false,
  formatFilterValue?: TFormatFilterValue,
): TApiFilter => {
  const serializedFilters = filters.map(({ column, operator, value, dataType }) => {
    let serializeValue: any = '';

    if (formatFilterValue && formatFilterValue[operator]) {
      serializeValue = formatFilterValue[operator](value);
    } else {
      switch (operator) {
        case MATCHES:
        case MATCHES_ANY:
        case NOT_MATCHES: {
          if (Array.isArray(value)) {
            // Check if value is object the return object.key otherwise return this value
            serializeValue = value.map(item =>
              typeof item === 'object' ? item.key || item : item,
            );
          }
          break;
        }
        /** Date time */
        case AFTER:
        case AFTER_DATE:
        case BEFORE:
        case ON:
        case BEFORE_DATE: {
          if (dayjs(value).isValid()) {
            serializeValue = dayjs(value).format(DATE_TIME_FORMAT.FILTER_CONDITION);
          }
          break;
        }
        default:
          serializeValue = value;
          break;
      }
    }

    return {
      [column]: {
        [operator]: serializeValue,
        ...(includeDataType && { dataType: METRIC_MAP_STRING_TYPE[dataType || 'string'] }),
      },
    };
  });

  return serializedFilters;
};
