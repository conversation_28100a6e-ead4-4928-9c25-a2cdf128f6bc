// Constants
import { MatchesAnyItem } from '../../../molecules';
import { FILTER_ACTION_KEYS } from '../constants';

// Types
import { TActionButton, TConfirmResponse, TOperatorKey } from '@antscorp/antsomi-ui/es/types';

export type TFormatFilterValue = Partial<Record<TOperatorKey, (value: any) => unknown>>;

export type TActionFilterButtonKey<T extends string[] = []> =
  | (typeof FILTER_ACTION_KEYS)[keyof typeof FILTER_ACTION_KEYS]
  | T[number];

export interface SavedFilterItem {
  id: string;
  name: string;
  removable?: boolean;
}

export interface FilterMetricItem {
  id: string | number;
  name: string;
  dataType?: string;
  children?: FilterMetricItem[];
  operators?: { label: string; value: string }[];
}

export interface SavedFilter {
  list?: SavedFilterItem[];
  selected?: string | number;
  onRemove?: (savedFilter: SavedFilterItem) => Promise<TConfirmResponse>;
  onSelect?: (savedFilter: SavedFilterItem) => void;
}

export interface FilterItem {
  column: string | number;
  dataType?: string;
  operator: string;
  type?: number;
  value: any;
}

export type TOnSaveFilterArgs = {
  existSavedFilter?: SavedFilterItem;
  name: string;
  filters: FilterItem[];
};

export type TFilterActionButton<T extends TActionFilterButtonKey> = TActionButton &
  T extends 'CLOSE'
  ? {}
  : {};

export interface FilterProps<TActionKey extends string[] = []> {
  isFilterActive?: boolean;
  savedFilter?: SavedFilter;
  filterMetrics?: FilterMetricItem[];
  filters?: FilterItem[];
  matchesAny?: {
    list?: MatchesAnyItem[];
    isLoading?: boolean;
  };
  rightContent?: React.ReactNode;
  leftContent?: React.ReactNode;
  actionButtons?: Partial<Record<TActionFilterButtonKey<TActionKey>, TActionButton>>;

  /* Handle callback when filters changed */
  onChangeFilters?: (filters: FilterItem[]) => void;
  onSaveFilter?: (args: TOnSaveFilterArgs) => Promise<TConfirmResponse>;

  /** Handle callback when filter condition changed */
  onChangeFilterCondition?: (filterItem: FilterItem) => void;

  /** Handle callback when matches any load more */
  onMatchesAnyLoadMore?: (metricId?: FilterMetricItem['id']) => void;

  className?: string;
}

export type TApiFilter = Record<string, any>[];

// Store
export interface FilterStore extends FilterProps {
  setFilter: (filter: Partial<FilterProps>) => void;
}
