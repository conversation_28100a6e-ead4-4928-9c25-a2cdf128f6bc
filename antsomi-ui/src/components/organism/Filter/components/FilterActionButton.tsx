// Libraries
import React, { memo, useState } from 'react';

// Components
import { Flex } from '@antscorp/antsomi-ui/es/components/atoms';
import { Modal } from '@antscorp/antsomi-ui/es/components/molecules';

// Utils
import { renderActionButtons } from '@antscorp/antsomi-ui/es/utils';

// Styled
import { EXCEPTION_OFF_FILTER_CLASS, FILTER_ACTION_OPTIONS } from '../constants';
import { ActionButton } from '@antscorp/antsomi-ui/es/styled';

// Context
import { useFilterContext } from '../hooks';
import { TActionFilterButtonKey } from '../types';

// Types
import { TActionButton } from '@antscorp/antsomi-ui/es/types';
import { SaveFilterPopover } from './SaveFilterPopover';

// Constants
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';

interface FilterActionButtonProps {}

const { t } = i18nInstance;

export const FilterActionButton: React.FC<FilterActionButtonProps> = memo(() => {
  // Store
  const actionButtons = useFilterContext(store => store.actionButtons);

  const {
    filters,
    savedFilter,
    onSaveFilter = () => Promise.resolve(),
    onChangeFilters = () => {},
    setFilter = () => {},
  } = useFilterContext(store => store);

  // State
  const [state, setState] = useState({
    openSaveFilterPopover: false,
    saveFilterLoading: false,
  });

  // Variables
  const { saveFilterLoading, openSaveFilterPopover } = state;

  // Hooks
  const [modal, context] = Modal.useModal();

  // Handlers
  const handleClickActionButton = (key: TActionFilterButtonKey) => {
    switch (key) {
      case 'CLOSE':
        setFilter({
          isFilterActive: false,
        });
        break;
      case 'RESET': {
        onChangeFilters([]);
        break;
      }

      default:
        break;
    }
  };

  /* Handle render action buttons */
  const renderComponent = ({ key, label, buttonProps, icon, customRender }: TActionButton) => {
    const actionButton = (
      <ActionButton
        key={key}
        type="text"
        {...buttonProps}
        // Handle hide action button except close button when filter is active
        onClick={e => {
          if (buttonProps && buttonProps.onClick) {
            buttonProps.onClick(e);
          }

          handleClickActionButton(key as TActionFilterButtonKey);
        }}
        $hide={key !== 'CLOSE' && !filters?.length}
      >
        <Flex align="center" justify="center" style={{ height: 24, width: 24, flexShrink: 0 }}>
          {icon}
        </Flex>
        {`${label}`.toUpperCase()}
      </ActionButton>
    );

    // If action has customer render props then return it not handle more
    if (customRender) {
      return actionButton;
    }

    switch (key as TActionFilterButtonKey) {
      case 'SAVE': {
        return (
          <SaveFilterPopover
            open={openSaveFilterPopover}
            onApply={async name => {
              const existSavedFilter = savedFilter?.list?.find(
                savedFilter => savedFilter.name.trim() === name,
              );

              if (existSavedFilter) {
                modal.confirm({
                  title: t(translations.dataTableFilter.replaceFilterSet.title).toString(),
                  centered: true,
                  content: t(translations.dataTableFilter.replaceFilterSet.description, {
                    filterName: name,
                  }).toString(),
                  icon: <div />,
                  wrapClassName: EXCEPTION_OFF_FILTER_CLASS,
                  okText: t(translations.global.replace).toString(),
                  onOk: async () =>
                    new Promise((resolve, reject) => {
                      onSaveFilter({
                        filters: filters || [],
                        name,
                        existSavedFilter,
                      })
                        .then(() => resolve(true))
                        .catch(() => reject());
                    }),
                });
              } else {
                setState(prev => ({ ...prev, saveFilterLoading: true }));

                await onSaveFilter({
                  filters: filters || [],
                  name,
                });

                setState(prev => ({
                  ...prev,
                  saveFilterLoading: false,
                  openSaveFilterPopover: false,
                }));
              }
            }}
            onCancel={() => setState(prev => ({ ...prev, openSaveFilterPopover: false }))}
            loading={saveFilterLoading}
            onOpenChange={open => setState(prev => ({ ...prev, openSaveFilterPopover: open }))}
          >
            {actionButton}
          </SaveFilterPopover>
        );
      }

      default:
        return actionButton;
    }
  };

  return (
    <>
      <Flex align="center" gap={2}>
        {renderActionButtons({
          renderComponent,
          // CLOSE ACTION ALWAYS VISIBLE
          actionButtons: { ...actionButtons, CLOSE: { ...actionButtons?.CLOSE } },
          defaultActionOptions: FILTER_ACTION_OPTIONS,
        })}
      </Flex>
      {context}
    </>
  );
});
