// Libraries
import React, { useCallback, useState } from 'react';
import { Meta, StoryObj } from '@storybook/react';

// Components
import { Filter } from './Filter';

// Types
import { FilterItem, FilterMetricItem, FilterProps, TOnSaveFilterArgs } from './types';
import { title } from 'process';

const meta: Meta<typeof Filter> = {
  component: Filter,
  title: 'Organism/Filter',
  argTypes: {},
  parameters: {
    docs: {
      description: {
        component: 'This component supports displaying filter UI',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof Filter>;

const DefaultFilter = (args: FilterProps) => {
  const [state, setState] = useState<any>({
    filterMetrics: [
      {
        id: '',
        name: 'Attributes',
        dataType: 'string',
        children: [
          {
            id: 'SURVEY_ID',
            name: 'Project ID',
            dataType: 'string',
            children: [],
            operators: [
              {
                label: 'matches any',
                value: 'matches_any',
              },
            ],
          },
          {
            id: 'SURVEY_NAME',
            name: 'Project name',
            dataType: 'string',
            children: [],
            operators: [
              {
                label: 'contains',
                value: 'contain',
              },
              {
                label: 'does not contain',
                value: 'does_not_contain',
              },
              {
                label: 'is',
                value: 'is',
              },
              {
                label: 'start with',
                value: 'start_with',
              },
            ],
          },
          {
            id: 'STATUS',
            name: 'Status',
            dataType: 'string',
            children: [],
            operators: [
              {
                label: 'matches any',
                value: 'matches_any',
              },
            ],
          },
          {
            id: 'RESPONSES',
            name: 'Responses',
            dataType: 'string',
            children: [],
            operators: [
              {
                label: 'greater than',
                value: 'greater_than',
              },
              {
                label: 'greater than or equal',
                value: 'greater_than_equal',
              },
              {
                label: 'less than',
                value: 'less_than',
              },
              {
                label: 'less than or equal',
                value: 'less_than_equal',
              },
              {
                label: 'between',
                value: 'between',
              },
              {
                label: 'equal to',
                value: 'equals',
              },
              {
                label: 'not equal to',
                value: 'not_equals',
              },
              {
                label: 'exists',
                value: 'exists',
              },
              {
                label: 'not exist',
                value: 'not_exists',
              },
            ],
          },
          {
            id: 'CTIME',
            name: 'Created on',
            dataType: 'date',
            children: [],
            operators: [
              {
                label: 'after',
                value: 'after',
              },
              {
                label: 'on',
                value: 'on',
              },
              {
                label: 'before',
                value: 'before',
              },
            ],
          },
        ],
        operators: [],
      },
    ],
    filters: [],
    matchesAny: {
      isLoading: false,
      list: [],
    },
    savedFilter: {
      list: [
        {
          id: 'recent',
          name: 'Recent',
          removable: false,
        },
      ],
      selected: 'recent',
    },
  });
  const { filterMetrics, matchesAny, savedFilter, filters } = state;

  const onChangeFilterCondition = useCallback((filterItem: FilterItem) => {
    const { operator } = filterItem;

    if (['matches_any', 'matches'].includes(operator)) {
      setState(prev => ({
        ...prev,
        matchesAny: {
          ...prev.matchesAny,
          list: Array.from({ length: 10 }).map((_, index) => ({
            key: index + 1,
            title: `Title ${index + 1}`,
          })),
        },
      }));
    }

    console.log({ filterItem });
  }, []);

  const onChangeFilters = useCallback((filters: FilterItem[]) => {
    console.log({ filters });
    setState(prev => ({ ...prev, filters }));
  }, []);

  const onMatchesAnyLoadMore = useCallback((metricId?: FilterMetricItem['id']) => {
    console.log({ metricId });
  }, []);

  const onSaveFilter = useCallback(async (args: TOnSaveFilterArgs) => {
    console.log({ args });

    return true;
  }, []);

  return (
    <Filter
      filters={filters}
      filterMetrics={filterMetrics}
      savedFilter={{
        list: savedFilter.list,
        selected: savedFilter.selected,
      }}
      matchesAny={{
        isLoading: false,
        list: matchesAny.list,
      }}
      onChangeFilterCondition={onChangeFilterCondition}
      onChangeFilters={onChangeFilters}
      onMatchesAnyLoadMore={onMatchesAnyLoadMore}
      onSaveFilter={onSaveFilter}
    />
  );
};

export const Default: Story = {
  render: args => <DefaultFilter {...args} />,
  parameters: {
    docs: {
      source: {
        state: 'open',
        type: 'code',
        code: `
const DefaultFilter = (args: FilterProps) => {
  const [state, setState] = useState<any>({
    filterMetrics: [
      {
        id: '',
        name: 'Attributes',
        dataType: 'string',
        children: [
          {
            id: 'SURVEY_ID',
            name: 'Project ID',
            dataType: 'string',
            children: [],
            operators: [
              {
                label: 'matches any',
                value: 'matches_any',
              },
            ],
          },
          {
            id: 'SURVEY_NAME',
            name: 'Project name',
            dataType: 'string',
            children: [],
            operators: [
              {
                label: 'contains',
                value: 'contain',
              },
              {
                label: 'does not contain',
                value: 'does_not_contain',
              },
              {
                label: 'is',
                value: 'is',
              },
              {
                label: 'start with',
                value: 'start_with',
              },
            ],
          },
          {
            id: 'STATUS',
            name: 'Status',
            dataType: 'string',
            children: [],
            operators: [
              {
                label: 'matches any',
                value: 'matches_any',
              },
            ],
          },
          {
            id: 'RESPONSES',
            name: 'Responses',
            dataType: 'string',
            children: [],
            operators: [
              {
                label: 'greater than',
                value: 'greater_than',
              },
              {
                label: 'greater than or equal',
                value: 'greater_than_equal',
              },
              {
                label: 'less than',
                value: 'less_than',
              },
              {
                label: 'less than or equal',
                value: 'less_than_equal',
              },
              {
                label: 'between',
                value: 'between',
              },
              {
                label: 'equal to',
                value: 'equals',
              },
              {
                label: 'not equal to',
                value: 'not_equals',
              },
              {
                label: 'exists',
                value: 'exists',
              },
              {
                label: 'not exist',
                value: 'not_exists',
              },
            ],
          },
          {
            id: 'CTIME',
            name: 'Created on',
            dataType: 'date',
            children: [],
            operators: [
              {
                label: 'after',
                value: 'after',
              },
              {
                label: 'on',
                value: 'on',
              },
              {
                label: 'before',
                value: 'before',
              },
            ],
          },
        ],
        operators: [],
      },
    ],
    filters: [],
    matchesAny: {
      isLoading: false,
      list: [],
    },
    savedFilter: {
      list: [
        {
          id: 'recent',
          name: 'Recent',
          removable: false,
        },
      ],
      selected: 'recent',
    },
  });
  const { filterMetrics, matchesAny, savedFilter, filters } = state;

  const onChangeFilterCondition = useCallback((filterItem: FilterItem) => {
    const { operator } = filterItem;

    if (['matches_any', 'matches'].includes(operator)) {
      setState(prev => ({
        ...prev,
        matchesAny: {
          ...prev.matchesAny,
          list: Array.from({ length: 10 }).map((_, index) => ({
            key: index + 1,
            title: "Title " + index + 1,
          })),
        },
      }));
    }

    console.log({ filterItem });
  }, []);

  const onChangeFilters = useCallback((filters: FilterItem[]) => {
    console.log({ filters });
    setState(prev => ({ ...prev, filters }));
  }, []);

  const onMatchesAnyLoadMore = useCallback((metricId?: FilterMetricItem['id']) => {
    console.log({ metricId });
  }, []);

  const onSaveFilter = useCallback(async (args: TOnSaveFilterArgs) => {
    console.log({ args });

    return true;
  }, []);

  return (
    <Filter
      filters={filters}
      filterMetrics={filterMetrics}
      savedFilter={{
        list: savedFilter.list,
        selected: savedFilter.selected,
      }}
      matchesAny={{
        isLoading: false,
        list: matchesAny.list,
      }}
      onChangeFilterCondition={onChangeFilterCondition}
      onChangeFilters={onChangeFilters}
      onMatchesAnyLoadMore={onMatchesAnyLoadMore}
      onSaveFilter={onSaveFilter}
    />
  );
};
        `,
      },
    },
  },
};
