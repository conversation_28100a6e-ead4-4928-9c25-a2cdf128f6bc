/* eslint-disable react/no-unescaped-entities */
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable no-multi-str */
import { useMemo, useEffect, useRef, useState, memo } from 'react';
import { Helmet } from 'react-helmet';

import MessageComponent from './components/MessageComponent';
import {
  Spin,
  Tooltip,
  Icon,
  Button,
  SelectAccount,
  QuillEditor,
} from '@antscorp/antsomi-ui/es/components';
import Service from './Service';

import {
  WrapperContent,
  WrapperContentInput,
  WrapperEditor,
  WrapperIconEditor,
  WrapperInputFile,
  WrapperLable,
  WrapperLeftContent,
  WrapperLinkItemFiles,
  WrapperMessageContent,
  WrapperRightContent,
  WrapperTextEdit,
  WrapperTextInput,
} from './styled';
import { formatAccountId, formatDatarender, handleValidateContent } from './util';
import { get, keyBy } from 'lodash';
import { TCallBack } from '../../molecules/QuillEditor/QuillEditor';

type TFileZendesk = {
  token: string;
  url: string;
  size: number;
  file_name: string;
};

const initValueInput = {
  originTitle: 'Create new ticket',
  title: '',
  feature: {},
  ticketType: {},
  priority: {},
  category: {},
  ownerId: null,
  followers: [],
  message: '',
  files: [],
  fileZendesk: [] as TFileZendesk[],
  isChanged: false,
  referenceUrl: window.location.href,
};

const Content = ({
  apiKey,
  domain,
  portalId,
  token,
  action,
  ticketId,
  listUsers,
  domainTicket,
  config,
  ticketDetails,
  listComment,
  userId,
  refetchComments,
  refetchTicket,
  isLoadingComment,
  isLoadingDetails,
  isLoadingDataSouce,
  accountManage,
  timeZone,
  initOwnerId,
  isAllAccount,
  dataSelectInput,
  setDataSelectInput,
  email,
  domainUpload,
}) => {
  const [isOpenToast, setIsOpenToast] = useState({
    isOpen: false,
    status: 'error',
    messages: '',
  });
  const [textValue, setTextValue] = useState('');
  const [isEmptyField, setIsEmptyField] = useState(false);
  const [dataSelects, setDataSelect] = useState(dataSelectInput);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingUpload, setIsLoadingUpload] = useState(false);
  const [isLoadingFollower, setIsLoadingFollower] = useState(false);
  const [valueInput, setValueInput] = useState(initValueInput);
  const [fileInputKey, setFileInputKey] = useState(0);

  const [errFile, setErrFile] = useState({
    isError: false,
    message: '',
  });
  const isUpdate = action === 'edit' && ticketId;
  const valueInputRef = useRef(valueInput);
  valueInputRef.current = valueInput;

  const updateValueInput = value => {
    setValueInput(prev => ({
      ...prev,
      ...value,
      isChanged: true,
    }));
  };

  const handleOnchangeInput = e => {
    const { name, value } = e.target;

    updateValueInput({
      [name]: value,
    });
  };

  const browserTitle = useMemo(() => {
    if (valueInput.title && valueInput.title.trim()) {
      return valueInput.title;
    }

    return valueInput.originTitle;
  }, [valueInput.title, valueInput.originTitle]);

  const getCustomFields = () => {
    if (dataSelects && dataSelects.length) return;

    Service.tickets.callApi
      .getCustomFields({}, domainTicket, token, config, userId, 'get-custom-fields')
      .then(res => {
        if (res.code === 200) {
          setDataSelect(res?.data?.fields);
          setDataSelectInput(res?.data?.fields);
        }
      })
      .catch(err => {
        // console.log('err ===>', err)
      });
  };

  const fetchUpdateComment = params => {
    Service.tickets.callApi
      .updateComment(
        params,
        domainTicket,
        token,
        config,
        userId,
        'create-ticket-comment',
        ticketId,
        portalId,
      )
      .then(res => {
        if (res.code === 200) {
          refetchComments();
          updateValueInput({ message: '', files: [] });
          setTextValue('');
          // handleCloseDrawer();
        }
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const fetchUpdateFollwers = params => {
    setIsLoadingFollower(true);
    Service.tickets.callApi
      .updateFollowers(params, domainTicket, token, config, userId, ticketId, portalId)
      .then(res => {
        setIsLoadingFollower(false);
        refetchTicket();
      })
      .catch(err => {
        refetchTicket();
        setIsLoadingFollower(false);
      });
  };
  useEffect(() => {
    if (
      Object.keys(ticketDetails)?.length &&
      isUpdate &&
      listUsers?.length &&
      dataSelects?.length
    ) {
      const {
        category,
        feature,
        followers,
        ownerId,
        priority,
        ticketType,
        title,
        referenceUrl,
        ...rests
      } = formatDatarender(ticketDetails, dataSelects, listUsers);
      updateValueInput({
        category,
        feature,
        followers,
        ownerId,
        priority,
        ticketType,
        title,
        referenceUrl,
        originTitle: title,
      });
    }
  }, [ticketDetails, listUsers, dataSelects]);

  useEffect(() => {
    setIsLoading(true);
    getCustomFields();

    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, []);

  const handleEditorChange = content => {
    setTextValue(content);
    updateValueInput({ message: content });
    // setIsLoadingUpload(content.includes('<img src="data:image/png;base64'));
  };

  const handleUpdateFollowers = arrFolowers => {
    const params = {
      followers: [],
    };

    params.followers = arrFolowers;

    fetchUpdateFollwers(params);
  };

  const handleUpdateComment = () => {
    setIsLoading(true);
    const params: any = {
      followers: [],
      submitterId: null,
      ownerId: null,
      message: '',
      properties: {
        attachments: [],
      },
      submitterEmail: '',
      attachmentsZendesk: [],
    };

    if (valueInput?.followers?.length) {
      params.followers = valueInput?.followers;
    }
    if (valueInput?.files?.length > 0) {
      params.properties.attachments = valueInput?.files;
    }
    const mapUsers = keyBy(listUsers, 'email');
    params.submitterId = +mapUsers[email]?.userId || 0;
    params.submitterEmail = email;
    params.ownerId = null;
    params.message = valueInput?.message;
    params.attachmentsZendesk = valueInput?.fileZendesk?.map(file => file?.token);

    fetchUpdateComment(params);
    handleUpdateFollowers(valueInput?.followers?.map((fol: any) => fol.userId));
  };

  const onChangeFollowers = arrFolowers => {
    updateValueInput({ followers: arrFolowers });
    handleUpdateFollowers(arrFolowers?.map((fol: any) => fol.userId));
  };

  const handleUploadFile = async file => {
    setIsLoading(true);
    const uploadService = Service.tickets.callApi.uploadFile({
      domain: domainUpload,
    });

    const uploadZendeskService = async listFiles => {
      const formData = new FormData();

      formData.append('file', listFiles);
      const params = {
        data: formData,
      };

      const responseZendesk = await Service.tickets.callApi.upload(
        params,
        domainTicket,
        token,
        config,
        userId,
        'ticket',
      );

      if (responseZendesk.code === 200) {
        updateValueInput({
          fileZendesk: [...valueInput?.fileZendesk, responseZendesk?.data],
        });
      }
    };

    try {
      const response = await uploadService({ files: [file], mode: 'file' });

      await uploadZendeskService(file);

      if (response.code === 200) {
        updateValueInput({
          files: [...valueInput?.files, response?.data[0]],
        });
      }

      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const timeOut = setTimeout(() => {
      if (isOpenToast.isOpen) {
        setIsOpenToast({ ...isOpenToast, isOpen: false });
      }
    }, 1000);
    return () => {
      clearTimeout(timeOut);
    };
  }, [isOpenToast]);

  const handleOnchangeFile = e => {
    const file = e.target?.files[0];
    if (!file) return;

    const sizeFile = file.size;
    const fileType = file.type;

    const isImage = fileType.startsWith('image/');

    const limitSize = isImage ? 10 * 1024 * 1024 : 50 * 1024 * 1024;
    if (sizeFile >= limitSize) {
      setErrFile({
        isError: true,
        message: `Maximum file size is reached (10MB for image & 50MB for other types).`,
      });
    } else {
      setErrFile({
        isError: false,
        message: '',
      });
      handleUploadFile(e.target.files[0]);
    }
    setFileInputKey(fileInputKey + 1);
  };

  const handleRemoveFile = token => {
    let newListFile = valueInput.files;
    newListFile = newListFile.filter((list: any) => list?.token !== token);
    updateValueInput({ files: newListFile });
  };

  const handleRemoveFileV2 = (index: number) => {
    let newListFile = valueInput.files;
    newListFile = newListFile.filter((_, fileIndex) => fileIndex !== index);

    const newFileZendesk = valueInput.fileZendesk.filter((_, fileIndex) => fileIndex !== index);

    updateValueInput({
      files: newListFile,
      fileZendesk: newFileZendesk,
    });

    setFileInputKey(prev => prev + 1);
  };

  const ownerEmail = useMemo(() => {
    const keyById = keyBy(listUsers, 'userId');

    if (keyById[ticketDetails?.ownerId]) {
      return keyById[ticketDetails?.ownerId]?.email;
    }

    return get(ticketDetails, 'submitterEmail', '');
  }, [ticketDetails?.ownerId, listUsers]);

  const renderOptions = info => {
    const { field_options: fieldOptions } = info;
    const dataSelectOptions = keyBy(fieldOptions, 'value');

    return (
      <WrapperContentInput key={info.id} style={{ marginTop: '15px' }}>
        <WrapperLable>{info.title}</WrapperLable>
        <div style={{ flex: 1 }}>
          <WrapperTextEdit>
            {!ticketDetails[info?.value]
              ? '--'
              : dataSelectOptions[ticketDetails[info?.value]]?.name}
          </WrapperTextEdit>
        </div>
      </WrapperContentInput>
    );
  };

  const callbackEditor = (type: TCallBack, value: string | boolean) => {
    switch (type) {
      case TCallBack.LOADING_UPLOAD:
        setIsLoadingUpload(value as boolean);
        break;
      case TCallBack.ERROR: {
        switch (value) {
          case 'FILE_TOO_LARGE':
            setErrFile({
              isError: true,
              message: '*Maximum upload file size: 10MB',
            });
            break;
          case 'RESET':
            setErrFile({
              isError: false,
              message: '',
            });
            break;
          default:
            break;
        }
        break;
      }
      default:
        break;
    }
  };

  return (
    <div style={{ height: '100%' }}>
      <Helmet>
        <meta charSet="utf-8" />
        <title>{browserTitle}</title>
      </Helmet>
      <Spin
        style={{ height: '100vh' }}
        spinning={isLoading || isLoadingDetails || isLoadingDataSouce || isLoadingFollower}
      >
        <WrapperContent style={{ height: '100%' }}>
          <WrapperLeftContent>
            <WrapperContentInput style={{ alignItems: 'flex-start' }}>
              <WrapperLable>
                Owner<span style={{ color: '#ff0000' }}>*</span>
              </WrapperLable>

              <div>{ownerEmail}</div>
            </WrapperContentInput>
            <WrapperContentInput style={{ marginTop: '15px', alignItems: 'flex-start' }}>
              <WrapperLable>Follower(s)</WrapperLable>
              <SelectAccount
                type="default"
                initData={ticketDetails?.followers || []}
                nameKey="userName"
                userIdKey="userId"
                users={listUsers}
                onChange={(_, followers) => onChangeFollowers(followers)}
              />
            </WrapperContentInput>
            {dataSelects?.map((data: any) => <>{renderOptions(data)}</>)}

            <WrapperContentInput style={{ marginTop: '15px' }}>
              <WrapperLable htmlFor="referenceUrl">Reference URL</WrapperLable>
              {isUpdate ? (
                ticketDetails.referenceUrl ? (
                  <Tooltip title={ticketDetails.referenceUrl} placement="top">
                    <WrapperTextEdit
                      color="#005fb8"
                      href={ticketDetails.referenceUrl}
                      target="_blank"
                    >
                      {ticketDetails.referenceUrl}
                    </WrapperTextEdit>
                  </Tooltip>
                ) : (
                  <WrapperTextEdit
                    color="#005fb8"
                    href={ticketDetails.referenceUrl}
                    target="_blank"
                  >
                    {ticketDetails.referenceUrl}
                  </WrapperTextEdit>
                )
              ) : (
                <WrapperTextInput
                  placeholder="Reference URL"
                  id="referenceUrl"
                  // width="300px"
                  onChange={handleOnchangeInput}
                  name="referenceUrl"
                  value={valueInput.referenceUrl}
                />
              )}
            </WrapperContentInput>
          </WrapperLeftContent>
          <WrapperRightContent>
            <WrapperEditor>
              <>
                <QuillEditor
                  value={textValue}
                  uploadService={Service.tickets.callApi.uploadFile({
                    domain: domainUpload,
                  })}
                  onChange={handleEditorChange}
                  placeholder="Enter your comment..."
                  height={195}
                  callback={callbackEditor}
                />
                <div>
                  {valueInput.files?.length > 0 && (
                    <WrapperLinkItemFiles>
                      {valueInput.files?.map((file: any, index) => (
                        <div className="file-item" key={file?.token}>
                          <div className="file-name-group">
                            <Icon className="file-icon" type="icon-ants-attachment" />
                            <Tooltip title={file?.file_name}>
                              <span className="file-name">{file?.file_name}</span>
                            </Tooltip>
                          </div>
                          <Icon
                            onClick={() => handleRemoveFileV2(index)}
                            className="remove-btn"
                            type="icon-ants-remove-slim"
                          />
                        </div>
                      ))}
                    </WrapperLinkItemFiles>
                  )}
                  <WrapperIconEditor borderTop={!!valueInput.files?.length}>
                    <WrapperInputFile>
                      <label htmlFor={`fileImage-${fileInputKey}`} className="upload-wrapper-label">
                        <Icon type="icon-ants-attachment" className="upload-icon" />
                      </label>
                      <input
                        key={fileInputKey}
                        type="file"
                        style={{ position: 'absolute', top: 0, right: 0, display: 'none' }}
                        name={`fileImage-${fileInputKey}`}
                        id={`fileImage-${fileInputKey}`}
                        onChange={handleOnchangeFile}
                      />
                    </WrapperInputFile>
                    {isUpdate && (
                      <Button
                        type="primary"
                        disabled={!handleValidateContent(textValue) || isLoadingUpload}
                        className="reply-btn"
                        style={{
                          background: `${
                            !handleValidateContent(textValue) || isLoadingUpload
                              ? '#ccc'
                              : '#1f5fac'
                          }`,
                        }}
                        onClick={handleUpdateComment}
                      >
                        Reply
                      </Button>
                    )}
                  </WrapperIconEditor>
                </div>
              </>
              {errFile.isError ? (
                <div className="error-message">{errFile.message}</div>
              ) : (
                isEmptyField &&
                !valueInput?.message && (
                  <div className="error-message">* This field can't be empty</div>
                )
              )}
            </WrapperEditor>

            {isUpdate && (
              <Spin spinning={isLoadingComment}>
                <WrapperMessageContent>
                  {listComment?.map(comment => (
                    <MessageComponent
                      key={comment?.id}
                      toUser={comment?.toUser}
                      fromUser={comment?.fromUser}
                      followers={comment?.followers}
                      // mailFollower={comment?.mailFollower}
                      message={comment?.message}
                      date={comment?.createdDate}
                      attachments={comment?.attachments}
                      timeZone={timeZone}
                      submitterEmail={comment?.submitterEmail}
                    />
                  ))}
                </WrapperMessageContent>
              </Spin>
            )}
          </WrapperRightContent>
        </WrapperContent>
      </Spin>
    </div>
  );
};

export default memo(Content);
