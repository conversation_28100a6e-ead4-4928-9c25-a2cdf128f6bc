import type React from 'react';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, Editor, MarkR<PERSON><PERSON> } from '@tiptap/core';
import type { Mark } from '@tiptap/pm/model';
import type { SmartTagAttrs } from './extensions/SmartTag';
import {
  CUSTOM_LINK_EXTENSION_NAME,
  ORDERED_LIST_STYLE_TYPE,
  UNORDERED_LIST_STYLE_TYPE,
} from './constants';
import type { OverrideProperties } from 'type-fest';
import { BubbleMenuPluginProps } from './extensions/BubbleMenu';

export type HandleSmartTagRef = {
  setSmartTag: (attrs: SmartTagAttrs) => void;
  deleteSmartTag: (id: string) => void;
  updateSmartTagAttrs: (id: string, updatedAttrs: Omit<SmartTagAttrs, 'id'>) => void;
};

export type HandleLinkRef = {
  setLink: (params: { attrs: LinkAttrs; content?: string }) => void;
  deleteLink: (predicate: (attrs: LinkAttrs) => boolean) => void;
  updateLinkAttrs: (
    predicate: (currentAttrs: LinkAttrs) => boolean,
    updateFn: (currentAttrs: LinkAttrs) => LinkAttrs | false,
  ) => void;
  updateLinkText: (params: {
    predicate: (attrs: LinkAttrs) => boolean;
    updated: (currentText: string) => string;
    mergeAdjacentEqualFn?: (currentAttrs: LinkAttrs, candidateAttrs: LinkAttrs) => boolean;
  }) => void;
};

export type TextEditorRef = HandleSmartTagRef &
  HandleLinkRef & {
    blur: (perserveSelection?: boolean) => void;
    style?: CSSStyleDeclaration;
    readonly editor: Editor | null;
  };

export type SmartTagHandler = Partial<{
  edit: (id: string, event: React.MouseEvent) => void;
  setNew: (args: { selectionText: string }) => void;
}>;

export type LinkHandler = Partial<{
  setNew: (args: { selectionText: string }) => void;
  edit: (args: { attrs: LinkAttrs; selectionText: string }) => void;
}>;

export type FontConfig = {
  fontFamily: { name: string; fallback?: string[] };
  fontWeight: number[];
  category?: string;
};

/**
 * Function type for grouping fonts into categories
 * @param font - The font configuration to categorize
 * @returns Object with group key and display label, or void to skip grouping
 */
export type FontGroupingFunction = (font: FontConfig) => { key: string; label: string } | void;

/**
 * Result of font grouping operation
 */
export type FontGroupResult = {
  label: string;
  fonts: FontConfig[];
};

/**
 * Configuration options for TextEditor components
 */
export type Config = Partial<{
  /** Font family configuration */
  FontFamily: {
    /** Array of available font configurations */
    fonts?: FontConfig[];
    /** Function to group fonts or false to disable grouping */
    fontGroupingFn?: FontGroupingFunction | false;
    /** Array of group keys to define display order. Groups not in this array will appear after ordered groups */
    groupOrder?: string[];
  };
  /** Unordered list configuration */
  UnorderedList: {
    /** Whether to use custom bullet styles */
    useCustomBullet?: boolean;
  };
}>;

export type TextStyle = {
  fontFamily: string;
  fontSize: string;
  color: string;
  backgroundColor: string;
  lineHeight: string;
  fontWeight: string;
  letterSpacing: string;
};

export type TextEditorComponentsRender = {
  renderLinkToolbar: (
    linkAttrs: LinkAttrs,
    defaultNode: React.ReactNode,
    defaultToolNodes: {
      gotoLink: React.ReactNode;
      copy: React.ReactNode;
      edit: React.ReactNode;
      unlink: React.ReactNode;
    },
  ) => React.ReactNode;
};

type Optional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>;

export type BubbleMenuProps = Optional<
  Omit<Optional<BubbleMenuPluginProps, 'pluginKey'>, 'element'>,
  'editor'
> &
  React.HTMLAttributes<HTMLDivElement>;

export type TextEditorProps = {
  id?: string;
  className?: string;
  style?: React.CSSProperties;
  initialContent?: string;
  config?: Config;
  smartTagHandler?: SmartTagHandler;
  linkHandler?: LinkHandler;
  editable?: boolean;
  bubbleMenuProps?: {
    container?: HTMLElement | null;
  } & Pick<BubbleMenuProps, 'options'>;
  defaultTextStyle?: Partial<TextStyle>;
  dataAttributes?: Record<`data-${string}`, unknown>;
  onUpdateDebounced?: number;
  render?: Partial<TextEditorComponentsRender>;
  onFocus?: (event: FocusEvent) => void;
  onUpdate?: (args: { html: string; text: string; json: JSONContent }) => void;
  onChangeFont?: (changeInfo: {
    font: FontConfig;
    weight: number;
    analysis: {
      isFullyWebSafe: boolean;
      isPrimaryFontWebSafe: boolean;
    };
  }) => void;
};

export type OrderedListStyleType =
  (typeof ORDERED_LIST_STYLE_TYPE)[keyof typeof ORDERED_LIST_STYLE_TYPE];

export type UnorderedListStyleType =
  (typeof UNORDERED_LIST_STYLE_TYPE)[keyof typeof UNORDERED_LIST_STYLE_TYPE];

export const isOrderedListStyleType = (value: unknown): value is OrderedListStyleType =>
  typeof value === 'string' && Object.values(ORDERED_LIST_STYLE_TYPE).some(v => v === value);

export const isUnorderedListStyleType = (value: unknown): value is UnorderedListStyleType =>
  typeof value === 'string' && Object.values(UNORDERED_LIST_STYLE_TYPE).some(v => v === value);

export type LinkAttrs = { href: string } & Partial<{
  target: string | null;
  rel: string | null;
  class: string | null;
  title: string | null;
  data: Record<string, string>;
}>;

export type LinkMark = OverrideProperties<
  Mark,
  {
    attrs: LinkAttrs;
  }
>;

export type LinkMarkRange = OverrideProperties<
  MarkRange,
  {
    mark: LinkMark;
  }
>;

export const isLinkMark = (mark: Mark): mark is LinkMark =>
  mark.type.name === CUSTOM_LINK_EXTENSION_NAME;

export const isLinkMarkRange = (markRange: MarkRange): markRange is LinkMarkRange =>
  isLinkMark(markRange.mark);
