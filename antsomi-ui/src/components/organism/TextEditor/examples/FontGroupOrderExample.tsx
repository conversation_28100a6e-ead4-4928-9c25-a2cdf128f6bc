import React, { useState } from 'react';
import { TextEditor } from '../TextEditor';
import { FontConfig, FontGroupingFunction } from '../types';

// Example font configurations
const EXAMPLE_FONTS: FontConfig[] = [
  // Web-safe fonts
  { fontFamily: { name: 'Arial', fallback: ['sans-serif'] }, fontWeight: [400, 700] },
  { fontFamily: { name: 'Helvetica', fallback: ['Arial', 'sans-serif'] }, fontWeight: [400, 700] },
  { fontFamily: { name: 'Times New Roman', fallback: ['serif'] }, fontWeight: [400, 700] },
  { fontFamily: { name: 'Georgia', fallback: ['serif'] }, fontWeight: [400, 700] },
  { fontFamily: { name: 'Courier New', fallback: ['monospace'] }, fontWeight: [400, 700] },
  
  // Google Fonts
  { fontFamily: { name: '<PERSON><PERSON>', fallback: ['Arial', 'sans-serif'] }, fontWeight: [300, 400, 500, 700] },
  { fontFamily: { name: 'Open Sans', fallback: ['Arial', 'sans-serif'] }, fontWeight: [400, 600, 700] },
  { fontFamily: { name: 'Lato', fallback: ['Arial', 'sans-serif'] }, fontWeight: [400, 700] },
  { fontFamily: { name: 'Montserrat', fallback: ['Arial', 'sans-serif'] }, fontWeight: [400, 600, 700] },
  
  // Custom fonts
  { fontFamily: { name: 'Custom Font A', fallback: ['Arial', 'sans-serif'] }, fontWeight: [400, 700] },
  { fontFamily: { name: 'Custom Font B', fallback: ['Arial', 'sans-serif'] }, fontWeight: [400, 700] },
];

// Custom grouping function
const customFontGrouping: FontGroupingFunction = (font) => {
  const webSafeFonts = ['Arial', 'Helvetica', 'Times New Roman', 'Georgia', 'Courier New'];
  const googleFonts = ['Roboto', 'Open Sans', 'Lato', 'Montserrat'];
  
  if (webSafeFonts.includes(font.fontFamily.name)) {
    return { key: 'web_safe', label: 'Web Safe Fonts' };
  }
  
  if (googleFonts.includes(font.fontFamily.name)) {
    return { key: 'google_fonts', label: 'Google Fonts' };
  }
  
  return { key: 'custom_fonts', label: 'Custom Fonts' };
};

/**
 * Example component demonstrating font group ordering functionality
 */
export const FontGroupOrderExample: React.FC = () => {
  const [content, setContent] = useState('<p>Select text to see font options with custom group ordering!</p>');

  return (
    <div style={{ padding: '20px' }}>
      <h2>Font Group Order Example</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Features demonstrated:</h3>
        <ul>
          <li>Custom font grouping function</li>
          <li>Controlled group display order using <code>groupOrder</code></li>
          <li>Groups appear in specified order: Google Fonts → Web Safe Fonts → Custom Fonts</li>
        </ul>
      </div>

      <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', padding: '16px' }}>
        <TextEditor
          initialContent={content}
          config={{
            FontFamily: {
              fonts: EXAMPLE_FONTS,
              fontGroupingFn: customFontGrouping,
              // Define the order of groups - groups not in this array will appear after
              groupOrder: ['google_fonts', 'web_safe', 'custom_fonts']
            }
          }}
          onUpdate={({ html }) => {
            setContent(html);
          }}
        />
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3>Configuration used:</h3>
        <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px', fontSize: '12px' }}>
{`config={{
  FontFamily: {
    fonts: EXAMPLE_FONTS,
    fontGroupingFn: customFontGrouping,
    groupOrder: ['google_fonts', 'web_safe', 'custom_fonts']
  }
}}`}
        </pre>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3>Without groupOrder (default behavior):</h3>
        <p>Groups would appear in the order they are processed, which may not be predictable.</p>
        
        <h3>With groupOrder:</h3>
        <p>Groups appear in the exact order specified: Google Fonts first, then Web Safe Fonts, then Custom Fonts.</p>
      </div>
    </div>
  );
};

export default FontGroupOrderExample;
