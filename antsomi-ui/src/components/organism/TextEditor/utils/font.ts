import { analyzeFontFamily, isWebSafeFont } from '@antscorp/antsomi-ui/es/utils';
import { FontConfig, FontGroupingFunction, FontGroupResult } from '../types';

/**
 * Gets the appropriate bold font weight for a given font family
 * @param currentFontFamily The font family to find bold weight for
 * @param fonts Array of available font configurations
 * @param minBoldWeight Minimum weight considered bold (default 700)
 * @returns The first available bold weight for the font family, or minBoldWeight if none found
 */
export const getBoldFontWeight = (
  currentFontFamily: string,
  fonts: FontConfig[],
  minBoldWeight = 700,
): number => {
  if (!currentFontFamily || !fonts?.length) return minBoldWeight;

  const fontConfig = fonts.find(font => font.fontFamily.name === currentFontFamily);
  return fontConfig?.fontWeight.find(weight => weight >= minBoldWeight) ?? minBoldWeight;
};

/**
 * Checks if a font weight is considered bold
 * @param weight Font weight to check
 * @param threshold Bold threshold (default 600)
 * @returns true if weight is bold
 */
export function isBoldWeight(weight: string | number, threshold = 600): boolean {
  const numericWeight = typeof weight === 'string' ? parseInt(weight, 10) : weight;
  return !isNaN(numericWeight) && numericWeight >= threshold;
}

/**
 * Gets all available font weights for a font family
 * @param fontFamily Font family name
 * @param fonts Array of font configurations
 * @returns Array of available weights
 */
export function getFontWeights(fontFamily: string, fonts: FontConfig[]): number[] {
  const fontConfig = fonts.find(font => font.fontFamily.name === fontFamily);
  return fontConfig?.fontWeight || [400];
}

/**
 * Finds the closest available font weight
 * @param targetWeight Desired font weight
 * @param availableWeights Array of available weights
 * @returns Closest available weight
 */
export function getClosestFontWeight(targetWeight: number, availableWeights: number[]): number {
  if (availableWeights.includes(targetWeight)) {
    return targetWeight;
  }

  return availableWeights.reduce((closest, weight) =>
    Math.abs(weight - targetWeight) < Math.abs(closest - targetWeight) ? weight : closest,
  );
}

/**
 * Validates font configuration
 * @param fontConfig Font configuration to validate
 * @returns true if configuration is valid
 */
export function isValidFontConfig(fontConfig: FontConfig): boolean {
  return !!(
    fontConfig.fontFamily?.name &&
    fontConfig.fontWeight &&
    Array.isArray(fontConfig.fontWeight) &&
    fontConfig.fontWeight.length > 0 &&
    fontConfig.fontWeight.every(weight => typeof weight === 'number' && weight > 0)
  );
}

/**
 * Sorts font configurations alphabetically by name
 * @param fonts Array of font configurations
 * @returns Sorted array of font configurations
 */
export function sortFontsByName(fonts: FontConfig[]): FontConfig[] {
  return [...fonts].sort((a, b) => a.fontFamily.name.localeCompare(b.fontFamily.name));
}

/**
 * Groups fonts by category (if category property exists)
 * @param fonts Array of font configurations
 * @returns Object with fonts grouped by category
 */
export function groupFontsByCategory(fonts: FontConfig[]): Record<string, FontConfig[]> {
  const grouped: Record<string, FontConfig[]> = {};

  fonts.forEach(font => {
    const category = font.category || 'Other';
    if (!grouped[category]) {
      grouped[category] = [];
    }
    grouped[category].push(font);
  });

  return grouped;
}

/**
 * Creates a font stack string from a font configuration
 * @param font Font configuration
 * @returns Font stack string
 */
export const getFontStack = (font: FontConfig) =>
  [font.fontFamily.name, ...(font.fontFamily.fallback || [])].join(', ');

/**
 * Analyzes a font configuration for web safety
 * @param font Font configuration
 * @returns Analysis results
 */
export const analyzeFont = (font: FontConfig) => {
  const fontStack = getFontStack(font);
  const { isFullyWebSafe, isPrimaryFontWebSafe } = analyzeFontFamily(fontStack);

  return { isFullyWebSafe, isPrimaryFontWebSafe };
};

/**
 * Groups fonts using a custom grouping function or default web-safe categorization
 * @param fonts Array of font configurations to group
 * @param fontGroupingFn Optional custom function to determine font groups
 * @param groupOrder Optional array of group keys to define display order
 * @returns Map with grouped fonts by category in specified order
 */
export const groupFonts = (
  fonts: FontConfig[],
  fontGroupingFn?: FontGroupingFunction,
  groupOrder?: string[],
): Map<string, FontGroupResult> => {
  const results = new Map<string, FontGroupResult>();

  const defaultGroupingFn: FontGroupingFunction = font => {
    if (isWebSafeFont(font.fontFamily.name)) {
      return { key: 'web_safe_fonts', label: 'Standard' };
    }

    return { key: 'custom_fonts', label: 'Non-standard' };
  };

  // First pass: collect all groups
  const tempGroups = new Map<string, FontGroupResult>();

  for (const font of fonts) {
    const groupInfo = (fontGroupingFn || defaultGroupingFn)(font);

    if (groupInfo) {
      const { key, label } = groupInfo;

      if (tempGroups.has(key)) {
        tempGroups.get(key)!.fonts.push(font);
      } else {
        tempGroups.set(key, { label, fonts: [font] });
      }
    }
  }

  // Second pass: order groups according to groupOrder
  if (groupOrder && groupOrder.length > 0) {
    // Add ordered groups first
    for (const groupKey of groupOrder) {
      if (tempGroups.has(groupKey)) {
        results.set(groupKey, tempGroups.get(groupKey)!);
        tempGroups.delete(groupKey);
      }
    }

    // Add remaining groups that weren't in groupOrder
    for (const [key, value] of tempGroups) {
      results.set(key, value);
    }
  } else {
    // No specific order, use insertion order from tempGroups
    for (const [key, value] of tempGroups) {
      results.set(key, value);
    }
  }

  return results;
};
