import { Extension } from '@tiptap/core';
import { DEFAULT_TEXT_STYLE } from '../constants';
import { convertPxToNumber } from '@antscorp/antsomi-ui/es/utils';

export interface LineHeightOptions {
  types: string[];
  defaultHeight: string;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    lineHeight: {
      /**
       * Set the line height attribute
       */
      setLineHeight: (height: string) => ReturnType;
      /**
       * Unset the text align attribute
       */
      unsetLineHeight: () => ReturnType;
    };
  }
}

function convertLineHeight(lineHeight: unknown, fontSize: number | null = 22): number {
  // Handle null/undefined cases
  if (lineHeight == null) {
    return 1.2; // Default line-height in most browsers
  }

  if (typeof lineHeight === 'number') {
    return lineHeight;
  }

  const value = String(lineHeight).trim();

  // Handle percentage values (e.g., "150%" -> 1.5)
  if (value.endsWith('%')) {
    const percentValue = parseFloat(value.slice(0, -1));
    if (isNaN(percentValue)) {
      throw new Error(`Invalid percentage value: ${lineHeight}`);
    }
    return percentValue / 100;
  }

  // Handle pixel values (e.g., "24px" -> 1.5 with 16px font)
  if (value.endsWith('px') && fontSize) {
    const pixelValue = parseFloat(value.slice(0, -2));

    if (isNaN(pixelValue)) {
      throw new Error(`Invalid pixel value: ${lineHeight}`);
    }

    if (fontSize <= 0) {
      throw new Error(`Invalid font size for px conversion: ${fontSize}`);
    }

    return pixelValue / fontSize;
  }

  // Handle em/rem values (e.g., "1.5em" -> 1.5)
  if (value.endsWith('em') || value.endsWith('rem')) {
    const numValue = parseFloat(value.slice(0, -2));

    if (isNaN(numValue)) {
      throw new Error(`Invalid em/rem value: ${lineHeight}`);
    }
    return numValue;
  }

  const numValue = parseFloat(value);

  if (isNaN(numValue)) {
    throw new Error(`Cannot convert line-height value "${lineHeight}" to number!`);
  }

  return numValue;
}

function canConvertLineHeight(lineHeight: unknown, fontSize = 16) {
  try {
    // Try to convert, if no error thrown then it's convertible
    convertLineHeight(lineHeight, fontSize);
    return true;
  } catch (error) {
    return false;
  }
}

export const LineHeight = Extension.create<LineHeightOptions>({
  name: 'lineHeight',

  addOptions() {
    return {
      types: ['textStyle'],
      defaultHeight: DEFAULT_TEXT_STYLE.lineHeight,
    };
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          lineHeight: {
            default: this.options.defaultHeight,
            parseHTML: element => {
              const pxFontSize = convertPxToNumber(element.style.fontSize);

              let { lineHeight } = element.style;

              if (element.parentElement instanceof HTMLParagraphElement) {
                const { lineHeight: parentParagraphLineHeight } = element.parentElement.style;

                if (canConvertLineHeight(parentParagraphLineHeight)) {
                  lineHeight = parentParagraphLineHeight;
                }
              }

              if (!canConvertLineHeight(lineHeight)) return null;

              return convertLineHeight(lineHeight, pxFontSize);
            },
            renderHTML: attrs => {
              if (!attrs.lineHeight || attrs.lineHeight === this.options.defaultHeight) {
                return {};
              }

              return { style: `line-height: ${attrs.lineHeight}` };
            },
          },
        },
      },
    ];
  },

  addCommands() {
    return {
      setLineHeight:
        lineHeight =>
        ({ chain }) =>
          chain().setMark('textStyle', { lineHeight }).run(),

      unsetLineHeight:
        () =>
        ({ chain }) =>
          chain().setMark('textStyle', { lineHeight: null }).removeEmptyTextStyle().run(),
    };
  },
});
