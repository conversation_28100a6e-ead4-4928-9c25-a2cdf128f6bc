import { CopyDuplicateIcon, EditIcon } from '@antscorp/antsomi-ui/es/components/icons';
import { Editor } from '@tiptap/core';
import { useEditorState } from '@tiptap/react';
import { Flex, Typography } from 'antd';
import React from 'react';
import { ToolbarWrapper } from '../../styled';
import { Config, FontConfig, LinkAttrs, TextEditorComponentsRender, TextStyle } from '../../types';
import { isShowLinkToolbar } from '../../utils';
import { TextEditorButton } from '../Button';
import {
  BoldAction,
  BulletListAction,
  ClearFormattingAction,
  EmoijiAction,
  FontFamilyAction,
  FontSizeAction,
  HistoryAction,
  IndentAction,
  ItalicAction,
  LetterSpacingAction,
  LinkAction,
  OrderedListAction,
  OutdentAction,
  SmartTagAction,
  LineSpacingAction,
  StrikeAction,
  SubscriptAction,
  SuperscriptAction,
  TextAlignAction,
  TextBackColorAction,
  TextColorAction,
  TextTransformAction,
  UnderlineAction,
  UnsetLinkAction,
} from './actions';
import { CUSTOM_LINK_EXTENSION_NAME, DEFAULT_TEXT_STYLE } from '../../constants';

export type ToolbarProps = {
  editor: Editor;
  config?: Config;
  defaultTextStyle?: TextStyle;
  smartTagHandler?: {
    onUpsert?: (event: React.MouseEvent) => void;
  };
  linkHanlder?: {
    onUpsert?: () => void;
  };
  render?: Partial<Pick<TextEditorComponentsRender, 'renderLinkToolbar'>>;
  onChangeFont?: (args: { font: FontConfig; weight: number }) => void;
};

export const Toolbar = (props: ToolbarProps) => {
  const {
    editor,
    smartTagHandler,
    linkHanlder,
    config,
    defaultTextStyle = DEFAULT_TEXT_STYLE,
    render,
    onChangeFont,
  } = props;

  const { showLinkMenu, linkAttrs } = useEditorState({
    editor,
    selector: ctx => ({
      showLinkMenu: isShowLinkToolbar(ctx.editor.state),
      linkAttrs: editor.getAttributes(CUSTOM_LINK_EXTENSION_NAME) as LinkAttrs,
    }),
  });

  if (showLinkMenu) {
    const href = String(linkAttrs?.href);

    const goToLinkEl = (
      <Typography.Link style={{ maxWidth: 150 }} ellipsis href={href}>
        {href}
      </Typography.Link>
    );

    const copyLinkEl = (
      <TextEditorButton
        icon={<CopyDuplicateIcon size={18} />}
        tooltipProps={{ title: 'Copy' }}
        onClick={() => {
          navigator.clipboard.writeText(href);
        }}
      />
    );

    const editLinkEl = (
      <TextEditorButton
        icon={<EditIcon size={18} />}
        tooltipProps={{ title: 'Edit' }}
        onClick={() => linkHanlder?.onUpsert?.()}
      />
    );

    const unsetLinkEl = <UnsetLinkAction editor={editor} />;

    let linkTools: React.ReactNode = (
      <>
        {goToLinkEl}
        {copyLinkEl}
        {editLinkEl}
        {unsetLinkEl}
      </>
    );

    if (render?.renderLinkToolbar && linkAttrs) {
      linkTools = render.renderLinkToolbar(linkAttrs, linkTools, {
        copy: copyLinkEl,
        edit: editLinkEl,
        gotoLink: goToLinkEl,
        unlink: unsetLinkEl,
      });
    }

    return (
      <ToolbarWrapper wrap="wrap" gap={8} align="center">
        {linkTools}
      </ToolbarWrapper>
    );
  }

  return (
    <ToolbarWrapper vertical gap={20}>
      <Flex gap={15} justify="flex-start" align="center">
        <FontFamilyAction
          editor={editor}
          fonts={config?.FontFamily?.fonts}
          onChange={(font, weight) => onChangeFont?.({ font, weight })}
          fontGroupingFn={config?.FontFamily?.fontGroupingFn}
          groupOrder={config?.FontFamily?.groupOrder}
        />
        <FontSizeAction editor={editor} defaultFontSize={defaultTextStyle.fontSize} />
        <BoldAction editor={editor} />
        <ItalicAction editor={editor} />
        <UnderlineAction editor={editor} />
        <LinkAction editor={editor} onClick={linkHanlder?.onUpsert} />

        {/* <Divider type="vertical" className="toolbar-separator" /> */}

        <StrikeAction editor={editor} />
        <SuperscriptAction editor={editor} />
        <SubscriptAction editor={editor} />

        {/* <Divider type="vertical" className="toolbar-separator" /> */}

        <TextTransformAction editor={editor} />
        <TextColorAction editor={editor} />
        <TextBackColorAction editor={editor} />
        <EmoijiAction editor={editor} />
        <SmartTagAction editor={editor} onClick={smartTagHandler?.onUpsert} />
      </Flex>

      <Flex gap={15} justify="flex-start" align="center">
        <BulletListAction
          editor={editor}
          useCustomBullet={config?.UnorderedList?.useCustomBullet}
        />
        <OrderedListAction editor={editor} />
        <TextAlignAction editor={editor} />
        <LineSpacingAction editor={editor} defaultValue={defaultTextStyle?.lineHeight} />
        <LetterSpacingAction editor={editor} />
        <IndentAction editor={editor} />
        <OutdentAction editor={editor} />

        {/* <Divider type="vertical" className="toolbar-separator" /> */}

        <HistoryAction editor={editor} />
        <ClearFormattingAction editor={editor} />
      </Flex>
    </ToolbarWrapper>
  );
};
