import { Editor } from '@tiptap/core';
import { DEFAULT_TEXT_STYLE, TEXT_EDITOR_CONSTANTS } from '../../../constants';
import { DropdownButton } from '../../DropdownButton';
import { LineHeight } from '../../../extensions/LineHeight';
import { isEmpty, range, round } from 'lodash';
import { Tooltip } from 'antd';
import { useEditorState } from '@tiptap/react';

const { TOOLTIPS } = TEXT_EDITOR_CONSTANTS;

interface SpacingActionProps {
  editor: Editor;
  defaultValue?: string;
}

export const LineSpacingIcon = (props: React.SVGProps<SVGSVGElement> & { size?: number }) => {
  const { size = 24, ...restProps } = props;

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...restProps}
    >
      <path
        d="M21 10H13M21 6H13M21 14H13M21 18H13M6 20L6 4M6 20L3 17M6 20L9 17M6 4L3 7M6 4L9 7"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

const OPTIONS: { value: string; label: string }[] = [
  { value: '1', label: 'Single' },
  { value: '2', label: 'Double' },

  ...range(1.1, 3.1, 0.1).map(spacing => {
    const roundValue = round(spacing, 1);

    return {
      value: `${roundValue}`,
      label: `${roundValue}`,
    };
  }),
] as const;

export const LineSpacingAction = (props: SpacingActionProps) => {
  const { editor, defaultValue = DEFAULT_TEXT_STYLE.lineHeight } = props;

  const { selectedLineHeight } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => ({
      selectedLineHeight: OPTIONS.find(opt => {
        const { value } = opt;

        return +editorInstance.getAttributes('textStyle').lineHeight === +value;
      }),
    }),
  });

  return (
    <DropdownButton
      value={selectedLineHeight?.value}
      options={[
        {
          label: (
            <Tooltip mouseEnterDelay={0.4} title={defaultValue} arrow={false}>
              Default
            </Tooltip>
          ),
          value: defaultValue,
        },
        ...OPTIONS,
      ]}
      onClickOption={value => {
        editor.chain().focus().setLineHeight(value).run();
      }}
      tooltipProps={{
        title: TOOLTIPS.LINE_SPACING.TITLE,
      }}
    >
      <LineSpacingIcon size={18} />
    </DropdownButton>
  );
};
