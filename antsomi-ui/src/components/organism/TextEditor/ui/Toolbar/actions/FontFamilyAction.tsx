import { Editor } from '@tiptap/core';
import { DEFAULT_FONT_CONFIGS } from '../../../constants';
import { FontConfig, FontGroupingFunction } from '../../../types';
import { FontPopover } from '../../FontPopover/FontPopover';
import { getPrimaryFontFamily } from '@antscorp/antsomi-ui/es/utils';
import { useEditorState } from '@tiptap/react';
import { useCallback, useEffect, useRef } from 'react';

/**
 * Props for FontFamilyAction component
 */
export interface FontFamilyActionProps {
  /** TipTap editor instance */
  editor: Editor;
  /** Array of available font configurations */
  fonts?: FontConfig[];
  /** Function to group fonts or false to disable grouping */
  fontGroupingFn?: FontGroupingFunction | false;
  /** Array of group keys to define display order. Groups not in this array will appear after ordered groups */
  groupOrder?: string[];
  /** Callback when font selection changes */
  onChange?: (font: FontConfig, weight: number) => void;
}

export const FontFamilyAction = (props: FontFamilyActionProps) => {
  const { editor, fonts = DEFAULT_FONT_CONFIGS, onChange, fontGroupingFn, groupOrder } = props;

  const onChangeRef = useRef<typeof onChange>();

  useEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);

  const { fontValue, fontWeight } = useEditorState({
    editor,
    selector: ({ editor: editorInstance }: { editor: Editor }) => {
      const { fontWeight, fontFamily } = editorInstance.getAttributes('textStyle') || {};

      return {
        fontValue: getPrimaryFontFamily(fontFamily),
        fontWeight: Number(fontWeight),
      };
    },
  });

  const activeFont = fonts.find(font => font.fontFamily.name === fontValue);

  const handleChangeFont = useCallback(
    (font: FontConfig, weight: number) => {
      const done = editor
        .chain()
        .focus()
        .setFontFamily([font.fontFamily.name, ...(font.fontFamily.fallback || [])].join(', '))
        .setFontWeight(weight)
        .run();

      if (done) {
        onChangeRef.current?.(font, weight);
      }
    },
    [editor],
  );

  return (
    <FontPopover
      fonts={fonts}
      value={
        activeFont
          ? {
              font: activeFont,
              weight: fontWeight,
            }
          : undefined
      }
      onChange={handleChangeFont}
      fontGrouping={fontGroupingFn}
      groupOrder={groupOrder}
    />
  );
};
