import { useCurrentEditor } from '@tiptap/react';
import { forwardRef, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { BubbleMenuPlugin } from '../../extensions/BubbleMenu';
import { useDeepCompareEffect, useIsMounted } from '@antscorp/antsomi-ui/es/hooks';
import { useTextEditorStore } from '../../provider';
import clsx from 'clsx';
import { ANTSOMI_COMPONENT_PREFIX_CLS } from '@antscorp/antsomi-ui/es/constants';
import { BubbleMenuProps } from '../../types';

export const BubbleMenu = forwardRef<HTMLDivElement, BubbleMenuProps>(
  (
    {
      pluginKey = 'bubbleMenu',
      editor,
      updateDelay,
      resizeDelay,
      shouldShow = null,
      options,
      children,
      container,
      className,
      ...restProps
    },
    ref,
  ) => {
    const menuEl = useRef(document.createElement('div'));

    const isMounted = useIsMounted();

    const setIsShowBubbleMenu = useTextEditorStore(state => state.setIsShowBubbleMenu);

    const { editor: currentEditor } = useCurrentEditor();

    useEffect(() => {
      if (typeof ref === 'function') {
        ref(menuEl.current);
      } else if (ref) {
        ref.current = menuEl.current;
      }
    }, [ref]);

    useDeepCompareEffect(() => {
      const bubbleMenuElement = menuEl.current;

      bubbleMenuElement.className = clsx(
        className,
        `${ANTSOMI_COMPONENT_PREFIX_CLS}-text-editor-bubble-menu`,
      );

      if (editor?.isDestroyed || currentEditor?.isDestroyed) {
        return;
      }

      const attachToEditor = editor || currentEditor;

      if (!attachToEditor) {
        // eslint-disable-next-line no-console
        console.warn(
          'BubbleMenu component is not rendered inside of an editor component or does not have editor prop.',
        );
        return;
      }

      const plugin = BubbleMenuPlugin({
        updateDelay,
        resizeDelay,
        editor: attachToEditor,
        element: bubbleMenuElement,
        container,
        pluginKey,
        options: {
          ...options,
          strategy: options?.strategy || 'absolute',
          placement: options?.placement || 'bottom',
          offset: options?.offset,
          onShow: () => {
            if (isMounted()) {
              setIsShowBubbleMenu(true);
            }

            options?.onShow?.();
          },
          onHide: () => {
            if (isMounted()) {
              setIsShowBubbleMenu(false);
            }

            options?.onHide?.();
          },
        },
        shouldShow,
      });

      attachToEditor.registerPlugin(plugin);

      return () => {
        attachToEditor.unregisterPlugin(pluginKey);

        window.requestAnimationFrame(() => {
          if (bubbleMenuElement.parentNode) {
            bubbleMenuElement.parentNode.removeChild(bubbleMenuElement);
          }
        });
      };
    }, [
      editor,
      container,
      updateDelay,
      resizeDelay,
      pluginKey,
      options,
      className,
      currentEditor,
      isMounted,
      shouldShow,
      setIsShowBubbleMenu,
    ]);

    return createPortal(<div {...restProps}>{children}</div>, menuEl.current);
  },
);
