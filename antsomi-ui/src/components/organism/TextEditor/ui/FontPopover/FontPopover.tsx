import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { Typography } from 'antd';
import { SearchPopover, VirtualizedMenu } from '@antscorp/antsomi-ui/es/components/molecules';
import { FontConfig, FontGroupingFunction } from '../../types';
import { FontGroupTitle, PopoverTrigger } from './styled';
import { calInlineListSize } from '@antscorp/antsomi-ui/es/components/molecules/VirtualizedMenu/utils';
import { useTextEditorStore } from '../../provider';
import { groupFonts, sortFontsByName } from '../../utils';
import { searchStringQuery } from '@antscorp/antsomi-ui/es/utils';
import { FontItem } from './FontItem';
import { FontPopoverProps } from './types';
import { useDebounce } from 'use-debounce';

/**
 * Converts font configurations to menu items with optional grouping
 * @param fonts Array of font configurations
 * @param renderLabel Function to render font label
 * @param fontGroupingFn Optional function to group fonts, or false to disable grouping
 * @param groupOrder Optional array of group keys to define display order
 * @returns Array of menu items for font selection
 */
export const createFontMenuItems = (
  fonts: FontConfig[],
  renderLabel: (font: FontConfig) => React.ReactNode,
  fontGroupingFn?: FontGroupingFunction | false,
  groupOrder?: string[],
): { key: string; label: React.ReactNode }[] => {
  const items: { key: string; label: React.ReactNode; displayOnly?: boolean }[] = [];

  const mapFontToMenuItem = (font: FontConfig) => ({
    key: font.fontFamily.name,
    label: renderLabel(font),
  });

  const appendFontGroup = (groupKey: string, groupLabel: string, groupFonts: FontConfig[]) => {
    if (groupFonts.length <= 0) return;

    items.push(
      {
        key: groupKey,
        displayOnly: true,
        label: <FontGroupTitle className="font-popover-item">{groupLabel}</FontGroupTitle>,
      },
      ...groupFonts.map(mapFontToMenuItem),
    );
  };

  if (fontGroupingFn !== false) {
    const groupedFonts = groupFonts(fonts, fontGroupingFn, groupOrder);

    // Use Map.forEach to maintain insertion order
    groupedFonts.forEach(({ label, fonts: groupFonts }, key) => {
      appendFontGroup(key, label, groupFonts);
    });
  } else {
    items.push(...fonts.map(mapFontToMenuItem));
  }

  return items;
};

export const FontPopover = memo((props: FontPopoverProps) => {
  const { fonts = [], value: fontValue, onChange, fontGrouping, groupOrder } = props;

  const itemSize = 30;
  const itemSpacing = 4;

  const activeFontName = fontValue?.font.fontFamily.name;

  const [search, setSearch] = useState('');
  const [open, setOpen] = useState(false);
  const [fontItemsSize, setFontItemSize] = useDebounce(0, 200);

  const bubbleMenuContainer = useTextEditorStore(state => state.bubbleMenuContainer);

  const filteredFonts = useMemo(() => {
    let results = fonts.filter(font => searchStringQuery(font.fontFamily.name, search));

    results = sortFontsByName(results);

    return results;
  }, [fonts, search]);

  useEffect(() => {
    const listSize = calInlineListSize(Math.min(filteredFonts.length, 6), itemSize, itemSpacing);

    setFontItemSize(listSize);
  }, [filteredFonts.length, setFontItemSize]);

  const handleClose = useCallback(() => {
    setOpen(false);
    setSearch('');
  }, []);

  const handleChangeFont = useCallback(
    (font: FontConfig, weight: number = 400) => {
      onChange?.(font, weight);
      handleClose();
    },
    [onChange, handleClose],
  );

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);

    if (newOpen === false) {
      handleClose();
    }
  };

  const items = useMemo(() => {
    const renderLabel = (fontConfig: FontConfig) => (
      <FontItem
        fontConfig={fontConfig}
        active={fontValue ? { font: fontValue.font, weight: fontValue.weight } : undefined}
        onSelectFont={weight => handleChangeFont(fontConfig, weight)}
      />
    );

    return createFontMenuItems(filteredFonts, renderLabel, fontGrouping, groupOrder);
  }, [filteredFonts, fontGrouping, groupOrder, fontValue, handleChangeFont]);

  return (
    <SearchPopover
      open={open}
      placement="bottomLeft"
      onOpenChange={handleOpenChange}
      getPopupContainer={() => bubbleMenuContainer || document.body}
      inputSearchProps={{
        value: search,
        onAfterChange: setSearch,
      }}
      overlayClassName="font-popover-overlay"
      content={
        <div
          className="list-font-root"
          style={{
            height: `${fontItemsSize}px`,
          }}
        >
          <VirtualizedMenu
            itemSize={itemSize}
            inlinePadding={0}
            itemSpacing={itemSpacing}
            selectable={false}
            items={items}
          />
        </div>
      }
    >
      <PopoverTrigger className="font-popover-trigger">
        <Typography.Text ellipsis={{ tooltip: true }}>{activeFontName}</Typography.Text>
      </PopoverTrigger>
    </SearchPopover>
  );
});
