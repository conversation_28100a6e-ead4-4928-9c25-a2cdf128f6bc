import { FontConfig, FontGroupingFunction } from '../../types';

/**
 * Props for FontPopover component
 */
export type FontPopoverProps = {
  /** Array of available font configurations */
  fonts?: FontConfig[];
  /** Currently selected font and weight */
  value?: { font: FontConfig; weight: number };
  /** Callback when font selection changes */
  onChange?: (font: FontConfig, weight: number) => void;
  /** Function to group fonts or false to disable grouping */
  fontGrouping?: FontGroupingFunction | false;
  /** Array of group keys to define display order. Groups not in this array will appear after ordered groups */
  groupOrder?: string[];
};

/**
 * Props for FontItem component
 */
export type FontItemProps = {
  /** Font configuration to display */
  fontConfig: FontConfig;
  /** Currently active font and weight */
  active?: { font: FontConfig; weight: number };
  /** Callback when font weight is selected */
  onSelectFont?: (fontweight: number) => void;
  /** Callback when popover open state changes */
  onOpenChange?: (open: boolean) => void;
};
