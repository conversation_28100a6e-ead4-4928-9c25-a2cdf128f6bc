/* eslint-disable react/no-this-in-sfc */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import axios from 'axios';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CookiesProvider, useCookies } from 'react-cookie';
import { useInView } from 'react-intersection-observer';

// Icons
import { Text } from '@antscorp/antsomi-ui/es/components/atoms/Text';
import { Typography } from '@antscorp/antsomi-ui/es/components/atoms/Typography';
import { Button } from '@antscorp/antsomi-ui/es/components/atoms/Button';
import { Flex } from '@antscorp/antsomi-ui/es/components/atoms/Flex';
import { Popover } from '@antscorp/antsomi-ui/es/components/atoms/Popover';
import { Spin } from '@antscorp/antsomi-ui/es/components/atoms/Spin';

import { AccountProfile } from '../AccountProfile';
import CloseIcon from './icons/close';
import SearchIcon from './icons/search';
import {
  InnerCardStyled,
  InputSearchStyled,
  PackageSharingStyled,
  TriggerPopoverStyled,
} from './styled';

// Utils
import {
  formatUserId,
  getObjectPropSafely,
  removeAppCookieSessionSubdomain,
  removeAppCookieSessionSubdomainPrefix,
  removeCookieSubDomain,
  setCookieSession,
} from '@antscorp/antsomi-ui/es/utils';
import { translate } from './utils';
import Icon from '@antscorp/icons';
import { useDebounce } from '@antscorp/antsomi-ui/es/hooks/useDebounceV2';
import { DEFAULT_TRANSLATE_DATA } from './constant';
import { CDP_ROUTE, THEME, UOGS_PREFIX } from '@antscorp/antsomi-ui/es/constants';
import { useAppConfigContext } from '@antscorp/antsomi-ui/es/providers/AppConfigProvider/selector';

// Constants
import { ENV } from '@antscorp/antsomi-ui/es/config';

// const STEP = {
//   SWITCH_ACCOUNT: 'SWITCH_ACCOUNT',
//   SELECT_NETWORK: 'SELECT_NETWORK',
// };

const InputSearch = props => {
  const { value, onChange, className, onClickRemove } = props;

  const inputRef = useRef<HTMLInputElement>(null);
  const { ref: wrapperRef, inView: componentInView } = useInView();

  useEffect(() => {
    if (componentInView) {
      inputRef.current?.focus();
    }
  }, [componentInView]);

  return (
    <InputSearchStyled className={className || ''} ref={wrapperRef}>
      <input
        className="input-search-account"
        placeholder="Search..."
        value={value}
        onChange={e => onChange(e.target.value)}
        ref={inputRef}
      />
      {value ? (
        <CloseIcon className="remove-icon" onClick={onClickRemove} />
      ) : (
        <SearchIcon className="search-icon" />
      )}
    </InputSearchStyled>
  );
};

export interface AccountSharingProps {
  permissionDomain: string;
  iamDomain: string;
  token: string;
  accountId: number;
  networkId: number;
  appCode: string;
  lang: string;
  translateData?: any;
  urlEditProfile: string;
  callbackGetInfoAccount?: (args: any) => void;
  urlLogout: string;
  isShowSharing?: boolean;
  isShareAccountAccess?: boolean;
  u_ogs: string;
  callbackLogout?: () => void;
  usePrivilege?: boolean;
}

export const AccountSharing: React.FC<AccountSharingProps> = props => {
  const {
    permissionDomain = '',
    iamDomain = '',
    token = '',
    accountId = 0,
    networkId = 0,
    appCode = '',
    lang = 'en',
    translateData = DEFAULT_TRANSLATE_DATA,
    urlEditProfile = '',
    callbackGetInfoAccount,
    urlLogout = '',
    isShowSharing = false,
    isShareAccountAccess = false,
    u_ogs = '',
    callbackLogout,
    usePrivilege = true,
  } = props;

  const { appConfig } = useAppConfigContext();
  const { env } = appConfig;

  const selectedNetworkRef = useRef<any>();

  // const [isShowSearchAccount, setIsShowSearchAccount] = useState(false);
  const [infoAccount, setInfoAccount] = useState<any>({});
  const [listAccountSharing, setListAccountSharing] = useState([]);
  const [listAccountRecent, setListAccountRecent] = useState([]);
  const [limitAccount, setLimitAccount] = useState(10);
  const [isShowLoading, setIsShowLoading] = useState(false);
  const [accountSearch, setAccountSearch] = useState('');
  const [portalSearch, setPortalSearch] = useState('');
  const [openAccountProfile, setOpenAccountProfile] = useState(false);
  const [open, setOpen] = useState(false);

  const [tab, setTab] = useState<'account' | 'network'>('account');

  const [listNetworks, setListNetworks] = useState([]);
  const [currentNetwork, setCurrentNetwork] = useState<any>();
  const [serverTranslateData, setServerTranslateData] = useState({});
  const [language, setLanguage] = useState(lang || 'en');
  const debounceSearch = useDebounce(accountSearch, 500);

  const [, setCookie] = useCookies();

  const domain = iamDomain.charAt(iamDomain.length - 1) === '/' ? iamDomain : `${iamDomain}/`;
  const formatDomain = iamDomain.endsWith('/') ? iamDomain.slice(0, -1) : iamDomain;

  const translateLang = useMemo(
    () => translate(translateData, serverTranslateData, language),
    [serverTranslateData, language, translateData],
  );

  const getInfoAccount = () => {
    const params = {
      type: 'user-info',
      userId: +accountId,
      networkId: +networkId,
    };

    return axios.get(`${domain}api/3rd/info`, { params });
  };

  const getTranslateData = () => {
    if ((translateData && Object.keys(translateData).length > 0) || !permissionDomain) {
      return true;
    }
    const response = axios.get(`${permissionDomain}/api/language/info`, {
      params: {
        type: 'get-language-by-code',
        locale: language,
      },
    });
    if (response) {
      response.then(res => {
        if (res && res.data && res.data.data && res.data.data.languages) {
          setServerTranslateData(res.data.data.languages);
        }
      });
    }
    return response;
  };

  const handleInfoAccount = () => {
    getInfoAccount().then(res => {
      if (res?.data?.data) {
        setInfoAccount(res.data.data);
      }
    });
  };

  const getListNetworkSelect = () => {
    if (!token) return;

    fetch(`${domain}api/network/info?type=get-list-network&token=${token}`)
      .then(response => response.json())
      .then(res => {
        if (res?.data?.length) {
          const convertedNetworks = res.data.map(network => ({
            networkId: network.network_id,
            networkName: network.network_name,
            homePage: network.home_page,
            logo: network.logo,
          }));
          setListNetworks(convertedNetworks);

          const curNetwork = convertedNetworks.find(item => +item.networkId === +networkId);

          if (curNetwork) {
            setCurrentNetwork(curNetwork);
          }
        }
      });
  };

  const getMenuUrl = (menu, userId = '') => {
    if (!menu || !menu.menu_path) {
      return '';
    }
    const { app_domain: appDomain, app_path: appPath, menu_path: menuPath } = menu;

    return (appDomain + appPath + menuPath).replaceAll(':user_id', userId);
  };

  const redirectFirstMenu = (data, redirectAccountId) => {
    if (!Array.isArray(data)) {
      return false;
    }

    const arrMenus =
      getObjectPropSafely(
        () => [...data].sort((first, second) => first.app_order - second.app_order)[0].childs,
      ) || [];

    if (arrMenus && arrMenus.length) {
      const firstMenu = arrMenus
        .filter(menu => +menu.show_hide)
        .sort((first, second) => first.menu_order - second.menu_order)[0];

      if (firstMenu) {
        const redirectUrl = getMenuUrl(firstMenu, redirectAccountId);

        window.location.href = redirectUrl;
      }
    }
  };

  const buildListAppMenus = (data, userId) => {
    if (!Array.isArray(data) || !data.length) {
      return [];
    }

    let arrMenus: string[] = [];

    data.forEach(item => {
      if (item.menu_id) {
        arrMenus.push(getMenuUrl(item, userId));
      }

      if (item.childs && item.childs.length) {
        arrMenus = arrMenus.concat(buildListAppMenus(item.childs, userId));
      }
    });

    return arrMenus;
  };

  const checkRedirectHomePage = (homePage, data, userId, newNetworkId) => {
    const convertedHomePage = String(homePage).replace(/:user_id/g, userId);
    if (!Array.isArray(data) || !data.length) {
      window.location.href = convertedHomePage;
      return;
    }
    const arrMenus = buildListAppMenus(data, userId);

    if (!arrMenus.some(url => convertedHomePage.includes(url))) {
      // Redirect to recommendation
      if (env !== ENV.DEV) {
        window.location.assign(
          `${CDP_ROUTE[env || 'development']}/gen2/${newNetworkId}/dashboard/recommendation`,
        );
        return;
      }
      // return redirectFirstMenu(data, userId);
    }

    window.location.href = convertedHomePage;
  };

  const getAppsMenus = ({ token, userId, accountId, homePage, newNetworkId }) => {
    if (permissionDomain && token && userId && accountId) {
      const apiUrl =
        permissionDomain + (usePrivilege ? '/api/privilege/index' : '/api/permission/index');

      return axios({
        url: apiUrl,
        params: {
          type: 'list-app',
          _token: token,
          _user_id: userId,
          _account_id: accountId,
          _lang: 'en',
          hasChild: true,
          from: 'login',
        },
      }).then(res => {
        if (res && res.data) {
          checkRedirectHomePage(homePage, res.data.data, userId, newNetworkId);
        }
      });
    }

    return null;
  };

  const getListAccountSharing = () => {
    if (isShareAccountAccess) {
      const params = {
        type: 'get-list-access',
        _token: token,
        _user_id: +accountId,
        _account_id: +accountId,
        app_code: appCode,
        _lang: lang,
        limit: 1000,
        search: accountSearch,
      };
      setIsShowLoading(true);
      return axios.get(`${permissionDomain}/api/account/share-access`, {
        params,
      });
    }
    const params = {
      type: 'get-list-access-by-app-code',
      _token: token,
      _user_id: +accountId,
      _account_id: +accountId,
      app_code: appCode,
      _lang: lang,
      limit: 1000,
      search: accountSearch,
    };
    setIsShowLoading(true);
    return axios.get(`${permissionDomain}/api/account/share`, {
      params,
    });
  };

  useEffect(() => {
    if (!iamDomain || !accountId || !networkId) return;

    getTranslateData();
    handleInfoAccount();
    getListNetworkSelect();

    const handleChangeUserLanguage = (e: MessageEvent<any>) => {
      if (e?.data?.action === 'change-user-language') {
        if (e?.data?.language) setLanguage(e.data.language);
        if (e?.data?.translateData) setServerTranslateData(e.data.translateData);
      }
    };

    window.addEventListener('message', handleChangeUserLanguage);

    return () => {
      window.removeEventListener('message', handleChangeUserLanguage);
    };
  }, []);

  useEffect(() => {
    if (!iamDomain || !accountId || !networkId) return;
    handleInfoAccount();
  }, [accountId]);

  useEffect(() => {
    if (lang && lang !== language) {
      setLanguage(lang);
    }
  }, [lang]);

  useEffect(() => {
    if (!permissionDomain || !token || !accountId || !appCode) {
      return;
    }
    setIsShowLoading(true);
    getListAccountSharing().then(res => {
      setIsShowLoading(false);
      if (res?.data?.data) {
        setListAccountSharing(res?.data?.data?.list_access || []);
        setListAccountRecent(res?.data?.data?.recent_access || []);
      }
    });
  }, [debounceSearch]);

  useEffect(() => {
    if (limitAccount > 10) {
      getListAccountSharing().then(res => {
        setIsShowLoading(false);
        if (res?.data?.data) {
          setListAccountSharing(res?.data?.data?.list_access || []);
          setListAccountRecent(res?.data?.data?.recent_access || []);
        }
      });
    }
  }, [limitAccount]);

  const filterListNetwork = useMemo(
    () =>
      listNetworks.filter(
        (netWork: any) =>
          netWork.networkName.toLocaleLowerCase().indexOf(portalSearch.toLocaleLowerCase()) !==
            -1 ||
          String(netWork.networkId)
            .toLocaleLowerCase()
            .indexOf(portalSearch.toLocaleLowerCase()) !== -1,
      ),
    [listNetworks, portalSearch],
  );

  const logAccountSwitch = async switchAccountId => {
    // setIsShowLoading(true)
    if (switchAccountId && isShareAccountAccess) {
      const params = {
        type: 'update-recent-shared-access',
        _token: token,
        _user_id: +accountId,
        _account_id: +accountId,
        access_user_id: switchAccountId,
      };

      await axios.get(`${permissionDomain}/api/account/share-access`, {
        params,
      });
      // await delay(2000)
      // setIsShowLoading(false)
      // .then((res) => console.log('LOG DONE', res))
    }
  };

  // const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

  const onClickButtonLogout = useCallback(() => {
    // Handle remove cookie and redirect to login
    removeAppCookieSessionSubdomainPrefix({ name: UOGS_PREFIX, env });
    removeAppCookieSessionSubdomain({ name: 'c_pid', env });
    removeAppCookieSessionSubdomain({ name: 'api_token', env });
    removeAppCookieSessionSubdomain({ name: 'api_r_token', env });
    removeAppCookieSessionSubdomain({ name: 'api_pid', env });
    removeAppCookieSessionSubdomain({ name: 'user_id', env });
    removeAppCookieSessionSubdomain({ name: 'user_logged_in_email', env });
    removeAppCookieSessionSubdomain({ name: 'user_logged_in_full_name', env });
    removeAppCookieSessionSubdomain({ name: 'user_logged_in_avatar', env });
    removeAppCookieSessionSubdomain({ name: '_fe_ogs_pid', env });

    localStorage.removeItem('cdp_decrypt_permission');

    if (callbackLogout) callbackLogout();

    // Check if env is dev
    if (env === ENV.DEV) {
      window.location.reload();
      return;
    }

    // Handle logout with cdp domain
    window.location.href = `${CDP_ROUTE[ENV.PROD]}/iam#/login`;

    // if (urlLogout) {
    //   window.location.href = urlLogout;
    // } else {
    //   window.location.reload();
    // }
  }, [callbackLogout, env]);

  useEffect(() => {
    const logoutEvent = (event: MessageEvent) => {
      const { data } = event;

      if (data?.type === 'cdp_request_logout') {
        onClickButtonLogout();
      }
    };
    window.addEventListener('message', logoutEvent);

    return () => {
      window.removeEventListener('message', logoutEvent);
    };
  }, [onClickButtonLogout]);

  function urlDomain(data) {
    const a = document.createElement('a');
    a.href = data;
    return a;
  }

  const postMessage = postParam => {
    if (postParam.error && postParam.error !== '') return;

    const newUrlObj = urlDomain(selectedNetworkRef?.current?.homePage);
    const arrTmp = newUrlObj.hostname.split('.');
    if (postParam.action === 'ogs-authenticate') {
      const loginData = { ...postParam };

      if (loginData.full_name) {
        delete loginData.full_name;
      }
      if (loginData.email) {
        delete loginData.email;
      }
      if (loginData.avatar) {
        delete loginData.avatar;
      }
      if (loginData.action) {
        delete loginData.action;
      }
      if (loginData.env) {
        delete loginData.env;
      }
      // if (loginData.hasOwnProperty('account_id')) {
      //     delete loginData.account_id;
      // }
      // if (loginData.hasOwnProperty('conversion_id')) {
      //     delete loginData.conversion_id;
      // }
      // if (loginData.hasOwnProperty('seller_role')) {
      //     delete loginData.seller_role;
      // }
      if (u_ogs && typeof u_ogs === 'string' && u_ogs.trim()) {
        const index = u_ogs.indexOf(`${networkId}`);

        let prefix = `${u_ogs}_`;
        if (index >= 0) {
          prefix = u_ogs.slice(0, index);
        }
        const newU_ogs = prefix + selectedNetworkRef?.current?.networkId;
        if (newUrlObj.protocol !== 'https:') {
          if (arrTmp.length < 4) {
            setCookie(newU_ogs, JSON.stringify(loginData), {
              domain: arrTmp.slice(-2).join('.'),
              path: '/; samesite=None',
            });
          } else {
            setCookie(newU_ogs, JSON.stringify(loginData), {
              domain: arrTmp.slice(-3).join('.'),
              path: '/; samesite=None',
            });
          }
        } else {
          if (arrTmp.length < 4) {
            setCookie(newU_ogs, JSON.stringify(loginData), {
              domain: arrTmp.slice(-2).join('.'),
              path: '/; samesite=None; secure',
            });
          } else {
            setCookie(newU_ogs, JSON.stringify(loginData), {
              domain: arrTmp.slice(-3).join('.'),
              path: '/; samesite=None; secure',
            });
          }
        }
      }
    }
    if (selectedNetworkRef.current && selectedNetworkRef.current.homePage) {
      getAppsMenus({
        token: postParam.token,
        userId: accountId,
        accountId,
        homePage: selectedNetworkRef.current.homePage,
        newNetworkId: selectedNetworkRef.current.networkId,
      });
      // window.location.href = selectedNetworkRef.current.homePage.replaceAll(':user_id', accountId);
    }
  };

  const handleLoginData = (data: any = null) => {
    if (!data.token) {
      return;
    }

    const postParam = {
      action: 'ogs-authenticate',
      user_id: data.personal.user_id,
      account_id: data.personal.user_id,
      full_name: data.personal.full_name,
      role: data.personal.role,
      seller_role: data.personal.seller_role,
      conversion_id: data.personal.conversion_id,
      email: data.personal.email,
      avatar: data.personal.avatar,
      token: data.token,
      language: data.personal.language,
    };
    postMessage(postParam);
  };

  const loginNetworkWithToken = async networkItem => {
    if (!token || !networkItem || !networkItem.networkId) {
      return;
    }
    selectedNetworkRef.current = networkItem;

    const loginUrl = `${domain}api/account/authenticate`;

    const { networkId: networkIdSelected } = networkItem;
    const data = {
      token,
      app_id: networkIdSelected,
    };

    const xhr = new XMLHttpRequest();
    xhr.open('POST', loginUrl, true);

    // Send the proper header information along with the request
    xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.withCredentials = false;

    xhr.onreadystatechange = function () {
      // Call a function when the state changes.
      // if (this.readyState === XMLHttpRequest.DONE) {
      //   setLoading(false)
      // }
      if (this.readyState === XMLHttpRequest.DONE && this.status === 200) {
        const res = JSON.parse(this.responseText);

        if (res && res.data && res.data.token) {
          handleLoginData(res.data);
        }
      }
    };
    xhr.send(JSON.stringify(data));

    // Set cookie and session
    setCookieSession({ name: 'api_pid', value: networkIdSelected });
  };

  const selectNetwork = network => {
    if (+networkId === +network.networkId) {
      return;
    }
    loginNetworkWithToken(network);
  };

  const handleSwitchAccount = async account => {
    await logAccountSwitch(account.user_id);

    if (callbackGetInfoAccount) callbackGetInfoAccount(account);
    // setLimitAccount(limitAccount + 10)
    // if (isShowDetail) {
    //   setIsShowDetail(false);
    // }

    setLimitAccount(limitAccount + 1);
  };

  const renderAccounts = (listAccountsRender: any[]) =>
    listAccountsRender?.map((account: any) => (
      <li key={account.user_id} onClick={() => handleSwitchAccount(account)}>
        <Typography.Text className="account-name" ellipsis={{ tooltip: true }}>
          {account.full_name}
        </Typography.Text>
        <Text className="account-number">{formatUserId(account.user_id)}</Text>
      </li>
    ));
  return (
    <CookiesProvider>
      <Popover
        open={open}
        onOpenChange={() => setOpen(p => !p)}
        content={
          <PackageSharingStyled>
            <div className="info-account">
              {infoAccount ? (
                <>
                  <div className="current-account-avatar">
                    <img src={infoAccount.avatar} alt="" />
                    {/* <a
                      className="edit-profile"
                      href={urlEditProfile}
                      target="_blank"
                      rel="noreferrer"
                    > */}
                    <div
                      className="edit-profile"
                      onClick={() => {
                        setOpen(p => !p);
                        setOpenAccountProfile(true);
                      }}
                    >
                      <Icon type="icon-ants-edit-2" style={{ color: '#aaa' }} />
                    </div>
                    {/* </a> */}
                  </div>
                  <p style={{ fontWeight: 600 }}>{infoAccount.full_name}</p>
                  <p style={{ color: '#aaa', marginTop: 2 }}>{infoAccount.email}</p>
                </>
              ) : (
                <div>{translateLang('KEY_LOADING')}...</div>
              )}
            </div>

            <InnerCardStyled>
              <div className="network-block">
                {tab === 'account' ? (
                  <div className="current-network">
                    {currentNetwork?.logo ? (
                      <div>
                        <img
                          src={currentNetwork.logo}
                          style={{
                            maxHeight: 28,
                            maxWidth: 200,
                            borderRadius: 0,
                            border: 'none',
                            boxShadow: 'none',
                          }}
                          alt="network-logo"
                        />
                      </div>
                    ) : null}
                    <Button
                      type="text"
                      className="change-network-btn"
                      onClick={() => setTab('network')}
                      icon={<Icon type="icon-ants-angle-right" style={{ fontSize: '12px' }} />}
                    >
                      {translateLang('KEY_CHANGE_PORTAL')}
                    </Button>
                  </div>
                ) : (
                  <>
                    <Flex gap={10}>
                      <Button
                        type="text"
                        onClick={() => setTab('account')}
                        icon={<Icon type="icon-ants-angle-left" style={{ fontSize: '12px' }} />}
                      >
                        {translateLang('KEY_BACK')}
                      </Button>
                      <InputSearch
                        className="--portal"
                        value={portalSearch}
                        onChange={value => setPortalSearch(value)}
                        onClickRemove={() => setPortalSearch('')}
                      />
                    </Flex>
                    <div className="network-select">
                      {filterListNetwork?.map((network: any) => (
                        <div
                          key={network.networkId}
                          className={`network-item ${
                            +networkId === +network.networkId ||
                            +currentNetwork?.networkId === +network.networkId
                              ? 'disabled'
                              : ''
                          }`}
                          onClick={() => selectNetwork(network)}
                        >
                          <div className="image-wrapper">
                            <img
                              src={network.logo}
                              style={{
                                borderRadius: 0,
                                border: 'none',
                                boxShadow: 'none',
                              }}
                              alt="logo"
                            />
                          </div>
                          <p className="network-name">{network.networkName}</p>
                        </div>
                      ))}
                    </div>
                    <div className="no-network-title">
                      <span>No networks</span>
                    </div>
                  </>
                )}
              </div>

              {tab === 'account' ? (
                isShowSharing ? (
                  <div className="div-cover-account">
                    {isShowLoading ? <Spin /> : null}

                    <InputSearch
                      value={accountSearch}
                      onChange={value => setAccountSearch(value)}
                      onClickRemove={() => {
                        setAccountSearch('');
                      }}
                    />
                    <span className="recent-title">Recent accounts</span>
                    {isShowLoading ? null : (
                      <div className="no-account-title">
                        <span>{translateLang('KEY_NO_ACCOUNTS')}</span>
                      </div>
                    )}
                    <ul style={{ opacity: `${isShowLoading ? 0.2 : 1}` }}>
                      {renderAccounts(listAccountRecent)}
                      {renderAccounts(listAccountSharing)}
                    </ul>
                  </div>
                ) : (
                  <hr style={{ margin: '10px 0 20px', background: '#E3EEF1' }} />
                )
              ) : null}
            </InnerCardStyled>

            <Flex align="center" justify="center">
              <Button onClick={() => onClickButtonLogout()} icon={<Icon type="icon-ants-logout" />}>
                {translateLang('KEY_LOGOUT')}
              </Button>
            </Flex>
          </PackageSharingStyled>
        }
        arrow={false}
        trigger={['click']}
        placement="bottomRight"
        overlayInnerStyle={{
          backgroundColor: THEME.token?.blue1_2,
          padding: '20px 15px',
          width: '400px',
        }}
      >
        <TriggerPopoverStyled>
          <div className="brand-logo">
            {currentNetwork?.logo ? <img src={currentNetwork.logo} alt="" /> : null}
          </div>
          <div className="avatar">
            {infoAccount ? <img src={infoAccount.avatar} alt="" /> : null}
          </div>
        </TriggerPopoverStyled>
      </Popover>
      <AccountProfile
        accountId={accountId}
        iamDomain={formatDomain}
        token={token}
        isDisabledEdit={false}
        open={openAccountProfile}
        onClose={() => setOpenAccountProfile(false)}
      />
    </CookiesProvider>
  );
};
