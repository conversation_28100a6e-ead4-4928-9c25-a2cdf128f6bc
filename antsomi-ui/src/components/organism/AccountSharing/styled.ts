import styled from 'styled-components';
import { THEME } from '@antscorp/antsomi-ui/es/constants';

export const InputSearchStyled = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  /* padding: 10px; */

  box-sizing: border-box;
  * {
    box-sizing: border-box;
  }

  &.--portal {
    padding: 0;
    flex: 1;
  }

  .input-search-account {
    border: none;
    border-bottom: 1px solid rgb(227, 238, 241);
    font-size: 12px;
    height: 30px;
    padding: 0 5px;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    display: block;
    width: 100%;
    &:focus {
      outline: none;
    }

    &::placeholder {
      font-style: normal !important;
    }
    &::-webkit-input-placeholder {
      font-style: normal !important;
    }
    &:-ms-input-placeholder {
      font-style: normal !important;
    }
  }

  svg {
    position: absolute;
    fill: ${THEME?.token?.bw8};
    right: 5px;
    width: 20px;
    height: 20px;

    &.search-icon {
      /* right: 5px;
      width: 18px; */
    }

    &.remove-icon {
      /* right: 5px; */
      width: 16px;
      height: 16px;
      cursor: pointer;
      /* width: 12px; */
    }
  }
`;

export const TriggerPopoverStyled = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
  padding: 3px 3px 3px 10px;
  border: 1px solid ${THEME.token?.bw3};
  background-color: white;
  border-radius: 999px;

  width: 140px;
  box-sizing: border-box;

  cursor: pointer;

  .brand-logo {
    height: 20px;
    width: 100%;
    display: flex;
    justify-content: center;
    img {
      height: 20px;
      display: block;
    }
  }
  .avatar {
    width: 30px;
    height: 30px;
    flex-shrink: 0;
    img {
      width: 30px;
      height: 30px;
      border-radius: 999px;
      display: block;
    }
  }
`;

export const PackageSharingStyled = styled.div`
  font-size: 13px;
  color: #666;

  background-color: ${THEME.token?.blue1_2};

  /* min-width: 400px;
  max-width: 400px; */

  .info-account {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 370px;
    margin-bottom: 15px;
    p {
      margin: 0;
    }

    .current-account-avatar {
      position: relative;
      margin-bottom: 10px;

      img {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        cursor: pointer;
        display: block;
      }

      .edit-profile {
        text-decoration: none;
        background-color: white;
        border-radius: 100px;
        position: absolute;
        box-sizing: border-box;
        width: 24px;
        height: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        right: 0;
        bottom: 0;
        cursor: pointer;
      }
    }

    p:first-child {
      line-height: 18px;
    }
    p:last-child {
      line-height: 13px;
    }
  }
  .div-cover-account {
    margin-top: 10px;
    min-height: 185px;
    position: relative;

    .antsomi-spin {
      position: absolute;
      top: 50px;
      width: 100%;
      height: calc(100% - 50px);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .recent-title {
      display: none;
      font-size: 11px;
      color: #999;
      padding: 10px 5px 0;
    }

    &:has(li) .recent-title {
      display: block;
    }

    .no-account-title {
      position: absolute;
      height: calc(100% - 30px);
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &:has(li) .no-account-title {
      display: none;
    }

    ul {
      overflow-y: auto;
      max-height: 143px;
      list-style: none;
      padding: 0;
      margin: 0;
      li {
        cursor: pointer;
        padding: 8px 5px;
        border-radius: ${THEME.token?.borderRadius}px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 10px;

        &:hover {
          background-color: ${THEME.token?.blue};
        }

        .account-name {
          color: #666;
          font-size: 13px;
          font-size: 13px;
          font-weight: normal;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          line-height: 1.4;
          font-size: 13px;
          font-weight: normal;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          line-height: 1.4;
        }

        .account-number {
          color: #aaa;
          font-size: 12px;
          flex-shrink: 0;
        }
      }
    }
  }

  .network-block {
    .change-network-btn {
      flex-direction: row-reverse;
      .antsomi-btn-icon {
        margin-inline-end: 0 !important;
        margin-inline-start: 8px;
      }
    }
    .current-network {
      display: flex;
      justify-content: space-between;
      align-items: center;

      & > div:has(img) {
        width: 106px;
        height: 42px;
        padding: 10px;
        border-radius: 10px;
        border: 1px solid ${THEME.token?.bw4};

        & img {
          width: 100%;
          height: auto;
        }
      }
    }

    .network-select {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 15px;
      margin-top: 26px;
      max-height: 335px;
      overflow-y: auto;
      padding: 0 7px;
      .network-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        &:hover {
          .image-wrapper {
            background-color: ${THEME.token?.blue};
            border-color: ${THEME.token?.blue3};
          }
        }
        &.disabled {
          cursor: default;
          .image-wrapper {
            background: ${THEME.token?.blue1_1};
            border-color: ${THEME.token?.blue7};
            img {
              cursor: default;
            }
          }
        }
        .image-wrapper {
          border: 1px solid #ddd;
          padding: 10px;
          border-radius: ${THEME.token?.borderRadius}px;
          transition: all 0.2s ease-in-out;
          /* width: 108px;
          height: 45px; */
          img {
            max-width: 100%;
          }
        }
        .network-name {
          color: ${THEME.token?.bw8};
          font-size: 11px;
          margin-top: 5px;
          margin-bottom: 0;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          word-break: break-word;
        }
      }
    }

    .no-network-title {
      height: 158px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .network-select:has(> div.network-item) + .no-network-title {
      display: none;
    }
  }
`;

export const InnerCardStyled = styled.div`
  width: 370px;
  background-color: white;
  border-radius: ${THEME.token?.borderRadius}px;
  padding: 10px;
  margin-bottom: 15px;
`;
