// Components
import { Meta } from '@storybook/react/*';
import PreviewCollections from './PreviewCollections';

// Libraries
export default {
  title: 'Preview/Collections',
  component: PreviewCollections,
  argTypes: {},
  parameters: {},
} as Meta<typeof PreviewCollections>;

export const Basic = {
  render: PreviewCollections,
};

export const BasicUsage = {
  render: () => <PreviewCollections />,

  parameters: {
    docs: {
      description: {
        story: '',
      },
    },
  },
};
