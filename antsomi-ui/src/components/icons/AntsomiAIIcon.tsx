import React, { forwardRef } from 'react';
import { IconProps } from './types';
import { useIcon } from './hooks/useIcon';

export const AntsomiAIIcon = forwardRef<SVGSVGElement, IconProps>((props, ref) => {
  const { width, height } = useIcon(props);
  return (
    <svg
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
      ref={ref}
      width={width}
      height={height}
    >
      <path
        d="M33.0997 25.3651L25.1083 7.28243C24.8076 6.60574 24.3188 6.02572 23.6905 5.61755C23.0675 5.20939 22.3371 4.99457 21.596 4.99457C20.8548 4.99457 20.1244 5.20939 19.5014 5.61755C18.8784 6.02572 18.3844 6.60037 18.0836 7.28243L0.3285 47.4328C0.124418 47.8947 0.0116362 48.3888 0.000895095 48.8882C-0.00984604 49.3877 0.0760831 49.8925 0.258682 50.3598C0.441282 50.827 0.715181 51.2567 1.06427 51.6219C1.41335 51.9871 1.82689 52.2771 2.28876 52.4811C2.75063 52.6852 3.24472 52.7926 3.74955 52.8088C4.25439 52.8249 4.75385 52.7336 5.22646 52.551C5.69907 52.3684 6.12872 52.0998 6.48854 51.7508C6.84837 51.4017 7.14375 50.9881 7.34783 50.5316L21.5906 18.319L27.278 31.1761L33.0997 25.3651Z"
        fill="#005EB8"
      />
      <path
        d="M42.8598 47.4328L37.6718 35.6874L31.8555 41.4823L35.8458 50.5102C36.2593 51.4393 37.022 52.1643 37.9725 52.5349C38.9231 52.9001 39.9811 52.8786 40.9102 52.4704C41.8394 52.0623 42.5698 51.2997 42.9403 50.3544C43.3109 49.4092 43.284 48.3566 42.8705 47.4275"
        fill="#005EB8"
      />
      <path
        d="M42.063 20.4941C41.3433 19.7744 40.3659 19.3716 39.3509 19.3716C38.3358 19.3716 37.3584 19.7744 36.6387 20.4941L21.4991 35.5747L15.5378 29.6348L12.2188 37.1482L18.7816 43.6842C19.5012 44.4039 20.4787 44.8067 21.4937 44.8067C22.5088 44.8067 23.4862 44.4039 24.2059 43.6842L42.0576 25.8915C42.7773 25.1772 43.1801 24.1998 43.1801 23.1847C43.1801 22.1697 42.7773 21.1976 42.0576 20.4834L42.063 20.4941Z"
        fill="#EF3340"
      />
      <path
        d="M16.6787 29.4307L12.5273 38.8185L6.71094 33.0236L10.8624 23.6359L16.6787 29.4307Z"
        fill="#005EB8"
      />
      <path
        d="M57.004 49.8389C57.7881 49.8389 58.4164 49.2052 58.8837 47.9431L60.0007 48.1203C60.0007 48.5177 59.845 49.0279 59.5281 49.6617C59.2542 50.2524 58.9213 50.7465 58.5292 51.144C57.5088 52.2503 55.9782 52.8035 53.9427 52.8035C49.7483 52.8035 47.6484 50.8486 47.6484 46.9334V23.55C47.6484 21.3427 49.4368 19.5489 51.6495 19.5489C53.8568 19.5489 55.6506 21.3373 55.6506 23.55V47.8196C55.6506 49.1622 56.1017 49.8335 57.004 49.8335V49.8389Z"
        fill="url(#paint0_linear_2513_39378)"
      />
      <path
        d="M51.4106 16.015C54.2284 16.015 56.5127 13.7307 56.5127 10.913C56.5127 8.09517 54.2284 5.81091 51.4106 5.81091C48.5929 5.81091 46.3086 8.09517 46.3086 10.913C46.3086 13.7307 48.5929 16.015 51.4106 16.015Z"
        fill="#EF3340"
      />
      {/* <defs>
<linearGradient id="paint0_linear_2513_39378" x1="53.83" y1="52.8035" x2="53.83" y2="19.5543" gradientUnits="userSpaceOnUse">
<stop stop-color="#005EB8"/>
<stop offset="1" stop-color="#EF3340"/>
</linearGradient>
</defs> */}
    </svg>
  );
});
