// Types
import React, { ReactNode } from 'react';
import { ThumbnailCardProps } from '../../../molecules';
import { TThumbnailCardId } from '../../../molecules/ThumbnailCard';
import { PreviewTemplateModalProps } from '../../../organism/PreviewTemplateModal/types';
import { BlankTemplateProps } from './BlankTemplate';
import { CategoryListingProps } from './CategoryListing';
import { EmptyProps } from './Empty';

export interface TemplateListingProps<T = {}> {
  templatesProps?: TemplatesProps<T>;
  templateItemProps?: Omit<ThumbnailCardProps, 'id' | 'thumbnail'>;
  categoryListingProps?: Omit<CategoryListingProps, 'emptyProps'>;
  blankTemplateProps?: Omit<BlankTemplateProps, 'width' | 'height'>;
  emptyProps?: EmptyProps;
  previewModalProps?: PreviewTemplateModalProps;
}

export type TemplatesProps<T = {}> = {
  header?: ReactNode;
  footer?: ReactNode;
  /** Custom content render instead render template listing */
  content?: ReactNode | ((node: ReactNode) => ReactNode);
  items?: ((TTemplateItem & T) | undefined)[];
  loading?: boolean;
  gap?: number;
  wrapperClassName?: string;
  wrapperStyle?: React.CSSProperties;

  // Callback
  onLoadMoreTemplates?: () => void;
};

export interface TTemplateItem {
  id: TThumbnailCardId;
  name?: ThumbnailCardProps['name'];
  thumbnail?: ThumbnailCardProps['thumbnail'];

  // NOTE: Will move generic type
  [key: string]: any;
}

export type TSelectTemplateAction = 'edit' | 'preview' | 'use' | string;
