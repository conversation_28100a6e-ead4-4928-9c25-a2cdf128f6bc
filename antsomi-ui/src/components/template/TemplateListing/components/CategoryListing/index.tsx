// Libraries
import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { type MenuProps } from 'antd';
import { isEmpty, isNumber } from 'lodash';
import Icon from '@antscorp/icons';
import { THEME } from '@antscorp/antsomi-ui/es/constants';
import isEqual from 'react-fast-compare';

// Types
import { TItem, TMenuItem } from './types';
import { CategoryListingProps, TCheckedCategories } from '../../types';

// Styled
import { CustomLabelStyled, CustomRootLabel, MenuWrapper, TextWrapper } from './styled';

// Components
import { Checkbox, Scrollbars, Spin } from '../../../../atoms';
import { Menu } from '../../../../organism';
import { Empty } from '../Empty';

// Utils
import { handleError } from '@antscorp/antsomi-ui/es/utils';

// Constants
import { CATEGORY_DEBOUNCE, CATEGORY_LISTING_WITH } from '../../constants';

const PATH = 'src/components/template/TemplateListing/components/CategoryListing/index.tsx';

export const CategoryListing: React.FC<CategoryListingProps> = memo(props => {
  const {
    width = CATEGORY_LISTING_WITH,
    items = [],
    loading = false,
    show = true,
    checkedCategories = {},
    emptyProps,
    debounceChangeMenu = CATEGORY_DEBOUNCE,
    header,
    footer,

    /* Wrapper */
    wrapperClassName,
    wrapperStyle,

    onMenuChange,
    ...restOfProps
  } = props;

  const [checkedState, setCheckedState] = useState<TCheckedCategories>({});

  useEffect(() => {
    setCheckedState(prev => (isEqual(prev, checkedCategories) ? prev : checkedCategories));
  }, [checkedCategories]);

  const timeoutAfterChange = useRef<any>(null);

  const handleClickMenu: MenuProps['onClick'] = e => {
    try {
      const { key, keyPath } = e;
      const parentId = keyPath?.[1];
      if (parentId) {
        const newCheckedCategories = checkedState?.[parentId]?.includes(key)
          ? {
              ...checkedState,
              [parentId]: checkedState[parentId].filter(item => item !== key),
            }
          : { ...checkedState, [parentId]: [...(checkedState?.[parentId] || []), key] };

        if (timeoutAfterChange) {
          setCheckedState(newCheckedCategories);
          clearTimeout(timeoutAfterChange.current);
        }

        timeoutAfterChange.current = setTimeout(() => {
          onMenuChange?.(newCheckedCategories);
        }, debounceChangeMenu);
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleClickMenu()',
        args: { e },
      });
    }
  };

  const renderCustomLabel = useCallback(
    (args: TItem): React.ReactNode => {
      try {
        const { key, parentId, label, total } = args;

        const isChecked = !!(!!parentId && checkedState?.[parentId]?.includes(key));
        return (
          <CustomLabelStyled onClick={e => e.preventDefault()}>
            <div className="label">
              <Checkbox checked={isChecked}>
                <TextWrapper ellipsis={{ tooltip: label }}>{label}</TextWrapper>
              </Checkbox>
            </div>
            <span className="total">{isNumber(total) ? total : ''}</span>
          </CustomLabelStyled>
        );
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'renderCustomLabel()',
          args,
        });
        return null;
      }
    },
    [checkedState],
  );

  const getItem = useCallback(
    (item: TItem, isRoot?: boolean): TMenuItem | undefined => {
      try {
        const { key, label, children } = item;
        return {
          ...item,
          ...(isRoot
            ? {
                label: (
                  <CustomRootLabel>
                    <TextWrapper ellipsis={{ tooltip: label }}>{label}</TextWrapper>
                  </CustomRootLabel>
                ),
              }
            : {}),
          children:
            !!children && !isEmpty(children)
              ? children?.map(child => {
                  const childItem: TItem = { ...child, parentId: key };
                  return getItem({
                    ...childItem,
                    label: renderCustomLabel(childItem),
                  });
                })
              : undefined,
        } as TMenuItem;
      } catch (error) {
        handleError(error, {
          path: PATH,
          name: 'getItem()',
          args: { item, isRoot },
        });
        return undefined;
      }
    },
    [renderCustomLabel],
  );

  const menuItems = useMemo(() => {
    const menuItems: MenuProps['items'] = [];
    items?.forEach((item, _) => {
      // if (index > 0) menuItems.push({ type: 'divider' });
      const menuItem: TMenuItem | undefined = getItem({ ...item, parentId: null }, true);

      if (menuItem) menuItems.push(menuItem);
    });
    return menuItems;
  }, [getItem, items]);

  return (show && !isEmpty(menuItems)) || header || loading ? (
    <MenuWrapper $width={width} className={wrapperClassName} style={wrapperStyle}>
      {header}
      <Scrollbars className="category__listing">
        <Spin spinning={loading} wrapperClassName="template-listing__loading">
          {/* {isEmpty(menuItems) && !loading ? (
            <div className="empty-wrapper">
              <Empty {...emptyProps} />
            </div>
          ) : ( */}
          <Menu
            selectedKeys={[]}
            items={menuItems}
            inlineIndent={0}
            mode="inline"
            defaultOpenKeys={items?.map(item => String(item.key))}
            expandIcon={({ isOpen }) => (
              <Icon
                type="icon-ants-expand-more"
                style={{
                  fontSize: 20,
                  color: THEME?.token?.bw8,
                  transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                  insetInlineEnd: 5,
                  position: 'absolute',
                }}
              />
            )}
            // expandIcon={
            //   <Icon
            //     type="icon-ants-expand-more"
            //     style={{
            //       fontSize: 20,
            //       color: THEME?.token?.bw8,
            //       // transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
            //     }}
            //   />
            // }
            onClick={handleClickMenu}
            {...restOfProps}
          />
          {/* )} */}
          {footer}
        </Spin>
      </Scrollbars>
    </MenuWrapper>
  ) : null;
});

CategoryListing.displayName = 'CategoryListing';
