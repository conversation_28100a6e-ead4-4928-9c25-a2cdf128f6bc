// Types
import { ThumbnailCardProps } from '../../../molecules';
import {
  BlankTemplateProps,
  CategoryListingProps,
  // TemplateListingProps,
  TemplatesProps,
} from '../types';
import { EmptyProps } from '../types/Empty';

/* Variables */
export const CATEGORY_LISTING_WITH = 230;
export const THUMBNAIL_URL = 'https://st-media-template.antsomi.com/base64-img/37563.png';
export const LISTING_GAP_DEFAULT = 30;
export const CATEGORY_DEBOUNCE = 400;

/* Default props */
export const TEMPLATE_ITEM_DEFAULT: Omit<ThumbnailCardProps, 'id'> = {
  removable: true,
};
export const BLANK_TEMPLATE_DEFAULT: BlankTemplateProps = { show: false };
export const CATEGORY_LISTING_DEFAULT: Omit<CategoryListingProps, 'items'> = {
  width: CATEGORY_LISTING_WITH,
  show: true,
  checkedCategories: {},
  onMenuChange: () => {},
};

// export const TEMPLATE_LISTING_DEFAULT: TemplateListingProps = {
//   templateItemProps: TEMPLATE_ITEM_DEFAULT,
//   blankTemplateProps: BLANK_TEMPLATE_DEFAULT,
// };

export const EMPTY_DEFAULT: EmptyProps = {
  description: 'No data',
};

export const TEMPLATES_DEFAULT: TemplatesProps = { items: [] };
