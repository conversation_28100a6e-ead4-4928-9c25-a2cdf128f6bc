// Libraries
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON>bj, Meta, StoryFn } from '@storybook/react';

// Components
import { TemplateListing } from '../..';
import { Menu } from '../../../../organism/Menu';
import { Typography } from '../../../../atoms';

// Constants
import {
  CATEGORY_DEBOUNCE,
  CATEGORY_LISTING_WITH,
  LISTING_GAP_DEFAULT,
  MENU_ITEMS_SAMPLE,
  THUMBNAIL_URL,
} from '../../constants';
import {
  THUMBNAIL_CARD_DEFAULT_HEIGHT,
  THUMBNAIL_CARD_DEFAULT_WIDTH,
} from '../../../../molecules/ThumbnailCard/constants';
import {
  GET_LIST_TYPE,
  LIMIT_LIST_PER_PAGE,
  OBJECT_TYPES,
  PUBLIC_LEVEL,
} from '@antscorp/antsomi-ui/es/constants';
import {
  HTML,
  SIM<PERSON>AR_CARD_HEIGHT_DEFAULT,
  SIM<PERSON>AR_CARD_WIDTH_DEFAULT,
  THUMBNAIL_HEIGHT_DEFAULT,
  THUMBNAIL_SAMPLES,
  THUMBNAIL_WIDTH_DEFAULT,
} from '../../../../organism/PreviewTemplateModal/constants';

// Types
import { TCategoryItem, TCheckedCategories, TTemplateItem } from '../../types';
import { TThumbnailCardId } from '../../../../molecules/ThumbnailCard';
import { TThumbnail } from '../../../../organism/PreviewTemplateModal/types';

// Hooks
import { useTemplateListing } from '../../hooks/useTemplateListing';

// Styled
import { ImageWrapper } from './styled';
import { EmptyData, Tabs } from '../../../../molecules';
import { DestinationsIcon } from '../../../../icons';

export default {
  title: 'Templates/TemplateListing/Demo',
  component: TemplateListing,
  argTypes: {
    templatesProps: {
      name: 'templatesProps',
      defaultValue: {
        items: [],
        gap: LISTING_GAP_DEFAULT,
        loading: false,
        onLoadMoreTemplates: () => {},
      },
      description: 'To customize the template listing',
      table: {
        type: { summary: 'TemplatesProps' },
        defaultValue: {
          summary: `{
            items: [],
            gap: ${LISTING_GAP_DEFAULT},
            loading: false,
            onLoadMoreTemplates: () => {},
          }`,
        },
      },
      control: 'object',
    },
    templateItemProps: {
      name: 'templateItemProps',
      defaultValue: {
        width: THUMBNAIL_CARD_DEFAULT_WIDTH,
        height: THUMBNAIL_CARD_DEFAULT_HEIGHT,
        removable: true,
      },
      description: 'To customize the template item',
      table: {
        type: {
          summary: "Omit<TemplateItemProps, 'id' | 'thumbnail'>",
        },
        defaultValue: {
          summary: `{
          width: ${THUMBNAIL_CARD_DEFAULT_WIDTH},
          height: ${THUMBNAIL_CARD_DEFAULT_HEIGHT},
          removable: true,
        }`,
        },
      },
      control: 'object',
    },
    categoryListingProps: {
      name: 'categoryListingProps',
      defaultValue: {
        items: MENU_ITEMS_SAMPLE,
        width: CATEGORY_LISTING_WITH,
        show: true,
        checkedCategories: {},
        debounceChangeMenu: CATEGORY_DEBOUNCE,
        onMenuChange: () => {},
      },
      description: 'To customize the listing category menu',
      table: {
        type: { summary: 'CategoryListingProps' },
        defaultValue: {
          summary: `{
            items: MENU_ITEMS_SAMPLE,
            show: true,
            width: ${CATEGORY_LISTING_WITH},
            checkedCategories: {},
            debounceChangeMenu: ${CATEGORY_DEBOUNCE},
            onMenuChange: () => {},
          }`,
        },
      },
      control: 'object',
    },
    blankTemplateProps: {
      name: 'blankTemplateProps',
      defaultValue: { show: false },
      description: 'To customize Blank Template',
      table: {
        type: { summary: "Omit<BlankTemplateProps, 'width' | 'height'>" },
        defaultValue: { summary: `{ show: false }` },
      },
      control: 'object',
    },
    previewModalProps: {
      name: 'previewModalProps',
      defaultValue: {
        loading: false,
        bannerProps: { showBackgroundSkeleton: false },
        similarTemplateProps: {
          show: true,
          similarCardProps: {
            width: SIMILAR_CARD_WIDTH_DEFAULT,
            height: SIMILAR_CARD_HEIGHT_DEFAULT,
          },
        },
        thumbnailProps: {
          show: true,
          width: THUMBNAIL_WIDTH_DEFAULT,
          height: THUMBNAIL_HEIGHT_DEFAULT,
        },
        informationProps: {
          itemName: 'Template name',
          categoryListing: MENU_ITEMS_SAMPLE,
          buttonText: 'Use template',
          showDeviceRadios: true,
          onButtonClick: () => {},
        },
      },
      description: 'To customize Preview Modal',
      table: {
        type: { summary: 'PreviewTemplateModalProps' },
        defaultValue: { summary: `{ show: false }` },
      },
      control: 'object',
    },
    emptyProps: {
      name: 'emptyProps',
      defaultValue: {
        description: 'No data',
      },
      description: 'To customize empty component',
      table: {
        type: { summary: 'EmptyProps' },
        defaultValue: {
          summary: `{
          description: 'No data',
        }`,
        },
      },
      control: 'object',
    },
  },
  parameters: {
    docs: {
      description: {
        component: 'A Template Listing. \n',
      },
    },
  },
} as Meta<typeof TemplateListing>;

const Template: StoryFn<typeof TemplateListing> = args => {
  const { categoryListingProps, templatesProps, ...restOfArgs } = args;

  // States
  const [checkedCategories, setCheckedCategories] = useState<TCheckedCategories>({});

  // Variables
  const templateQuantity = 14;
  const templateItems: TTemplateItem[] = [];
  for (let i = 1; i <= templateQuantity; i++) {
    templateItems.push({
      id: `template-${i}`,
      name: `template ${i}`,
      thumbnail: THUMBNAIL_URL,
    });
  }

  // Handlers
  const onMenuChange = useCallback(
    (value: TCheckedCategories) => {
      console.log({ value });
      setCheckedCategories(value);
    },
    [setCheckedCategories],
  );

  return (
    <div
      style={{
        height: '100vh',
      }}
    >
      <TemplateListing
        {...restOfArgs}
        templatesProps={{ ...templatesProps, items: templateItems }}
        categoryListingProps={{
          ...categoryListingProps,
          items: MENU_ITEMS_SAMPLE,
          checkedCategories,
          onMenuChange,
        }}
        previewModalProps={{ open: false }}
      />
    </div>
  );
};

// Variables
export const TEMPLATE_TAB_MENU_KEYS = {
  templateGallery: 'templateGallery',
  myTemplate: 'myTemplate',
  sharedWithMe: 'sharedWithMe',
} as const;
const { templateGallery, myTemplate, sharedWithMe } = TEMPLATE_TAB_MENU_KEYS;

export const TEMPLATE_TAB_MENU_ITEMS = {
  [templateGallery]: {
    key: templateGallery,
    label: 'Template Gallery',
    publicLevel: PUBLIC_LEVEL.PUBLIC,
    getListType: GET_LIST_TYPE.OWNER,
  },
  [myTemplate]: {
    key: myTemplate,
    label: 'My Template',
    publicLevel: PUBLIC_LEVEL.RESTRICTED,
    getListType: GET_LIST_TYPE.OWNER,
  },
  [sharedWithMe]: {
    key: sharedWithMe,
    label: 'Shared With Me',
    publicLevel: PUBLIC_LEVEL.RESTRICTED,
    getListType: GET_LIST_TYPE.SHARE_WITH_ME,
  },
};

export const Default = {
  render: Template,
  args: {},
};

export const BasicUsage: StoryObj<any> = {
  render: () => {
    // States
    const [checkedCategories, setCheckedCategories] = useState<TCheckedCategories>({});

    // Variables
    const templateQuantity = 4;
    const templateItems: TTemplateItem[] = [];
    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;
    for (let i = 1; i <= templateQuantity; i++) {
      templateItems.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }

    // Handlers
    const onMenuChange = useCallback(
      (value: TCheckedCategories) => {
        console.log({ value });
        setCheckedCategories(value);
      },
      [setCheckedCategories],
    );

    return (
      <TemplateListing
        templatesProps={{ items: templateItems }}
        categoryListingProps={{ items: categoryItems, checkedCategories, onMenuChange }}
        previewModalProps={{ open: false }}
      />
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Simple Template Listing with actions.',
      },
    },
  },
};

export const CustomizeTemplateListing: StoryObj<any> = {
  render: () => {
    // States
    const [loading, setLoading] = useState<boolean>(false);
    const [templateQuantity, setTemplateQuantity] = useState<number>(10);
    const [checkedCategories, setCheckedCategories] = useState<TCheckedCategories>({});

    // Variables
    const templateItems: TTemplateItem[] = [];
    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;
    for (let i = 1; i <= templateQuantity; i++) {
      templateItems.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }

    // Handlers
    const handleLoadMoreTemplates = () => {
      setLoading(true);
      setTimeout(() => {
        setTemplateQuantity(prev => prev + 4);
        setLoading(false);
      }, 1000);
    };

    const onMenuChange = useCallback(
      (value: TCheckedCategories) => {
        console.log({ value });
        setCheckedCategories(value);
      },
      [setCheckedCategories],
    );

    return (
      <div
        style={{
          height: '100vh',
        }}
      >
        <TemplateListing
          templatesProps={{
            items: templateItems,
            loading,
            onLoadMoreTemplates: handleLoadMoreTemplates,
          }}
          categoryListingProps={{ items: categoryItems, checkedCategories, onMenuChange }}
          previewModalProps={{ open: false }}
        />
      </div>
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Customize Template Listing.',
      },
    },
  },
};

export const BlankTemplate: StoryObj<any> = {
  render: () => {
    // States
    const [checkedCategories, setCheckedCategories] = useState<TCheckedCategories>({});

    // Variables
    const templateQuantity = 4;
    const templateItems: TTemplateItem[] = [];
    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;

    for (let i = 1; i <= templateQuantity; i++) {
      templateItems.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }

    // Handlers
    const handleClickAddTemplate = e => {
      console.log({ e });
    };

    const onMenuChange = useCallback(
      (value: TCheckedCategories) => {
        console.log({ value });
        setCheckedCategories(value);
      },
      [setCheckedCategories],
    );

    return (
      <TemplateListing
        templatesProps={{ items: templateItems }}
        categoryListingProps={{ items: categoryItems, checkedCategories, onMenuChange }}
        blankTemplateProps={{
          show: true,
          description: 'Blank Template',
          onClick: handleClickAddTemplate,
          blankTemplateRender: node => <div>{node}</div>,
        }}
        previewModalProps={{ open: false }}
      />
    );
  },

  parameters: {
    docs: {
      description: {
        story:
          'Simple Template Listing with actions. \n' +
          '- This component will be hide by default \n' +
          '- Width and height of this Blank Template is based on width and height of Template Item \n',
      },
    },
  },
};

export const CustomizeTemplateItem: StoryObj<any> = {
  render: () => {
    // States
    const [checkedCategories, setCheckedCategories] = useState<TCheckedCategories>({});

    // Variables
    const templateQuantity = 4;
    const templateItems: TTemplateItem[] = [];
    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;
    for (let i = 1; i <= templateQuantity; i++) {
      templateItems.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }

    // Handlers
    const onClickEdit = (id: TThumbnailCardId) => {
      console.log('Click edit: ', id);
    };

    const onClickPreview = (id: TThumbnailCardId) => {
      console.log('Click preview: ', id);
    };

    const onConfirmRemove = async (id: TThumbnailCardId) => {
      console.log('Click remove: ', id);
    };

    const onMenuChange = useCallback(
      (value: TCheckedCategories) => {
        console.log({ value });
        setCheckedCategories(value);
      },
      [setCheckedCategories],
    );

    return (
      <TemplateListing
        templatesProps={{ items: templateItems }}
        categoryListingProps={{ items: categoryItems, checkedCategories, onMenuChange }}
        templateItemProps={{
          width: 250,
          height: 250,
          removable: true,
          previewBtnProps: {
            text: 'Edit Template',
            onClick: onClickPreview,
          },
          editBtnProps: { text: 'Preview', onClick: onClickEdit },
          removeModalProps: {
            onOk: onConfirmRemove,
          },
        }}
        previewModalProps={{ open: false }}
      />
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Simple Template Item Customization.',
      },
    },
  },
};

export const CustomizeCategoryListing: StoryObj<any> = {
  render: () => {
    // States
    const [checkedCategories, setCheckedCategories] = useState<TCheckedCategories>({});

    // Variables
    const templateQuantity = 4;
    const templateItems: TTemplateItem[] = [];
    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;
    for (let i = 1; i <= templateQuantity; i++) {
      templateItems.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }

    // Side Effects
    useEffect(() => {
      setCheckedCategories({
        template_type: ['fullscreen', 'popup', 'slide_in'],
        seasonal: ['spring', 'christmas'],
      });
    }, []);

    // Handlers
    const onMenuChange = useCallback(
      (value: TCheckedCategories) => {
        console.log({ value });
        setCheckedCategories(value);
      },
      [setCheckedCategories],
    );

    return (
      <TemplateListing
        templatesProps={{ items: templateItems }}
        categoryListingProps={{
          items: categoryItems,
          width: 300,
          checkedCategories,
          defaultOpenKeys: Object.keys(checkedCategories),
          /** Handle the onMenuChange function immediately */
          debounceChangeMenu: 0,
          onMenuChange,
        }}
        previewModalProps={{ open: false }}
      />
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Simple Category Listing Customization.',
      },
    },
  },
};

export const WithoutCategoryListing: StoryObj<any> = {
  render: () => {
    // Variables
    const templateQuantity = 4;
    const templateItems: TTemplateItem[] = [];
    for (let i = 1; i <= templateQuantity; i++) {
      templateItems.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }

    return (
      <TemplateListing
        templatesProps={{ items: templateItems }}
        categoryListingProps={{
          show: false,
        }}
        previewModalProps={{ open: false }}
      />
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Template Listing without Category Listing.',
      },
    },
  },
};

export const WithUseTemplateListingHook: StoryObj<any> = {
  render: () => {
    const {
      categoryItems,
      templateItems,
      checkedCategories,
      templateDetail,
      openCategoryKeys,
      isLoadingCategoryList,
      selectTemplateAction,
      onLoadMore: onLoadMoreTemplates,
      onChangeOpenCategoryKeys,
      onChangeCheckedCategories,
      onRemoveObjectTemplate,
    } = useTemplateListing({
      serviceAuth: {
        url: 'https://sandbox-media-template.antsomi.com/cdp/api/v1',
        // token: '5474r2x214z254a4u2a4y444m4j5p27444c4h4h4n5t5',
        // userId: '**********',
        // accountId: '**********',
      },
      config: {
        getListType: GET_LIST_TYPE.OWNER,
        objectType: OBJECT_TYPES.MEDIA_TEMPLATE,
        publicLevel: PUBLIC_LEVEL.PUBLIC,
        limitListPerPage: LIMIT_LIST_PER_PAGE,
      },
    });

    // Side Effects
    useEffect(() => {
      if (templateDetail) {
        switch (selectTemplateAction) {
          case 'edit':
            console.log('Action edit template');
            break;
          case 'use':
            console.log('Action use template');
            break;
          case 'preview':
            console.log('Action preview template');
            break;
          default:
            break;
        }
      }
    }, [selectTemplateAction, templateDetail]);

    return (
      <div
        style={{
          height: '100vh',
        }}
      >
        <TemplateListing
          templatesProps={{
            items: templateItems,
            // loading: isLoadingTemplateList,
            onLoadMoreTemplates,
          }}
          categoryListingProps={{
            items: categoryItems,
            loading: isLoadingCategoryList,
            checkedCategories,
            openKeys: openCategoryKeys,
            onOpenChange: onChangeOpenCategoryKeys,
            onMenuChange: onChangeCheckedCategories,
          }}
          templateItemProps={{
            removeModalProps: {
              onOk: onRemoveObjectTemplate,
            },
          }}
          previewModalProps={{ open: false }}
        />
      </div>
    );
  },

  name: 'With useTemplateListing hook',

  parameters: {
    docs: {
      description: {
        story: 'Template Listing with useTemplateListing hook.',
      },
    },
  },
};

export const WithShowPreviewModal: StoryObj<any> = {
  render: () => {
    const {
      templateItems,
      categoryItems,
      similarTemplates,
      templateDetail,
      checkedCategories,
      openCategoryKeys,
      selectTemplateAction,
      isLoadingCategoryList,
      isLoadingTemplateList,
      isLoadingTemplateDetail,
      onLoadMore: onLoadMoreTemplates,
      onChangeOpenCategoryKeys,
      onChangeCheckedCategories,
      onRemoveObjectTemplate,
      onSelectTemplate,
    } = useTemplateListing<'create' | 'update'>({
      serviceAuth: {
        url: 'https://sandbox-media-template.antsomi.com/cdp/api/v1',
        token: '5474r2x214z254a4u2a4y444m4j5p27444c4h4h4n5t5',
        userId: '**********',
        accountId: '**********',
      },
      config: {
        getListType: GET_LIST_TYPE.OWNER,
        objectType: OBJECT_TYPES.MEDIA_TEMPLATE,
        publicLevel: PUBLIC_LEVEL.RESTRICTED,
        limitListPerPage: LIMIT_LIST_PER_PAGE,
      },
      checkedCategoriesDefault: {
        device_type: [1, 2],
        template_type: [1, 2, 3, 4, 5],
      },
    });

    // Variables
    const thumbnails: TThumbnail[] = THUMBNAIL_SAMPLES;

    // Map Data
    const previewModalProps = useMemo(() => {
      const { settings } = templateDetail || {};
      const { name, thumbnail, description } = settings || {};
      return {
        name,
        image: thumbnail,
        description,
      };
    }, [templateDetail]);

    // Side Effects
    useEffect(() => {
      if (templateDetail) {
        switch (selectTemplateAction) {
          case 'edit':
            console.log('Action edit template');
            break;
          case 'use':
            console.log('Action use template');
            break;
          case 'preview':
            console.log('Action preview template');
            break;
          case 'create':
            console.log('Action create template');
            break;
          case 'update':
            console.log('Action update template');
            break;
          default:
            break;
        }
      }
    }, [templateDetail, selectTemplateAction]);

    // Handlers
    const handleClickPreviewModalThumbnail = (thumbnail: TThumbnail) => {
      console.log({ thumbnail });
    };

    return (
      <div
        style={{
          height: '100vh',
        }}
      >
        <TemplateListing
          templatesProps={{
            items: templateItems,
            loading: isLoadingTemplateList,
            onLoadMoreTemplates,
          }}
          categoryListingProps={{
            items: categoryItems,
            loading: isLoadingCategoryList,
            checkedCategories,
            openKeys: openCategoryKeys,
            onOpenChange: onChangeOpenCategoryKeys,
            onMenuChange: onChangeCheckedCategories,
          }}
          templateItemProps={{
            previewBtnProps: {
              onClick: id => onSelectTemplate(id, 'preview'),
            },
            editBtnProps: { onClick: id => onSelectTemplate(id, 'edit') },
            removeModalProps: {
              onOk: onRemoveObjectTemplate,
            },
          }}
          blankTemplateProps={{
            show: true,
          }}
          previewModalProps={{
            bannerProps: { children: <ImageWrapper src={previewModalProps?.image} alt="" /> },
            thumbnailProps: {
              thumbnails,
              onClickThumbnail: handleClickPreviewModalThumbnail,
            },
            informationProps: {
              itemName: previewModalProps?.name,
              categoryListing: categoryItems,
              description: previewModalProps?.description,

              /* Click use/create template button */
              onButtonClick: () => {},
            },
            similarTemplateProps: {
              similarTemplates,
              similarCardProps: {
                onClickWrapper: id => onSelectTemplate(id, 'preview'),
              },
            },
            loading: isLoadingTemplateDetail,
          }}
        />
      </div>
    );
  },

  name: 'Template Listing With Showing Preview Modal',

  parameters: {
    docs: {
      description: {
        story: 'Template Listing with showing preview modal on click preview button.',
      },
    },
  },
};

export const CustomizePreviewModal: StoryObj<any> = {
  render: () => {
    const {
      templateItems,
      categoryItems,
      similarTemplates,
      templateDetail,
      checkedCategories,
      openCategoryKeys,
      selectTemplateAction,
      isLoadingCategoryList,
      isLoadingTemplateList,
      isLoadingTemplateDetail,
      onLoadMore: onLoadMoreTemplates,
      onChangeOpenCategoryKeys,
      onChangeCheckedCategories,
      onRemoveObjectTemplate,
      onSelectTemplate,
    } = useTemplateListing<'create' | 'update'>({
      serviceAuth: {
        url: 'https://sandbox-media-template.antsomi.com/cdp/api/v1',
        token: '5474r2x214z254a4u2a4y444m4j5p27444c4h4h4n5t5',
        userId: '**********',
        accountId: '**********',
      },
      config: {
        getListType: GET_LIST_TYPE.OWNER,
        objectType: OBJECT_TYPES.RICH_MENU_TEMPLATE,
        publicLevel: PUBLIC_LEVEL.RESTRICTED,
        limitListPerPage: LIMIT_LIST_PER_PAGE,
      },
      checkedCategoriesDefault: {
        device_type: [1, 2],
        template_type: [1, 2, 3, 4, 5],
      },
    });

    // States
    const [openPreviewModal, setOpenPreviewModal] = useState<boolean>(false);

    // Map Data
    const previewModalProps = useMemo(() => {
      const { settings } = templateDetail || {};
      const { name, thumbnail, description } = settings || {};
      return {
        name,
        image: thumbnail,
        description,
      };
    }, [templateDetail]);

    // Side Effects
    useEffect(() => {
      if (templateDetail) {
        switch (selectTemplateAction) {
          case 'edit':
            console.log('Action edit template');
            break;
          case 'use':
            console.log('Action use template');
            break;
          case 'preview':
            console.log('Action preview template');
            break;
          case 'create':
            console.log('Action create template');
            break;
          case 'update':
            console.log('Action update template');
            break;
          default:
            break;
        }
      }
    }, [templateDetail, selectTemplateAction]);

    // Handlers
    const handleClickPreviewButton = (id: TThumbnailCardId) => {
      setOpenPreviewModal(true);
      onSelectTemplate(id, 'preview');
    };

    const handleClosePreviewModal = () => {
      setOpenPreviewModal(false);
    };

    /** On click use/create template */
    const handleClickInformationButton = () => {
      setOpenPreviewModal(false);
    };

    return (
      <div
        style={{
          height: '100vh',
        }}
      >
        <TemplateListing
          templatesProps={{
            items: templateItems,
            loading: isLoadingTemplateList,
            onLoadMoreTemplates,
          }}
          categoryListingProps={{
            items: categoryItems,
            loading: isLoadingCategoryList,
            checkedCategories,
            openKeys: openCategoryKeys,
            onOpenChange: onChangeOpenCategoryKeys,
            onMenuChange: onChangeCheckedCategories,
          }}
          templateItemProps={{
            previewBtnProps: {
              onClick: handleClickPreviewButton,
            },
            editBtnProps: { onClick: id => onSelectTemplate(id, 'edit') },
            removeModalProps: {
              onOk: onRemoveObjectTemplate,
            },
          }}
          blankTemplateProps={{
            show: true,
          }}
          previewModalProps={{
            open: openPreviewModal,
            bannerProps: {
              children: (
                <iframe title="Preview" srcDoc={HTML} style={{ width: '100%', height: '100%' }} />
              ),
            },
            informationProps: {
              itemName: previewModalProps?.name,
              categoryListing: categoryItems,
              description: previewModalProps?.description,
              onButtonClick: handleClickInformationButton,
            },
            similarTemplateProps: {
              similarTemplates,
              similarCardProps: {
                height: 410,
                onClickWrapper: id => onSelectTemplate(id, 'preview'),
              },
            },
            loading: isLoadingTemplateDetail,
            onCancel: handleClosePreviewModal,
          }}
        />
      </div>
    );
  },

  name: 'With Preview Modal Customization',

  parameters: {
    docs: {
      description: {
        story: 'Template Listing with Preview Modal Customization.',
      },
    },
  },
};

export const WithExtraHeader: StoryObj<any> = {
  render: () => {
    const [activeTab, setActiveTab] = useState<string>(templateGallery);

    // Memos
    const publicLevel = useMemo(() => TEMPLATE_TAB_MENU_ITEMS[activeTab].publicLevel, [activeTab]);

    const getListType = useMemo(() => TEMPLATE_TAB_MENU_ITEMS[activeTab].getListType, [activeTab]);

    const {
      categoryItems,
      templateItems,
      checkedCategories,
      templateDetail,
      openCategoryKeys,
      isLoadingCategoryList,
      selectTemplateAction,
      onLoadMore: onLoadMoreTemplates,
      onChangeOpenCategoryKeys,
      onChangeCheckedCategories,
      onRemoveObjectTemplate,
    } = useTemplateListing({
      serviceAuth: {
        url: 'https://sandbox-media-template.antsomi.com/cdp/api/v1',
        token: '5474r2x214z254a4u2a4y444m4j5p27444c4h4h4n5t5',
        userId: '**********',
        accountId: '**********',
      },
      config: {
        getListType,
        objectType: OBJECT_TYPES.MEDIA_TEMPLATE,
        publicLevel,
        limitListPerPage: LIMIT_LIST_PER_PAGE,
      },
      checkedCategoriesDefault: {
        device_type: [1, 2],
        template_type: [1, 2, 3, 4, 5],
      },
    });

    // Side Effects
    useEffect(() => {
      if (templateDetail) {
        switch (selectTemplateAction) {
          case 'edit':
            console.log('Action edit template');
            break;
          case 'use':
            console.log('Action use template');
            break;
          case 'preview':
            console.log('Action preview template');
            break;
          default:
            break;
        }
      }
    }, [selectTemplateAction, templateDetail]);

    return (
      <div
        style={{
          height: '90vh',
        }}
      >
        <TemplateListing
          templatesProps={{
            header: (
              <Tabs
                items={Object.values(TEMPLATE_TAB_MENU_ITEMS)}
                style={{ marginBottom: 15 }}
                activeKey={activeTab}
                onChange={activeKey => setActiveTab(activeKey)}
              />
            ),
            items: templateItems,
            wrapperStyle: { padding: '0 15px' },
            onLoadMoreTemplates,
          }}
          categoryListingProps={{
            header: (
              <Menu
                defaultSelectedKeys={['item-1']}
                mode="inline"
                items={[
                  { key: 'item-1', label: 'Start from scratch' },
                  { key: 'item-2', label: 'Message Templates' },
                  { key: 'item-3', label: 'Journey Tactics' },
                ]}
              />
            ),
            width: 250,
            wrapperStyle: { padding: '0 10px' },
            items: categoryItems,
            loading: isLoadingCategoryList,
            checkedCategories,
            openKeys: openCategoryKeys,
            onOpenChange: onChangeOpenCategoryKeys,
            onMenuChange: onChangeCheckedCategories,
          }}
          templateItemProps={{
            removeModalProps: {
              onOk: onRemoveObjectTemplate,
            },
            actionButtons: {
              EDIT: {
                buttonProps: {
                  onClick: id => {
                    console.log('id');
                  },
                },
              },
              DUPLICATE: {
                buttonProps: {
                  onClick: id => {},
                },
              },
            },
          }}
          previewModalProps={{ open: false }}
          blankTemplateProps={{
            show: true,
          }}
        />
      </div>
    );
  },
  name: 'With Extra Header',
  parameters: {
    docs: {
      description: {
        story: 'Template Listing with Extra Header.',
      },
    },
  },
};

export const WithCustomContent: StoryObj<any> = {
  render: () => {
    const [activeTab, setActiveTab] = useState<string>(templateGallery);

    // Memos
    const publicLevel = useMemo(() => TEMPLATE_TAB_MENU_ITEMS[activeTab].publicLevel, [activeTab]);

    const getListType = useMemo(() => TEMPLATE_TAB_MENU_ITEMS[activeTab].getListType, [activeTab]);

    const {
      categoryItems,
      templateItems,
      checkedCategories,
      templateDetail,
      openCategoryKeys,
      isLoadingCategoryList,
      selectTemplateAction,
      onLoadMore: onLoadMoreTemplates,
      onChangeOpenCategoryKeys,
      onChangeCheckedCategories,
      onRemoveObjectTemplate,
    } = useTemplateListing({
      serviceAuth: {
        url: 'https://sandbox-media-template.antsomi.com/cdp/api/v1',
        token: '5474r2x214z254a4u2a4y444m4j5p27444c4h4h4n5t5',
        userId: '**********',
        accountId: '**********',
      },
      config: {
        getListType,
        objectType: OBJECT_TYPES.MEDIA_TEMPLATE,
        publicLevel,
        limitListPerPage: LIMIT_LIST_PER_PAGE,
      },
      checkedCategoriesDefault: {
        device_type: [1, 2],
        template_type: [1, 2, 3, 4, 5],
      },
    });

    // Side Effects
    useEffect(() => {
      if (templateDetail) {
        switch (selectTemplateAction) {
          case 'edit':
            console.log('Action edit template');
            break;
          case 'use':
            console.log('Action use template');
            break;
          case 'preview':
            console.log('Action preview template');
            break;
          default:
            break;
        }
      }
    }, [selectTemplateAction, templateDetail]);

    return (
      <div
        style={{
          height: '90vh',
        }}
      >
        <TemplateListing
          templatesProps={{
            header: (
              <Tabs
                items={Object.values(TEMPLATE_TAB_MENU_ITEMS)}
                style={{ marginBottom: 15 }}
                activeKey={activeTab}
                onChange={activeKey => setActiveTab(activeKey)}
              />
            ),
            content: (
              <EmptyData
                icon={<DestinationsIcon />}
                title="No destination available"
                description={
                  <span>
                    Create a <Typography.Link>Line Rich Menu</Typography.Link> destination to use
                    thi feature
                  </span>
                }
              />
            ),
            items: templateItems,
            wrapperStyle: { padding: '0 15px' },
            onLoadMoreTemplates,
          }}
          categoryListingProps={{
            header: (
              <Menu
                defaultSelectedKeys={['item-1']}
                mode="inline"
                items={[
                  { key: 'item-1', label: 'Start from scratch' },
                  { key: 'item-2', label: 'Message Templates' },
                  { key: 'item-3', label: 'Journey Tactics' },
                ]}
              />
            ),
            width: 250,
            wrapperStyle: { padding: '0 10px' },
            items: categoryItems,
            loading: isLoadingCategoryList,
            checkedCategories,
            openKeys: openCategoryKeys,
            onOpenChange: onChangeOpenCategoryKeys,
            onMenuChange: onChangeCheckedCategories,
          }}
          templateItemProps={{
            removeModalProps: {
              onOk: onRemoveObjectTemplate,
            },
            actionButtons: {
              EDIT: {
                buttonProps: {
                  onClick: id => {
                    console.log('id');
                  },
                },
              },
              DUPLICATE: {
                buttonProps: {
                  onClick: id => {},
                },
              },
            },
          }}
          previewModalProps={{ open: false }}
          blankTemplateProps={{
            show: true,
          }}
        />
      </div>
    );
  },
  name: 'With Custom Content',
  parameters: {
    docs: {
      description: {
        story: 'Template Listing with Custom Content.',
      },
    },
  },
};
