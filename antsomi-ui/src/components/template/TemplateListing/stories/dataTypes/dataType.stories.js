import LinkTo from '@storybook/addon-links/react';
import { Button, Tag, Table } from '@antscorp/antsomi-ui/es/index';
import {
  DataTypeTable,
  TemplatesPropsTable,
  CategoryListingPropsTable,
  BlankTemplatePropsTable,
  EmptyPropsTable,
  TTemplateItemTable,
  TCategoryItemTable,
} from './components';

export default {
  title: 'Templates/TemplateListing/DataType',
};

export const DataTypes = {
  render: () => <DataTypeTable />,
  name: 'DataTypes',
};

export const TemplatesProps = {
  render: () => <TemplatesPropsTable />,
  name: 'TemplatesProps',
};

export const CategoryListingProps = {
  render: () => <CategoryListingPropsTable />,
  name: 'CategoryListingProps',
};

export const BlankTemplateProps = {
  render: () => <BlankTemplatePropsTable />,
  name: 'BlankTemplateProps',
};

export const EmptyProps = {
  render: () => <EmptyPropsTable />,
  name: 'EmptyProps',
};

export const TTemplateItem = {
  render: () => <TTemplateItemTable />,
  name: 'TTemplateItem',
};

export const TCategoryItem = {
  render: () => <TCategoryItemTable />,
  name: 'TCategoryItem',
};
