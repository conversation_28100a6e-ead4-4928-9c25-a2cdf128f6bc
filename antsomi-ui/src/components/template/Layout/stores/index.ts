// Libraries
import { create } from 'zustand';
import { cloneDeep, merge } from 'lodash';

// Types
import { HeaderV2Props } from '../../../molecules';
import { DeepPartial } from '@antscorp/antsomi-ui/es/types';
import { ContentWrapperProps, WorkspaceProps } from '../types';
import { LeftMenuProps } from '../../../organism';

export interface HeaderStore extends Partial<HeaderV2Props> {
  browserTitle?: string;
}

export interface LeftMenuStore extends LeftMenuProps {
  show?: boolean;
  onClickCreateDashboard?: React.MouseEventHandler<HTMLElement>;
}

export interface LayoutStoreState {
  header: HeaderStore;
  leftMenu: LeftMenuStore;
  workspace: WorkspaceProps;
  contentWrapper: ContentWrapperProps;
  /** Just show only content */
  onlyContent?: boolean;
}

interface LayoutStore {
  state: LayoutStoreState;
  setHeaderState: (state: DeepPartial<HeaderStore>) => void;
  setLeftMenuState: (state: DeepPartial<LeftMenuStore>) => void;
  setWorkspaceState: (state: Partial<WorkspaceProps>) => void;
  setLayoutState: (state: DeepPartial<LayoutStoreState>) => void;
  setContentWrapperState: (state: DeepPartial<ContentWrapperProps>) => void;
  resetHeaderState: () => void;
  resetLeftMenuState: () => void;
  resetWorkspaceState: () => void;
  resetLayoutState: () => void;
}

const initialState: LayoutStoreState = {
  header: {},
  leftMenu: {},
  workspace: {},
  contentWrapper: {},
  onlyContent: false,
};

export const useLayoutStore = create<LayoutStore>(set => ({
  state: initialState,
  setLayoutState: newState =>
    set(store => ({ state: merge(cloneDeep(store.state), cloneDeep(newState)) })),
  setHeaderState: newState =>
    set(store => ({
      state: { ...store.state, header: { ...store.state.header, ...newState } },
    })),
  setLeftMenuState: newState =>
    set(store => ({
      state: { ...store.state, leftMenu: { ...store.state.leftMenu, ...newState } },
    })),
  setWorkspaceState: newState =>
    set(store => ({
      state: { ...store.state, workspace: { ...store.state.workspace, ...newState } },
    })),
  setContentWrapperState: newState =>
    set(store => ({
      state: { ...store.state, contentWrapper: { ...store.state.contentWrapper, ...newState } },
    })),
  resetHeaderState: () =>
    set(store => ({
      state: { ...store.state, header: {} },
    })),
  resetLeftMenuState: () =>
    set(store => ({
      state: { ...store.state, leftMenu: {} },
    })),
  resetWorkspaceState: () =>
    set(store => ({
      state: { ...store.state, workspace: {} },
    })),
  resetContentWrapperState: () =>
    set(store => ({
      state: {
        ...store.state,
        contentWrapper: {},
      },
    })),
  resetLayoutState: () =>
    set(() => ({
      state: {
        contentWrapper: {} as ContentWrapperProps,
        header: {} as HeaderStore,
        leftMenu: {},
        workspace: {},
      },
    })),
}));
