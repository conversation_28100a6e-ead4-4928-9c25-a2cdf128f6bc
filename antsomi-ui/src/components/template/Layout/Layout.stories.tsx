// Libraries
import React, { useCallback, useEffect, useState } from 'react';
import { StoryObj, Meta, StoryFn } from '@storybook/react';
import { useLocation } from 'react-router-dom';
import { <PERSON><PERSON>, Flex } from 'antd';

// Components
import { Layout } from './Layout';
import { AppConfigProvider, AppConfigProviderProps } from '../../../providers';
import { TemplateListing } from '../TemplateListing';

// Types
import { TCategoryItem, TCheckedCategories, TTemplateItem } from '../TemplateListing/types';

// Constants
import { MENU_ITEMS_SAMPLE, THUMBNAIL_URL } from '../TemplateListing/constants';

// Utils
import { getGeneratePath } from '../../organism/LeftMenu/utils';

// Hooks
import { useCustomRouter } from '@antscorp/antsomi-ui/es/hooks';

// Stores
import { useLayoutStore } from './stores';

export default {
  title: 'Templates/Layout',
  component: Layout,
  argTypes: {
    headerProps: {
      config: {
        env: 'DEVELOPMENT',
        token: '5474r2x214r284b41354y4u5b4g5o464i4f4m4d4t5v5',
        userId: **********,
        accountId: **********,

        // userId: **********,
        // portalId: 33167,
        // token: '5474r2x214z2a4a4u254y4p5p41694x5j4c4t47454o5',
      },

      // accountSelection: {currentAccount: },
      helpConfig: {
        configs: {
          appCode: 'SANDBOX_MARKETING',
          avatar: '//c0-platform.ants.tech/avatar/2021/09/17/0xgbkurioo.png',
          config: {
            p_timezone: 'Asia/Singapore',
            api_pid: 33167,
            p_f_longdatetime: "dd MMMM 'at' HH:mm:ss",
            embeddedData: {},
            INSIGHT_U_OGS: 'uogs',
          },
        },
      },
      accountSharingConfig: {
        u_ogs: 'uogs',
        appCode: 'APP_CUSTOMER_360',
        // callbackGetInfoAccount: value => console.log('🚀 ~ callbackGetInfoAccount ~ value:: ', value),
        // callbackLogout: () => console.log('🚀 ~ callbackLogout ~ value:: '),
      },
    },
    leftMenuProps: {
      token: '',
      userId: '',
    },
  },
  parameters: {
    docs: {
      description: {
        component: 'A Preview Template Modal. \n',
      },
    },
  },
} as Meta<typeof Layout>;

// const Template: StoryFn<typeof Layout> = args => <Layout {...args}>Workspace</Layout>;

// export const Default = {
//   render: Template,
//   args: {},
// };

export const BasicUsage: StoryObj<any> = {
  render: () => {
    const appConfig: AppConfigProviderProps = {
      env: 'development',
      auth: {
        token: '5474r2x214z2a4a4u254y446n474q266s4k4k4e4l5u5',
        portalId: 33167,
        userId: '**********',
        accountId: '**********',
      },
      languageCode: 'en',
    };

    const setLayoutState = useLayoutStore(store => store.setLayoutState);
    const setLeftMenuState = useLayoutStore(store => store.setLeftMenuState);
    const resetLeftMenuState = useLayoutStore(store => store.resetLeftMenuState);
    const showLeftMenu = useLayoutStore(store => store.state.leftMenu.show);

    const [loading, setLoading] = useState<boolean>(false);
    const [templateQuantity, setTemplateQuantity] = useState<number>(10);
    const [checkedCategories, setCheckedCategories] = useState<TCheckedCategories>({});
    const { navigate } = useCustomRouter();
    const location = useLocation();
    console.log({ location });
    const templateItems: TTemplateItem[] = [];
    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;
    for (let i = 1; i <= templateQuantity; i++) {
      templateItems.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }

    const rightContent = (
      <Flex gap={10} style={{ marginRight: '20px' }}>
        <Button type="default">Refresh</Button>
        <Button type="default">Share</Button>
        <Button type="primary">Save</Button>
      </Flex>
    );

    const handleToggleLeftMenu = () => {
      setLeftMenuState({
        show: !showLeftMenu,
      });
    };

    useEffect(
      () =>
        navigate(
          getGeneratePath('/gen2/:networkId/dashboard/recommendation' || '', {
            ...appConfig.auth,
          }),
        ),
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [],
    );

    const handleLoadMoreTemplates = () => {
      setLoading(true);
      setTimeout(() => {
        setTemplateQuantity(prev => prev + 4);
        setLoading(false);
      }, 1000);
    };

    const onMenuChange = useCallback(
      (value: TCheckedCategories) => {
        setCheckedCategories(value);
      },
      [setCheckedCategories],
    );

    return (
      <AppConfigProvider value={appConfig}>
        <Button type="primary" onClick={handleToggleLeftMenu} style={{ marginBottom: '15px' }}>
          {`${showLeftMenu ? 'Hide' : 'Show'} left menu`}
        </Button>
        <Layout
          headerProps={{ accountSelection: { show: true, currentAccount: '**********' } }}
          workspaceProps={{ workspaceContentProps: { style: { overflow: 'hidden' } } }}
          leftMenuProps={
            {
              // show: false,
            }
          }
        >
          <TemplateListing
            templatesProps={{
              items: templateItems,
              loading,
              onLoadMoreTemplates: handleLoadMoreTemplates,
            }}
            categoryListingProps={{ items: categoryItems, checkedCategories, onMenuChange }}
            previewModalProps={{ open: false }}
          />
        </Layout>
      </AppConfigProvider>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Layout in localhost.',
      },
    },
  },
};

export const ShowOnlyContent: StoryObj<any> = {
  render: () => {
    const appConfig: AppConfigProviderProps = {
      env: 'development',
      auth: {
        token: '5474r2x214z284d4w2b4y474945414j5h4h494t4z5n5',
        portalId: 33167,
        userId: '**********',
        accountId: '**********',
      },
      languageCode: 'en',
    };

    const setLayoutState = useLayoutStore(store => store.setLayoutState);
    const resetLayoutState = useLayoutStore(store => store.resetLayoutState);
    const [loading, setLoading] = useState<boolean>(false);
    const [templateQuantity, setTemplateQuantity] = useState<number>(10);
    const [checkedCategories, setCheckedCategories] = useState<TCheckedCategories>({});
    const templateItems: TTemplateItem[] = [];
    const categoryItems: TCategoryItem[] = MENU_ITEMS_SAMPLE;
    for (let i = 1; i <= templateQuantity; i++) {
      templateItems.push({
        id: `template-${i}`,
        name: `template ${i}`,
        thumbnail: THUMBNAIL_URL,
      });
    }

    const handleLoadMoreTemplates = () => {
      setLoading(true);
      setTimeout(() => {
        setTemplateQuantity(prev => prev + 4);
        setLoading(false);
      }, 1000);
    };

    const onMenuChange = useCallback(
      (value: TCheckedCategories) => {
        setCheckedCategories(value);
      },
      [setCheckedCategories],
    );

    return (
      <AppConfigProvider value={appConfig}>
        <Layout onlyContent>
          <TemplateListing
            templatesProps={{
              items: templateItems,
              loading,
              onLoadMoreTemplates: handleLoadMoreTemplates,
            }}
            categoryListingProps={{ items: categoryItems, checkedCategories, onMenuChange }}
            previewModalProps={{ open: false }}
          />
        </Layout>
      </AppConfigProvider>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Layout show only content',
      },
    },
  },
};
