// Libraries
import React, {
  memo,
  PropsWithChildren,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useContextSelector } from 'use-context-selector';
import AntsProcessingNotification from '@antscorp/processing-notification';
import { cloneDeep, get, isString, merge, omit } from 'lodash';
import isEqual from 'react-fast-compare';
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';
import { Helmet } from 'react-helmet';
import { useCookies } from 'react-cookie';
import dayjs from 'dayjs';
import html2canvas from 'html2canvas';
import { useLocation, useParams } from 'react-router-dom';

// Styled
import {
  ChildrenWrapper,
  ContentWrapper,
  LayoutLoading,
  LayoutWrapper,
  NotificationWrapper,
} from './styled';

// Components
import { Flex } from '@antscorp/antsomi-ui/es/components/atoms/Flex';
import { HeaderV2 } from '@antscorp/antsomi-ui/es/components/molecules/HeaderV2';
import type { HeaderV2Props } from '@antscorp/antsomi-ui/es/components/molecules/HeaderV2';
import { AccessDenied } from '@antscorp/antsomi-ui/es/components/molecules/AccessDenied';
import { LeftMenu } from '@antscorp/antsomi-ui/es/components/organism/LeftMenu';
import type { LeftMenuProps } from '@antscorp/antsomi-ui/es/components/organism/LeftMenu';

// Contexts
import { AppConfigContext } from '@antscorp/antsomi-ui/es/providers/AppConfigProvider';

// Constants
import {
  APP_CACHE_PARAMS,
  APP_CODES,
  APP_IAM_DOMAIN,
  APP_PERMISSION_DOMAIN,
  CDP_ROUTE,
  IAM_API,
  PERMISSION_API,
  PORTALS_IDS,
  SOCKET_API,
  UOGS_PREFIX,
} from '../../../constants';
import { ENV } from '@antscorp/antsomi-ui/es/config';

// Css
import '@antscorp/processing-notification/dist/index.css';
import { useLayoutStore } from './stores';

// Types
import { ProcessingNotificationProps } from '@antscorp/antsomi-ui/es/types';
import { FeatureMenuPermission, TFeatureMenu } from '@antscorp/antsomi-ui/es/models/LeftMenu';
import { ContentWrapperProps, WorkspaceProps } from './types';

// Utils
import { hasSelectAccountPermission } from './utils';
import {
  useCustomRouter,
  useDeepCompareEffect,
  useDeepCompareMemo,
  useMutationObserver,
} from '@antscorp/antsomi-ui/es/hooks';
import { HOME_MENU_ITEMS } from '../../organism/LeftMenu/constants';
import { RecommendationWorkspace } from './components';
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';
import { RequestAccess } from '../../molecules';
import { permissionService } from '@antscorp/antsomi-ui/es/services/Permission';
import { handleError } from '@antscorp/antsomi-ui/es/utils';
import { translate, translations as translationsLocales } from '@antscorp/antsomi-locales';

const { DATAFLOWS, APP_ANTALYSER } = APP_CODES;

export interface LayoutProps {
  onlyContent?: boolean;
  headerProps?: Partial<
    Omit<HeaderV2Props, 'helpConfig'> & { helpConfig: Partial<HeaderV2Props['helpConfig']> }
  >;
  leftMenuProps?: LeftMenuProps;
  processingNotificationProps?: Partial<ProcessingNotificationProps>;
  contentWrapperProps?: ContentWrapperProps;
  workspaceProps?: WorkspaceProps;
  onActiveMenuChange?: (menuActive?: string) => void;
}

export const Layout: React.FC<PropsWithChildren<LayoutProps>> = memo(
  props => {
    const {
      leftMenuProps,
      headerProps = {},
      workspaceProps,
      contentWrapperProps,
      processingNotificationProps,
      children,
      onActiveMenuChange,
    } = props;
    const { workspaceContentProps, ...restOfWorkspaceProps } = workspaceProps || {};

    // Hooks
    const appConfig = useContextSelector(AppConfigContext, state => state);
    const {
      auth,
      languageCode,
      appCode,
      permissionDomain: configPermissionDomain,
      iamDomain: configIamDomain,
      env,
    } = appConfig;
    const { token, userId, portalId, accountId } = auth || {};
    const leftMenu = useLayoutStore(store => store.state.leftMenu);
    const header = useLayoutStore(store => store.state.header);
    const contentWrapper = useLayoutStore(store => store.state.contentWrapper);
    const workspace = useLayoutStore(store => store.state.workspace);
    const onlyContent = useLayoutStore(store => store.state.onlyContent);
    const [, , removeCookie] = useCookies();
    const params = useParams();
    const location = useLocation();
    const { navigate, replace } = useCustomRouter();

    const [showAccountSelection, setShowAccountSelection] = useState<boolean>(false);
    const [noSpaceTopContent, setNoSpaceTopContent] = useState<boolean>(false);
    const [activeMenu, setActiveMenu] = useState<TFeatureMenu | null>(null);
    const [activePageTitle, setActivePageTitle] = useState<string>('');
    const [isAccessDenied, setIsAccessDenied] = useState<boolean>(false);
    const [isLoadingLayout, setLoadingLayout] = useState<boolean>(true);
    const [isIpDenied, setIsIpDenied] = useState<boolean>(false);

    const notificationWrapperRef = useRef<HTMLDivElement>(null);
    const timeoutLoadingLayout = useRef<any>(null);

    useMutationObserver(
      () => {
        if (notificationWrapperRef.current) {
          const child = notificationWrapperRef.current.querySelector('div');
          setNoSpaceTopContent(!!child);
        }
      },
      notificationWrapperRef,
      {
        attributes: true,
        childList: true,
        subtree: true,
        characterData: true,
        attributeOldValue: true,
        characterDataOldValue: true,
      },
    );

    const checkIpRestriction = useCallback(async () => {
      try {
        const hasAccess = await permissionService.checkIpAccess({
          token,
          userId,
          accountId,
          domain: configPermissionDomain || PERMISSION_API,
        });

        // const hasAccess = res.data?.data?.hasAccess;
        if (!hasAccess && !isIpDenied) setIsIpDenied(true);
        if (hasAccess && isIpDenied) setIsIpDenied(false);

        return hasAccess;
      } catch (error) {
        handleError(error);
      }
    }, [accountId, configPermissionDomain, isIpDenied, token, userId]);

    useEffect(() => {
      checkIpRestriction();
      const intervalId = setInterval(
        () => {
          checkIpRestriction();
        },
        5 * 60 * 1000,
      );

      return () => {
        clearInterval(intervalId);
      };
    }, [checkIpRestriction]);

    // Memo
    const showOnlyContent = useDeepCompareMemo(
      () => props.onlyContent ?? onlyContent,
      [onlyContent, props.onlyContent],
    );

    const permissionDomain = useDeepCompareMemo(() => {
      /**
       * Priority for permission domain
       */
      if (configPermissionDomain) {
        return configPermissionDomain;
      }

      /**
       * Check if appCode not exist and portalId is SANDBOX
       * then return production permission api
       */
      if (!appCode || portalId === PORTALS_IDS.SANDBOX) return PERMISSION_API;

      switch (appCode) {
        case DATAFLOWS:
        case APP_ANTALYSER:
          return APP_PERMISSION_DOMAIN[appCode]?.[env || 'development'] || PERMISSION_API;

        default:
          return PERMISSION_API;
      }
    }, [appCode, configPermissionDomain, env, portalId]);

    const iamDomain = useMemo(() => {
      if (configIamDomain) return configIamDomain;

      /**
       * Check if appCode not exist and portalId is SANDBOX
       * then return production iam api
       */
      if (!appCode || portalId === PORTALS_IDS.SANDBOX) return IAM_API;

      switch (appCode) {
        case DATAFLOWS:
        case APP_ANTALYSER:
          return APP_IAM_DOMAIN[appCode]?.[env || 'development'] || IAM_API;

        default:
          return IAM_API;
      }
    }, [appCode, configIamDomain, env, portalId]);

    const handleRequestAccessIP = async message => {
      await permissionService.requestAccess({
        domain: iamDomain,
        accountId,
        token,
        userId,
        message,
      });
    };

    const initialHeaderProps = useDeepCompareMemo(
      () => ({
        className: 'layout-header',
        useURLParam: true,
        ...headerProps,
        config: {
          ...headerProps?.config,
          permissionDomain,
          iamDomain,
        },
        helpConfig: {
          ...headerProps?.helpConfig,
          configs: {
            appCode: 'SANDBOX_MARKETING',
            avatar: '//c0-platform.ants.tech/avatar/2021/09/17/0xgbkurioo.png',
            config: {
              p_timezone: 'Asia/Singapore',
              api_pid: 33167,
              p_f_longdatetime: "dd MMMM 'at' HH:mm:ss",
              embeddedData: {},
              INSIGHT_U_OGS: 'uogs',
            },
            ...headerProps?.helpConfig?.configs,
          },
        },
        accountSharingConfig: {
          ...headerProps?.accountSharingConfig,
          u_ogs: 'uogs',
          appCode: appCode || 'APP_CUSTOMER_360',
        },
        accountSelection: {
          ...headerProps?.accountSelection,
          show: !!headerProps?.accountSelection?.show && showAccountSelection,
        },
      }),
      [headerProps, iamDomain, permissionDomain, showAccountSelection],
    );

    const mergeHeaderProps = useDeepCompareMemo(
      () =>
        merge(cloneDeep(initialHeaderProps), {
          ...header,
          pageTitle: header?.pageTitle || activePageTitle,
        }),
      [initialHeaderProps, header, activePageTitle],
    );

    const {
      noPadding,
      showNotification = true,
      ...mergeContentWrapperProps
    } = useDeepCompareMemo(
      () => merge(cloneDeep(contentWrapperProps), cloneDeep(contentWrapper)),
      [contentWrapperProps, contentWrapper],
    );

    const mergeWorkSpaceProps = useDeepCompareMemo(
      () => merge(cloneDeep(restOfWorkspaceProps), omit(workspace, 'workspaceContentProps')),
      [restOfWorkspaceProps, workspace],
    );

    const {
      className: leftMenuClassName,
      show: showLeftMenu = true,
      ...mergeLeftMenuProps
    } = useDeepCompareMemo(
      () => merge(cloneDeep(leftMenuProps), cloneDeep(leftMenu)),
      [leftMenuProps, leftMenu, appConfig],
    );

    const leftMenuAppConfig = useDeepCompareMemo(
      () => omit({ ...appConfig, permissionDomain }, ['setAppConfig']),
      [appConfig],
    );

    const antsProcessingNotificationConfig = useMemo(
      () => ({
        permissionDomain,
        socketDomain: SOCKET_API,
        token,
        // accountId: userId,
        accountId,
        userId,
        lang: languageCode,
        networkId: portalId,
        ...processingNotificationProps,
      }),
      [permissionDomain, token, userId, languageCode, portalId, processingNotificationProps],
    );

    const onActiveMenuCodeChange = useCallback(
      (
        activeItemPath: TFeatureMenu[],
        flattenPermissionList?: FeatureMenuPermission[],
        menuListPermission?: FeatureMenuPermission[],
      ) => {
        // Set menuActiveArr for CDP app to tracking
        if (window && window[APP_CACHE_PARAMS] && activeItemPath?.length) {
          const menuActiveArr = activeItemPath.map((item, index) => ({
            code: item.menu_item,
            label: item.menu_item_name,
            /** In case index equal 0 (App level 1) then not set permission code */
            permission_code: index !== 0 ? item.permission_code : null,
          }));

          window[APP_CACHE_PARAMS].menuActiveArr = menuActiveArr;
        }

        const activeMenuItem = activeItemPath?.[activeItemPath?.length - 1];
        const { menu_item_code, page_title, menu_item_name, permission_code } =
          activeMenuItem || {};
        setActivePageTitle(
          page_title
            ? i18nInstance.t(translations.leftMenu.pageTitle?.[`${page_title}`]).toString()
            : menu_item_name,
        );
        setActiveMenu(activeMenuItem);
        onActiveMenuChange?.(menu_item_code);

        // console.log({
        //   activeItemPath: cloneDeep(activeItemPath),
        //   flattenPermissionList,
        //   menuListPermission,
        //   appItem: activeItemPath[0],
        // });

        const hasRole = hasSelectAccountPermission({
          activeItemPath: cloneDeep(activeItemPath),
          flattenPermissionList,
          menuListPermission,
          appItem: activeItemPath[0],
        });
        setShowAccountSelection(hasRole);

        // Check if the user has permission or not (access denied)
        if (
          !!permission_code &&
          !!activeMenuItem &&
          Array.isArray(flattenPermissionList) &&
          !!flattenPermissionList.length
        ) {
          const isAccessDenied = !flattenPermissionList.some(
            item => item.menu_code === activeMenuItem.permission_code,
          );

          setIsAccessDenied(isAccessDenied);
        }

        // Handle set loading layout
        if (timeoutLoadingLayout) {
          clearTimeout(timeoutLoadingLayout.current);
        }

        timeoutLoadingLayout.current = setTimeout(() => {
          if (isLoadingLayout) {
            setLoadingLayout(false);
          }
        }, 500);
      },
      [isLoadingLayout, onActiveMenuChange],
    );

    // Effects
    /** Handle replace current userId in params if current userId is not match */
    useDeepCompareEffect(() => {
      const { userId: userIdParam } = (params || {}) as Record<string, string>;

      if (userIdParam && accountId && +userIdParam !== +accountId && location.pathname) {
        replace(`${location.pathname.replace(userIdParam, `${accountId}`)}${location.search}`);
      }
    }, [params, location.pathname, accountId, replace]);

    /** If left menu props has customization then set layout loading to false, because we don't need check permission here */
    useDeepCompareEffect(() => {
      if (mergeLeftMenuProps.customization) {
        setLoadingLayout(false);
      }
    }, [mergeLeftMenuProps.customization]);

    /** Timeout for set layout loading off in case left menu no callback onActiveMenuCodeChange */
    useDeepCompareEffect(() => {
      setTimeout(() => {
        setLoadingLayout(false);
      }, 5000);
    }, []);

    // Handle access denied
    const onClickSwitchAccount = useCallback(() => {
      // Remove cookie when switch account
      removeCookie(`${UOGS_PREFIX}${portalId}`, {
        ...(env !== ENV.DEV && {
          domain: `.${origin.split('.').splice(-2).join('.')}`,
        }),
        path: '/',
        sameSite: 'none',
        secure: true,
      });

      // Redirect to login page
      navigate('/login');
    }, [env, portalId, navigate, removeCookie]);

    const onClickRequestAccess = useCallback(async () => {
      const canvas = await html2canvas(document.body, {
        allowTaint: true,
        useCORS: true,
        scale: 2,
        scrollX: 0,
        scrollY: 0,
        imageTimeout: 5000,
      });

      const dataUrl = canvas.toDataURL('image/png');

      const messageContent = {
        type: 'open_antsomi_help',
        helpType: 'ISSUE',
        helpInfo: {
          feature: 'other_feature',
          title: `Request Access to ${activeMenu?.menu_item_name}`,
          message: `
          I want to be granted permission to access this feature. <br /> <br />

          Details:
          <ul>
            <li>Portal ID: ${portalId}</li>
            <li>User ID: ${userId}</li>
            <li>Link: ${window.location.href}</li>
            <li>Datetime: ${dayjs().format('MMM DD, YYYY hh:mm:ss.SSS A')}</li>
          </ul>
          `,
          files: [],
          referenceUrl: '',
        },
        dataImage: {
          imageName: 'request-access.png',
          dataUrl,
        },
      };

      window.postMessage(messageContent);
    }, [activeMenu?.menu_item_name, portalId, userId]);

    // Handle callback processing notification
    const handleCallbackNotify = useCallback(
      (type, data) => {
        // If has callback from props then call it and return
        if (antsProcessingNotificationConfig.callback) {
          return antsProcessingNotificationConfig.callback(type, data);
        }

        switch (type) {
          case 'forecast_name': {
            const cdpDomain = CDP_ROUTE[env || 'development'];
            const portalId = get(data, 'row.network_id', '');
            const notificationId = get(data, 'row._id', '');

            if (cdpDomain && portalId && notificationId) {
              const searchParams = new URLSearchParams();

              searchParams.set('use', 'forecast-segment');
              searchParams.set('notification-id', notificationId);

              const url = `${cdpDomain}/gen2/${portalId}/profile/segments?${searchParams.toString()}`;

              window.open(url, '_blank', 'noopener,noreferrer');
            } else {
              throw new Error('CDP domain or portal id or notification id is not correct');
            }
            break;
          }
          default: {
            break;
          }
        }
      },
      [antsProcessingNotificationConfig, env],
    );

    const onCLickLogo = useCallback(() => {
      // Redirect to recommendation
      if (env !== ENV.DEV) {
        window.location.assign(
          `${CDP_ROUTE[env || 'development']}/gen2/${portalId}/dashboard/recommendation`,
        );
      }
    }, [env, portalId]);

    return (
      <LayoutWrapper showLeftMenu={!!showLeftMenu}>
        <Helmet>
          <meta charSet="utf-8" />
          <title>
            {header?.browserTitle ||
              (header?.pageTitle
                ? isString(header?.pageTitle)
                  ? header?.pageTitle
                  : ''
                : activePageTitle)}
          </title>
        </Helmet>
        {isIpDenied ? (
          <RequestAccess
            fullscreen
            sentTitle={translate(translationsLocales._USER_GUIDE_IP_RESTRICTION_REQUEST_SENT)}
            sentSubTitle={translate(
              translationsLocales._USER_GUIDE_IP_RESTRICTION_REQUEST_SENT_INFO,
            )}
            deniedTitle={translate(translationsLocales._USER_GUIDE_IP_ADDRESS_DENIED)}
            onRequest={handleRequestAccessIP}
            onSwitchAccount={onClickSwitchAccount}
          />
        ) : (
          <>
            <HeaderV2
              {...mergeHeaderProps}
              show={!showOnlyContent}
              showLogo={!showLeftMenu}
              onCLickLogo={onCLickLogo}
            />
            <Flex className="layout-body">
              <LeftMenu
                show={showLeftMenu && !showOnlyContent}
                className={`layout-body__menu ${leftMenuClassName}`}
                {...mergeLeftMenuProps}
                customization={{
                  ...mergeLeftMenuProps.customization,
                  // If user has access denied, disable expandable
                  showButtonExpand:
                    !isAccessDenied && mergeLeftMenuProps.customization?.showButtonExpand,
                }}
                appConfig={leftMenuAppConfig}
                onActiveMenuCodeChange={onActiveMenuCodeChange}
              />

              <ContentWrapper
                $noPadding={noPadding}
                $noSpaceTopContent={noSpaceTopContent}
                $showOnlyContent={showOnlyContent}
                {...mergeContentWrapperProps}
              >
                {activeMenu?.menu_item_code === HOME_MENU_ITEMS.RECOMMENDATION ? (
                  <div className="layout-body__content">
                    <RecommendationWorkspace />
                  </div>
                ) : (
                  <div
                    className={`layout-body__content ${workspaceProps?.className || ''}`}
                    {...mergeWorkSpaceProps}
                  >
                    {isLoadingLayout ? (
                      <LayoutLoading spinning />
                    ) : (
                      <>
                        <NotificationWrapper
                          className="process-notification"
                          ref={notificationWrapperRef}
                        >
                          {showNotification && (
                            <AntsProcessingNotification
                              {...antsProcessingNotificationConfig}
                              callback={handleCallbackNotify}
                            />
                          )}
                        </NotificationWrapper>
                        <ChildrenWrapper {...workspaceContentProps}>
                          {isAccessDenied ? (
                            <AccessDenied
                              onClickRequestAccess={onClickRequestAccess}
                              onClickSwitchAccount={onClickSwitchAccount}
                            />
                          ) : (
                            <div>{children}</div>
                          )}
                        </ChildrenWrapper>
                      </>
                    )}
                  </div>
                )}
              </ContentWrapper>
            </Flex>
          </>
        )}
      </LayoutWrapper>
    );
  },
  (prevProps, nextProps) => isEqual(prevProps, nextProps),
);

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace JSX {
    interface IntrinsicElements {
      'ants-processing-notification': any;
    }
  }
}
