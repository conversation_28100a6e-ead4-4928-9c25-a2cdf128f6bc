// Libraries
import { fireEvent, render } from '@testing-library/react';

// Components
import { Radio } from '..';
import { RenderDisabled, RenderRadioGroup, RenderRadioGroupOptional, Size } from '../Radio.stories';

describe('Radio Component', () => {
  it('should render the Radio Component', () => {
    const { container } = render(<Radio />);
    expect(container).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  describe('Default Radio Component', () => {
    it('should render the Default Radio Component', () => {
      const args = {};
      const { container } = render(<Radio {...args} />);

      expect(container).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  describe('Basic Radio Component', () => {
    it('should render the Basic Radio Component', () => {
      const { container } = render(<Radio>Radio</Radio>);
      expect(container).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });

  describe('Disabled Radio Component', () => {
    it('should render the Disabled Radio Component', () => {
      const { container } = render(<RenderDisabled />);

      expect(container).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });

    it('toggles disabled state on button click', () => {
      const { getByRole } = render(<RenderDisabled />);
      const toggleButton = getByRole('button');
      fireEvent.click(toggleButton);
    });
  });

  describe('Radio Group Component', () => {
    it('should render the Radio Group Component', () => {
      const { container } = render(<RenderRadioGroup />);
      expect(container).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });

    it('should call funciton when RadioGroup onChange event', () => {
      const { getAllByRole } = render(<RenderRadioGroup />);

      const radio = getAllByRole('radio')[1];
      fireEvent.click(radio);

      expect(radio).toBeChecked();
    });
  });

  describe('Radio Group Optional Component', () => {
    it('should render the Radio Group Optional Component', () => {
      const { container } = render(<RenderRadioGroupOptional />);

      expect(container).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });

    it('should trigger onChange when a Radio Group Optional is changed', () => {
      const { getAllByLabelText } = render(<RenderRadioGroupOptional />);

      const radios = getAllByLabelText('Pear');

      radios.forEach(radio => {
        fireEvent.click(radio);
        expect(radio).toBeChecked();
      });
    });
  });

  describe('Size Radio Component', () => {
    it('should render the Size Radio Component', () => {
      const { container } = render(Size.render?.call(this));
      expect(container).toBeInTheDocument();
      expect(container).toMatchSnapshot();
    });
  });
});
