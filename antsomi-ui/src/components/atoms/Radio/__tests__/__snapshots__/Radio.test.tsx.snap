// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Radio Component > Basic Radio Component > should render the Basic Radio Component 1`] = `
.c0 {
  font-family: 'Roboto';
  color: #222222;
  font-size: 12px;
}

.c0 .antsomi-radio .antsomi-radio-inner {
  border-width: 2px;
  border-color: #005fb8;
}

.c0 .antsomi-radio .antsomi-radio-inner::after {
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  background-color: #005fb8;
}

<div>
  <label
    class="ant-radio-wrapper c0 css-dev-only-do-not-override-ht6oo7"
  >
    <span
      class="ant-radio ant-wave-target"
    >
      <input
        class="ant-radio-input"
        type="radio"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span>
      Radio
    </span>
  </label>
</div>
`;

exports[`Radio Component > Default Radio Component > should render the Default Radio Component 1`] = `
.c0 {
  font-family: 'Roboto';
  color: #222222;
  font-size: 12px;
}

.c0 .antsomi-radio .antsomi-radio-inner {
  border-width: 2px;
  border-color: #005fb8;
}

.c0 .antsomi-radio .antsomi-radio-inner::after {
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  background-color: #005fb8;
}

<div>
  <label
    class="ant-radio-wrapper c0 css-dev-only-do-not-override-ht6oo7"
  >
    <span
      class="ant-radio ant-wave-target"
    >
      <input
        class="ant-radio-input"
        type="radio"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
  </label>
</div>
`;

exports[`Radio Component > Disabled Radio Component > should render the Disabled Radio Component 1`] = `
<div>
  <label
    class="ant-radio-wrapper ant-radio-wrapper-disabled css-dev-only-do-not-override-ht6oo7"
  >
    <span
      class="ant-radio ant-wave-target ant-radio-disabled"
    >
      <input
        class="ant-radio-input"
        disabled=""
        type="radio"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span>
      Disabled
    </span>
  </label>
  <label
    class="ant-radio-wrapper ant-radio-wrapper-disabled css-dev-only-do-not-override-ht6oo7"
  >
    <span
      class="ant-radio ant-wave-target ant-radio-checked ant-radio-disabled"
    >
      <input
        checked=""
        class="ant-radio-input"
        disabled=""
        type="radio"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
    <span>
      Disabled
    </span>
  </label>
  <br />
  <button
    class="ant-btn css-dev-only-do-not-override-ht6oo7 ant-btn-primary"
    style="margin-top: 16px;"
    type="button"
  >
    <span>
      Toggle disabled
    </span>
  </button>
</div>
`;

exports[`Radio Component > Radio Group Component > should render the Radio Group Component 1`] = `
<div>
  <div
    class="ant-radio-group ant-radio-group-outline css-dev-only-do-not-override-ht6oo7"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          type="radio"
          value="1"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span>
        A
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          type="radio"
          value="2"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span>
        B
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          type="radio"
          value="3"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span>
        C
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          type="radio"
          value="4"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span>
        D
      </span>
    </label>
  </div>
</div>
`;

exports[`Radio Component > Radio Group Optional Component > should render the Radio Group Optional Component 1`] = `
<div>
  <div
    class="ant-radio-group ant-radio-group-outline css-dev-only-do-not-override-ht6oo7"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          type="radio"
          value="Apple"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span>
        Apple
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          type="radio"
          value="Pear"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span>
        Pear
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          type="radio"
          value="Orange"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span>
        Orange
      </span>
    </label>
  </div>
  <br />
  <div
    class="ant-radio-group ant-radio-group-outline css-dev-only-do-not-override-ht6oo7"
  >
    <label
      class="ant-radio-wrapper ant-radio-wrapper-checked css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-checked"
      >
        <input
          checked=""
          class="ant-radio-input"
          type="radio"
          value="Apple"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span>
        Apple
      </span>
    </label>
    <label
      class="ant-radio-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio ant-wave-target"
      >
        <input
          class="ant-radio-input"
          type="radio"
          value="Pear"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span>
        Pear
      </span>
    </label>
    <label
      class="ant-radio-wrapper ant-radio-wrapper-disabled css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio ant-wave-target ant-radio-disabled"
      >
        <input
          class="ant-radio-input"
          disabled=""
          type="radio"
          value="Orange"
        />
        <span
          class="ant-radio-inner"
        />
      </span>
      <span>
        Orange
      </span>
    </label>
  </div>
  <br />
  <br />
  <div
    class="ant-radio-group ant-radio-group-outline css-dev-only-do-not-override-ht6oo7"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          type="radio"
          value="Apple"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Apple
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          type="radio"
          value="Pear"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Pear
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          type="radio"
          value="Orange"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Orange
      </span>
    </label>
  </div>
  <br />
  <br />
  <div
    class="ant-radio-group ant-radio-group-solid css-dev-only-do-not-override-ht6oo7"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          type="radio"
          value="Apple"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Apple
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          type="radio"
          value="Pear"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Pear
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-disabled css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button ant-radio-button-disabled"
      >
        <input
          class="ant-radio-button-input"
          disabled=""
          type="radio"
          value="Orange"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Orange
      </span>
    </label>
  </div>
</div>
`;

exports[`Radio Component > Size Radio Component > should render the Size Radio Component 1`] = `
<div>
  <div
    class="ant-radio-group ant-radio-group-outline ant-radio-group-large css-dev-only-do-not-override-ht6oo7"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          type="radio"
          value="a"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Hangzhou
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          type="radio"
          value="b"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Shanghai
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          type="radio"
          value="c"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Beijing
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          type="radio"
          value="d"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Chengdu
      </span>
    </label>
  </div>
  <br />
  <div
    class="ant-radio-group ant-radio-group-outline css-dev-only-do-not-override-ht6oo7"
    style="margin-top: 16px;"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          type="radio"
          value="a"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Hangzhou
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          type="radio"
          value="b"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Shanghai
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          type="radio"
          value="c"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Beijing
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          type="radio"
          value="d"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Chengdu
      </span>
    </label>
  </div>
  <br />
  <div
    class="ant-radio-group ant-radio-group-outline ant-radio-group-small css-dev-only-do-not-override-ht6oo7"
    style="margin-top: 16px;"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          type="radio"
          value="a"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Hangzhou
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          type="radio"
          value="b"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Shanghai
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          type="radio"
          value="c"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Beijing
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper css-dev-only-do-not-override-ht6oo7"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          type="radio"
          value="d"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span>
        Chengdu
      </span>
    </label>
  </div>
</div>
`;

exports[`Radio Component > should render the Radio Component 1`] = `
.c0 {
  font-family: 'Roboto';
  color: #222222;
  font-size: 12px;
}

.c0 .antsomi-radio .antsomi-radio-inner {
  border-width: 2px;
  border-color: #005fb8;
}

.c0 .antsomi-radio .antsomi-radio-inner::after {
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  background-color: #005fb8;
}

<div>
  <label
    class="ant-radio-wrapper c0 css-dev-only-do-not-override-ht6oo7"
  >
    <span
      class="ant-radio ant-wave-target"
    >
      <input
        class="ant-radio-input"
        type="radio"
      />
      <span
        class="ant-radio-inner"
      />
    </span>
  </label>
</div>
`;
