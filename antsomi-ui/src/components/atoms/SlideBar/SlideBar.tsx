// Libraries
import React, { FC, useRef, useEffect, useState, useMemo } from 'react';
import classNames from 'classnames';
import _ from 'lodash';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';

// Types
import type {
  DroppableProvided,
  DroppableStateSnapshot,
  DraggableProvided,
  DraggableStateSnapshot,
  OnDragEndResponder,
} from 'react-beautiful-dnd';

// Components
import { Icon } from '../Icon';
import { Popover } from '../Popover';

// Styled
import {
  ContainerSlideBar,
  MainContent,
  MenuItem,
  MenuList,
  SlideItem,
  SliderWrapper,
  SlideText,
  WrapperDisable,
  WrapperOverflow,
} from './styled';
import { PlusCircleFilled } from '@ant-design/icons';

// Constants
import { THEME } from '@antscorp/antsomi-ui/es/constants';
import { slideActionType, WIDTH_SLIDE_ITEM } from './constants';
import { EditFilledIcon } from '../../icons';

// Utils
import { getObjectPropSafely, handleError } from '@antscorp/antsomi-ui/es/utils';
import { getOffsetSizeAdd } from './utils';

// Locales
import i18nInstance from '@antscorp/antsomi-ui/es/locales/i18n';
import { translations } from '@antscorp/antsomi-ui/es/locales/translations';

export type SlideBarOptionProps = {
  label: string;
  value: string;
};

export type SlideBarLimitProps = {
  max: number;
  min: number;
};

export type ActionType = (typeof slideActionType)[keyof typeof slideActionType];

export type SlideBarState = {
  cacheInfoClicked: SlideBarOptionProps;
  openPopover: {
    [key in string]: boolean;
  };
  disabledPrev: boolean;
  disabledNext: boolean;
  notiUpdateScroll: number;
};

export interface SlideBarProps {
  size?: 'small' | 'medium';
  isMore?: boolean;
  borderless?: boolean;
  badgeNumber?: boolean;
  isViewMode?: boolean;
  isShowLabelSequentially?: boolean;
  isShowAdd?: boolean;
  isDragDisabled?: boolean;
  disabled?: boolean;
  activeId: string;
  prefix?: string;
  options: SlideBarOptionProps[];
  limit?: SlideBarLimitProps;
  errors: any;
  callback?: (type: ActionType, data?: SlideBarOptionProps | SlideBarOptionProps[]) => any;
}

const getItemStyle = (isDragging: boolean, draggableStyle: any) => ({
  // some basic styles to make the items look a bit nicer
  userSelect: 'none',
  borderLeft: isDragging ? '1px solid #e0e0e0' : 'none',
  // styles we need to apply on draggables
  ...draggableStyle,
});

const PATH = 'src/components/atoms/SlideBar/SlideBar.tsx';

export const SlideBar: FC<SlideBarProps> = props => {
  const {
    size,
    borderless,
    isMore,
    badgeNumber,
    isViewMode,
    isShowLabelSequentially,
    isDragDisabled,
    isShowAdd,
    disabled,
    prefix,
    options,
    activeId,
    limit,
    errors,
    callback,
  } = props;

  // States
  const [state, setState] = useState<SlideBarState>({
    cacheInfoClicked: {
      label: '',
      value: '',
    },
    openPopover: {},
    disabledPrev: true,
    disabledNext: true,
    notiUpdateScroll: 1,
  });

  // Refs
  const wrapperOverflowRef = useRef<HTMLDivElement>(null);

  const disabledAdd = useMemo(
    () => disabled || isViewMode || (limit && options.length >= limit?.max),
    [disabled, isViewMode, limit, options.length],
  );
  const disabledDuplicate = useMemo(
    () => (limit && options.length >= limit?.max) || isViewMode || disabled,
    [disabled, isViewMode, limit, options.length],
  );
  const disabledDelete = useMemo(
    () => (limit && options.length <= limit?.min) || isViewMode || disabled,
    [disabled, isViewMode, limit, options.length],
  );

  // Locales
  const { t } = i18nInstance;

  const handleClick = (event: any, slideInfo: SlideBarOptionProps) => {
    event.stopPropagation();

    setState(prev => ({
      ...prev,
      cacheInfoClicked: slideInfo,
    }));
  };

  const reorder = (
    list: SlideBarOptionProps[],
    startIndex: number,
    endIndex: number,
  ): SlideBarOptionProps[] => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);

    return result;
  };

  const handleDragEnd: OnDragEndResponder = result => {
    try {
      // dropped outside the list
      if (!result.destination) {
        return;
      }

      const items = reorder(options, result.source.index, result.destination.index);

      if (typeof callback === 'function') {
        callback(slideActionType.RE_ORDERS, items as any);
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleDragEnd',
        args: {
          error,
        },
      });
      // eslint-disable-next-line no-console
      console.log('error :>', error);
    }
  };

  const handleActiveSlide = (e: any, slideInfo: SlideBarOptionProps) => {
    try {
      e.stopPropagation();

      if (typeof callback === 'function' && !_.isEmpty(slideInfo) && activeId !== slideInfo.value) {
        callback(slideActionType.ACTIVE, slideInfo);
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleActiveSlide',
        args: {
          error,
          slideInfo,
        },
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const handleClosePopover = () => {
    setState(prev => ({
      ...prev,
      openPopover: {},
    }));
  };

  const handleDuplicateSlide = (slideInfo: SlideBarOptionProps) => {
    try {
      if (typeof callback === 'function' && !_.isEmpty(slideInfo)) {
        callback(slideActionType.DUPLICATE, slideInfo);
      }

      handleClosePopover();
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleDuplicateSlide',
        args: {
          error,
          slideInfo,
        },
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const handleDeleteSlide = (slideInfo: SlideBarOptionProps) => {
    try {
      if (typeof callback === 'function' && !_.isEmpty(slideInfo)) {
        callback(slideActionType.DELETE, slideInfo);
      }

      handleClosePopover();
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleDeleteSlide',
        args: {
          error,
          slideInfo,
        },
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const handleChangeScrollDirection = (type = '') => {
    try {
      if (_.isEmpty(wrapperOverflowRef.current)) return;

      if (type === 'PREVIOUS') {
        if (wrapperOverflowRef.current.scrollLeft < WIDTH_SLIDE_ITEM) {
          wrapperOverflowRef.current.scrollLeft = 0;
        } else {
          wrapperOverflowRef.current.scrollLeft -= WIDTH_SLIDE_ITEM;
        }
      } else if (type === 'NEXT') {
        wrapperOverflowRef.current.scrollLeft += WIDTH_SLIDE_ITEM;
      }

      setState(prev => ({
        ...prev,
        notiUpdateScroll: prev.notiUpdateScroll + 1,
      }));
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleChangePrev',
        args: {
          type,
          error,
        },
      });
      // eslint-disable-next-line no-console
      console.log(error);
    }
  };

  const handleAddSlide = () => {
    try {
      if (typeof callback === 'function') {
        callback(slideActionType.ADD);
      }
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'handleAddSlide',
        args: {
          error,
        },
      });
    }
  };

  const handleOpenChange = (newOpen: boolean, itemId: string) => {
    setState(prev => ({
      ...prev,
      openPopover: {
        [itemId]: newOpen,
      },
    }));
  };

  useEffect(
    () => () => {
      setState(prev => ({
        ...prev,
        cacheInfoClicked: { label: '', value: '' },
      }));
    },
    [],
  );

  // Calculator center selected template
  useEffect(() => {
    const selectedSlideEle = document.getElementById(`template-slide-${activeId}`);

    if (
      selectedSlideEle instanceof HTMLElement &&
      wrapperOverflowRef &&
      wrapperOverflowRef.current
    ) {
      // Calculate the scroll position to center the element
      const elementLeftOffset = selectedSlideEle.offsetLeft;
      const containerWidth = wrapperOverflowRef.current.clientWidth;
      const elementWidth = selectedSlideEle.clientWidth;
      const scrollPosition = elementLeftOffset - (containerWidth - elementWidth) / 2;

      // Set the scroll position of the container
      wrapperOverflowRef.current.scrollLeft = scrollPosition;
    }
  }, [activeId]);

  useEffect(() => {
    if (!_.isEmpty(wrapperOverflowRef.current)) {
      const isDisabledPrevTmp = getObjectPropSafely(
        () => wrapperOverflowRef.current?.scrollLeft === 0,
      );
      const isDisabledNextTmp = getObjectPropSafely(() => {
        const {
          scrollLeft = 0,
          offsetWidth = 0,
          scrollWidth = 0,
        } = wrapperOverflowRef.current || {};

        return offsetWidth === scrollWidth || scrollWidth <= Math.ceil(scrollLeft) + offsetWidth;
      });

      setState(prev => ({
        ...prev,
        disabledPrev: isDisabledPrevTmp,
        disabledNext: isDisabledNextTmp,
      }));
    }
  }, [options, state.notiUpdateScroll]);

  const renderMainContent = () =>
    options.map((item, index) => {
      const isActive = activeId === item.value;
      let { label } = _.cloneDeep(item);

      if (isShowLabelSequentially) {
        label = `${prefix} ${index + 1}`;
      }
      if (badgeNumber) {
        label = [isActive ? prefix : undefined, index + 1].join(' ');
      }

      return (
        <Draggable
          key={item.value}
          isDragDisabled={isViewMode || disabled || isDragDisabled}
          draggableId={item.value}
          index={index}
        >
          {(providedDraggable: DraggableProvided, snapshotDraggable: DraggableStateSnapshot) => (
            <SlideItem
              id={`template-slide-${item.value}`}
              isActive={isActive}
              className={classNames('slide-item', {
                'slide-item--badge-number': badgeNumber,
                'slide-item--active': isActive,
              })}
              ref={providedDraggable.innerRef}
              {...providedDraggable.draggableProps}
              {...providedDraggable.dragHandleProps}
              size={size}
              isSlideError={Array.isArray(errors) && errors.includes(item.value)}
              style={getItemStyle(
                snapshotDraggable.isDragging,
                providedDraggable.draggableProps.style,
              )}
              onClick={(e: any) => {
                e.stopPropagation();
                handleActiveSlide(e, item);
              }}
            >
              <SlideText
                size={size}
                isActive={isActive}
                className={classNames('slide-item__text', {
                  'slide-item__text--circle': badgeNumber && !isActive,
                  'slide-item__text--active': isActive,
                })}
              >
                {badgeNumber && isActive ? (
                  <span className="slide-item__text--icon">
                    <EditFilledIcon size={16} />
                  </span>
                ) : null}
                {label}
              </SlideText>
              {!isViewMode && isMore && (
                <Popover
                  placement="bottomLeft"
                  trigger="click"
                  open={state.openPopover[item.value]}
                  onOpenChange={newOpen => handleOpenChange(newOpen, item.value)}
                  content={
                    <MenuList>
                      <WrapperDisable disabled={disabledDuplicate}>
                        <MenuItem
                          isDisabled={disabledDuplicate}
                          onClick={(e: any) => {
                            e.stopPropagation();

                            if (!disabledDuplicate) {
                              handleDuplicateSlide(state.cacheInfoClicked);
                            }
                          }}
                        >
                          {t(translations.duplicate.title) as string}
                        </MenuItem>
                      </WrapperDisable>
                      <WrapperDisable disabled={disabledDelete}>
                        <MenuItem
                          isDisabled={disabledDelete}
                          onClick={(e: any) => {
                            e.stopPropagation();

                            if (!disabledDelete) {
                              handleDeleteSlide(state.cacheInfoClicked);
                            }
                          }}
                        >
                          {t(translations.delete.title) as string}
                        </MenuItem>
                      </WrapperDisable>
                    </MenuList>
                  }
                  arrow={false}
                >
                  <Icon
                    type="icon-ants-three-dot-vertical"
                    color={
                      activeId === item.value ? THEME.token?.colorPrimary : THEME.token?.colorText
                    }
                    size="20px"
                    onClick={(event: any) => handleClick(event, item)}
                    disabled={false}
                  />
                </Popover>
              )}
            </SlideItem>
          )}
        </Draggable>
      );
    });

  const renderSliderDragDrop = () => (
    <WrapperOverflow ref={wrapperOverflowRef} id="wrapper-overflow">
      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="droppable" direction="horizontal">
          {(providedDroppable: DroppableProvided, _snapshotDroppable: DroppableStateSnapshot) => (
            <MainContent ref={providedDroppable.innerRef} {...providedDroppable.droppableProps}>
              {renderMainContent()}
            </MainContent>
          )}
        </Droppable>
      </DragDropContext>
    </WrapperOverflow>
  );

  return (
    <ContainerSlideBar>
      <SliderWrapper
        size={size}
        isViewMode={isViewMode}
        className={classNames({ 'slider-wrapper--borderless': borderless })}
      >
        <Icon
          type="icon-ants-angle-left"
          style={{ fontSize: '16px', cursor: 'pointer', color: THEME.token?.colorPrimary }}
          className="btn-arrow-left btn-action-slide-template"
          disabled={state.disabledPrev}
          onClick={(e: any) => {
            e.stopPropagation();
            handleChangeScrollDirection('PREVIOUS');
          }}
        />
        {renderSliderDragDrop()}
        <Icon
          type="icon-ants-angle-right"
          style={{ fontSize: '16px', cursor: 'pointer', color: THEME.token?.colorPrimary }}
          className="btn-arrow-right btn-action-slide-template"
          disabled={state.disabledNext}
          onClick={(e: any) => {
            e.stopPropagation();
            handleChangeScrollDirection('NEXT');
          }}
        />
      </SliderWrapper>
      {isShowAdd && (
        <PlusCircleFilled
          style={{
            fontSize: `${getOffsetSizeAdd(size)}px`,
            cursor: disabledAdd ? 'not-allowed' : 'pointer',
            color: disabledAdd ? THEME.token?.colorTextDisabled : THEME.token?.colorPrimary,
          }}
          // disabled={disabledAdd}
          onClick={(e: any) => {
            e.stopPropagation();
            if (!disabledAdd) {
              handleAddSlide();
            }
          }}
        />
      )}
    </ContainerSlideBar>
  );
};

SlideBar.defaultProps = {
  isViewMode: false,
  disabled: false,
  size: 'small',
  borderless: false,
  isMore: true,
  badgeNumber: false,
  isShowLabelSequentially: true,
  isShowAdd: true,
  isDragDisabled: false,
  activeId: '',
  prefix: 'Slide',
  options: [],
};
