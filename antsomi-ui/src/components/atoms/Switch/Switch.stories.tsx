// Libraries
import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>a, StoryObj } from '@storybook/react';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';

// Components
import { Switch } from './Switch';
import { Button, Space } from '../index';

const meta = {
  title: 'Atoms/Switch',
  component: Switch,
  argTypes: {
    autoFocus: {
      name: 'autoFocus',
      defaultValue: false,
      description: 'Whether get focus when component mounted',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
      },
      control: {
        type: 'boolean',
      },
    },
    checked: {
      name: 'checked',
      description: 'Determine whether the Switch is checked	',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
      },
      control: {
        type: 'boolean',
      },
    },
    checkedChildren: {
      name: 'checkedChildren',
      defaultValue: false,
      description: 'The content to be shown when the state is checked	',
      table: {
        type: { summary: 'ReactNode' },
      },
    },
    className: {
      name: 'className',
      defaultValue: false,
      description: 'The additional class to Switch	',
      table: {
        type: { summary: 'string' },
      },
    },
    defaultChecked: {
      name: 'defaultChecked',
      defaultValue: false,
      description: 'Whether to set the initial state	',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false ' },
      },
      control: {
        type: 'boolean',
      },
    },
    disabled: {
      name: 'disabled',
      defaultValue: false,
      description: 'Disable switch	',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
      },
      control: {
        type: 'boolean',
      },
    },
    loading: {
      name: 'loading',
      defaultValue: false,
      description: 'Disable switch	',
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' },
      },
      control: {
        type: 'boolean',
      },
    },
    size: {
      name: 'size',
      defaultValue: 'default',
      description: 'The size of the Switch, options: `default` `small`	',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'default' },
      },
      control: {
        type: 'select',
        options: ['default', 'small'],
      },
    },
    unCheckedChildren: {
      name: 'unCheckedChildren',
      description: 'The content to be shown when the state is unchecked',
      table: {
        type: { summary: 'ReactNode' },
      },
    },
    onChange: {
      name: 'onChange',
      description: 'Trigger when the checked state is changing	',
      table: {
        type: { summary: 'function(checked: boolean, event: Event)	' },
      },
    },
    onClick: {
      name: 'onClick',
      description: 'Trigger when clicked	',
      table: {
        type: { summary: 'function(checked: boolean, event: Event)	' },
      },
    },
  },
  parameters: {
    docs: {
      description: {
        component:
          'Switching Selector.' +
          '\n### When To Use' +
          '\n' +
          '- If you need to represent the switching between two states or on-off state.' +
          '\n' +
          '- The difference between `Switch` and `Checkbox` is that `Switch` will trigger a state change directly when you toggle it, while `Checkbox` is generally used for state marking, which should work in conjunction with submit operation' +
          '\n',
      },
    },
  },
} satisfies Meta<typeof Switch>;

export default meta;

type Story = StoryObj<typeof meta>;

// Default
const Template: StoryFn<typeof Switch> = args => <Switch defaultChecked {...args} />;

export const Default = {
  render: Template,
};

export const Disabled = {
  args: {
    checked: false,
    defaultChecked: false,
  },
  render: () => {
    const [disabled, setDisabled] = useState(true);

    const toggle = () => {
      setDisabled(!disabled);
    };

    return (
      <Space direction="vertical">
        <Switch disabled={disabled} defaultChecked />
        <Button type="primary" onClick={toggle}>
          Toggle disabled
        </Button>
      </Space>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Disabled state of `Switch`.',
      },
    },
  },
};

export const TextIcon = {
  render: () => (
    <Space direction="vertical">
      <Switch checkedChildren="开启" unCheckedChildren="关闭" defaultChecked />
      <Switch checkedChildren="1" unCheckedChildren="0" />
      <Switch
        checkedChildren={<CheckOutlined />}
        unCheckedChildren={<CloseOutlined />}
        defaultChecked
      />
    </Space>
  ),

  parameters: {
    docs: {
      description: {
        story: 'With text and icon.',
      },
    },
  },
};

export const TwoSizes = {
  render: () => (
    <>
      <Switch defaultChecked />
      <br />
      <Switch size="small" defaultChecked />
    </>
  ),

  parameters: {
    docs: {
      description: {
        story: '`size="small"` represents a small sized switch.',
      },
    },
  },
};

export const Loading = {
  render: () => (
    <>
      <Switch loading defaultChecked />
      <br />
      <Switch size="small" loading />
    </>
  ),

  parameters: {
    docs: {
      description: {
        story: 'Mark a pending state of switch.',
      },
    },
  },
};

export const WithColor = {
  args: {
    color: 'red',
  },
} satisfies Story;
