// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Steps > should match snapshot 1`] = `
.c1 {
  border: 1px solid #b8cfe6;
  border-radius: 100%;
  font-size: 14px;
  line-height: 23px;
  color: #005fb8;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot).antsomi-steps-small .antsomi-steps-item .antsomi-steps-item-container .antsomi-steps-item-icon {
  width: 24px;
  height: 24px;
}

.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot) .antsomi-steps-item > .antsomi-steps-item-container > .antsomi-steps-item-icon {
  box-sizing: border-box;
  border-radius: 100%;
  font-size: 14px;
  font-weight: bold;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 32px;
  height: 32px;
}

.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot) .antsomi-steps-item > .antsomi-steps-item-container > .antsomi-steps-item-icon .antsomi-steps-icon {
  color: white;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  line-height: 23px;
  top: 0px;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot) .antsomi-steps-item > .antsomi-steps-item-container > .antsomi-steps-item-content > .antsomi-steps-item-title::after {
  height: 2px;
  background-color: #A2A2A2;
}

.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot) .antsomi-steps-item > .antsomi-steps-item-container > .antsomi-steps-item-tail::after {
  width: 2px;
  background-color: #A2A2A2;
}

.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot) .antsomi-steps-item:not(.antsomi-steps-item-active) > .antsomi-steps-item-container[role='button']:hover .antsomi-steps-item-icon {
  background-color: white;
}

.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot) .antsomi-steps-item:not(.antsomi-steps-item-active) > .antsomi-steps-item-container[role='button']:hover .antsomi-steps-item-content {
  color: #005fb8;
}

.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot) .antsomi-steps-item.highest-step .antsomi-steps-item-container > .antsomi-steps-item-content > .antsomi-steps-item-title::after,
.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot) .antsomi-steps-item.highest-step .antsomi-steps-item-container > .antsomi-steps-item-tail::after {
  background-color: #A2A2A2 !important;
}

.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot) .antsomi-steps-item.antsomi-steps-item-selectable > .antsomi-steps-item-container > .antsomi-steps-item-content .antsomi-steps-item-title:after {
  background-color: #005fb8;
}

.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot) .antsomi-steps-item.antsomi-steps-item-selectable > .antsomi-steps-item-container .antsomi-steps-item-tail::after {
  background-color: #005fb8;
}

.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot) .antsomi-steps-item.antsomi-steps-next-error > .antsomi-steps-item-container > .antsomi-steps-item-content > .antsomi-steps-item-title::after {
  background-color: #EF3340 !important;
}

.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot) .antsomi-steps-item.antsomi-steps-item-error > .antsomi-steps-item-container > .antsomi-steps-item-icon .antsomi-steps-icon {
  color: #FFFFFF !important;
}

.c0.save-highest-step.antsomi-steps:not(.antsomi-steps-dot) .antsomi-steps-item.antsomi-steps-item-error > .antsomi-steps-item-container > .antsomi-steps-item-content .antsomi-steps-item-title {
  color: #ef3340 !important;
}

.c0.save-highest-step.antsomi-steps.antsomi-steps-dot.antsomi-steps-horizontal .antsomi-steps-item .antsomi-steps-item-container > .antsomi-steps-item-tail::after {
  height: 2px;
}

.c0.save-highest-step.antsomi-steps.antsomi-steps-dot.antsomi-steps-vertical .antsomi-steps-item .antsomi-steps-item-container > .antsomi-steps-item-tail::after {
  width: 2px;
}

.c0.save-highest-step.antsomi-steps.antsomi-steps-dot.antsomi-steps-horizontal .antsomi-steps-item-tail::after,
.c0.save-highest-step.antsomi-steps.antsomi-steps-dot.antsomi-steps-vertical .antsomi-steps-item-tail::after {
  background-color: #A2A2A2;
}

.c0.save-highest-step.antsomi-steps.antsomi-steps-dot .antsomi-steps-item-selectable > .antsomi-steps-item-container > .antsomi-steps-item-tail::after {
  background-color: #005fb8;
}

.c0.save-highest-step.antsomi-steps.antsomi-steps-dot .highest-step .antsomi-steps-item-container > .antsomi-steps-item-tail::after {
  background-color: #A2A2A2 !important;
}

.c0.save-highest-step.antsomi-steps .antsomi-steps-item-title {
  font-size: 16px;
  line-height: 32px;
}

.c0.save-highest-step.antsomi-steps .antsomi-steps-item-description {
  font-size: 14px;
}

.c0.save-highest-step.antsomi-steps.antsomi-steps-small .antsomi-steps-item-title {
  font-size: 14px;
  line-height: 24px;
}

.c0.save-highest-step.antsomi-steps .antsomi-steps-item-wait {
  color: #7F7F7F;
}

.c0.save-highest-step.antsomi-steps .antsomi-steps-item-wait > .antsomi-steps-item-container > .antsomi-steps-item-icon {
  background-color: #A2A2A2;
}

.c0.save-highest-step.antsomi-steps .antsomi-steps-item-wait:not(.antsomi-steps-item-error) > .antsomi-steps-item-container > .antsomi-steps-item-content .antsomi-steps-item-title,
.c0.save-highest-step.antsomi-steps .antsomi-steps-item-wait:not(.antsomi-steps-item-error) > .antsomi-steps-item-container > .antsomi-steps-item-content .antsomi-steps-item-description {
  color: inherit;
}

.c0.save-highest-step.antsomi-steps .antsomi-steps-item-process.antsomi-steps-item-active {
  font-weight: bold;
  color: #005fb8;
}

.c0.save-highest-step.antsomi-steps .antsomi-steps-item-process.antsomi-steps-item-active > .antsomi-steps-item-container > .antsomi-steps-item-content > .antsomi-steps-item-title,
.c0.save-highest-step.antsomi-steps .antsomi-steps-item-process.antsomi-steps-item-active > .antsomi-steps-item-container > .antsomi-steps-item-content > .antsomi-steps-item-description {
  color: inherit;
}

.c0.save-highest-step.antsomi-steps .antsomi-steps-item-selectable {
  color: black;
}

.c0.save-highest-step.antsomi-steps .antsomi-steps-item-selectable > .antsomi-steps-item-container > .antsomi-steps-item-icon {
  color: #005fb8;
}

.c0.save-highest-step.antsomi-steps .antsomi-steps-item-selectable > .antsomi-steps-item-container > .antsomi-steps-item-content .antsomi-steps-item-title,
.c0.save-highest-step.antsomi-steps .antsomi-steps-item-selectable > .antsomi-steps-item-container > .antsomi-steps-item-content .antsomi-steps-item-description {
  color: inherit;
}

<div
  class="ant-steps ant-steps-horizontal c0  save-highest-step css-dev-only-do-not-override-15whjk ant-steps-small ant-steps-label-horizontal"
>
  <div
    class="ant-steps-item ant-steps-item-finish antsomi-steps-item-selectable highest-step ant-steps-item-custom ant-steps-item-active"
  >
    <div
      class="ant-steps-item-container"
    >
      <div
        class="ant-steps-item-tail"
      />
      <div
        class="ant-steps-item-icon"
      >
        <span
          class="ant-steps-icon"
        >
          <div
            class="c1"
          >
            1
          </div>
        </span>
      </div>
      <div
        class="ant-steps-item-content"
      >
        <div
          class="ant-steps-item-title"
        >
          Finished
        </div>
        <div
          class="ant-steps-item-description"
        >
          This is description
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps-item ant-steps-item-wait"
  >
    <div
      class="ant-steps-item-container"
    >
      <div
        class="ant-steps-item-tail"
      />
      <div
        class="ant-steps-item-icon"
      >
        <span
          class="ant-steps-icon"
        >
          2
        </span>
      </div>
      <div
        class="ant-steps-item-content"
      >
        <div
          class="ant-steps-item-title"
        >
          In Progress
          <div
            class="ant-steps-item-subtitle"
            title="Left 00:00:08"
          >
            Left 00:00:08
          </div>
        </div>
        <div
          class="ant-steps-item-description"
        >
          This is description
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-steps-item ant-steps-item-wait"
  >
    <div
      class="ant-steps-item-container"
    >
      <div
        class="ant-steps-item-tail"
      />
      <div
        class="ant-steps-item-icon"
      >
        <span
          class="ant-steps-icon"
        >
          3
        </span>
      </div>
      <div
        class="ant-steps-item-content"
      >
        <div
          class="ant-steps-item-title"
        >
          Waiting
        </div>
        <div
          class="ant-steps-item-description"
        >
          This is description
        </div>
      </div>
    </div>
  </div>
</div>
`;
