/* eslint-disable @typescript-eslint/no-unused-expressions */
/* eslint-disable react/destructuring-assignment */
// Libraries
import React, { ChangeEvent, FC, ReactNode, useEffect, useMemo, useRef, useState } from 'react';

// Types
import { InputProps as AntdInputProps, Input as AntdInput, InputRef } from 'antd';

// Components
import { Icon, RequiredLabel, Text } from '@antscorp/antsomi-ui/es/components/atoms';

// Utils
import { handleError } from '@antscorp/antsomi-ui/es/utils';
import { getPreventKeyboardAction } from '@antscorp/antsomi-ui/es/utils/web';
import { StyledIconWrapper, StyledInput } from './styled';
import { globalToken } from '@antscorp/antsomi-ui/es/constants';

// Icons
import { PreviewVisibilityEyeIcon, VisibilityOffEyeIcon } from '../../icons';

const PATH = '@antscorp/antsomi-ui/es/components/atoms/Input/Input.tsx';

export interface InputProps extends AntdInputProps {
  noborder?: 'true' | 'false' | boolean;
  debounce?: number;
  label?: ReactNode;
  onAfterChange?: (value: string) => void;
  errorArchive?: string;
  required?: boolean;
  isReverseMask?: boolean;
  labelColor?: string;
  isHideErrMessage?: boolean;
  focused?: boolean;
  errorMsg?: string;
  disableUndo?: boolean;
  withWrapper?: boolean;
}

const OriginInput = React.forwardRef<InputRef, InputProps>((props, ref) => {
  // Props
  const {
    withWrapper,
    debounce,
    errorArchive,
    required,
    labelColor,
    isReverseMask,
    isHideErrMessage,
    focused,
    label,
    onAfterChange,
    onChange,
    errorMsg,
    ...restProps
  } = props;

  // State
  const [value, setValue] = useState<any>(props.value);
  const [isFocused, setFocused] = useState(false);

  const requiredMsg = useMemo(() => {
    let msg = '';
    const isEmptyValue = !props.value || (Array.isArray(props.value) && !props.value.length);

    if (required && isEmptyValue && isFocused) {
      msg = 'This field is required';
    }

    return msg;
  }, [props.value, required, isFocused]);

  // Ref
  const timeoutAfterChange = useRef<any>(null);

  useEffect(() => {
    setValue(props.value);
  }, [props.value]);

  useEffect(() => {
    if (focused) {
      setFocused(focused);
    }
  }, [focused]);

  const onChangeInput = (event: ChangeEvent<HTMLInputElement>) => {
    try {
      const { value } = event.target;

      setValue(value);
      onChange && onChange(event);

      if (timeoutAfterChange) {
        clearTimeout(timeoutAfterChange.current);
      }

      timeoutAfterChange.current = setTimeout(() => {
        onAfterChange && onAfterChange(value);
      }, debounce);
    } catch (error) {
      handleError(error, {
        path: PATH,
        name: 'onChangeInput',
        args: {},
      });
    }
  };

  const renderRequiredLabel = (label: ReactNode) => {
    if (required) {
      return (
        <RequiredLabel
          isReverseMask={isReverseMask}
          color={labelColor || '#666666'}
          style={{ marginBottom: 5 }}
        >
          {label}
        </RequiredLabel>
      );
    }

    return (
      <Text color="#666666" style={{ marginBottom: 5 }}>
        {label}
      </Text>
    );
  };
  const listDisableActions: Parameters<typeof getPreventKeyboardAction>[0] = [];

  if (props.disableUndo) {
    listDisableActions.push('undo');
  }

  const content = (
    <>
      {label && renderRequiredLabel(label)}

      <StyledInput
        {...restProps}
        ref={ref}
        value={value}
        onBlur={e => {
          if (!isFocused) {
            setFocused(true);
          }

          restProps.onBlur && restProps.onBlur(e);
        }}
        onChange={onChangeInput}
        {...getPreventKeyboardAction(listDisableActions)}
      />

      {(restProps.status === 'error' || errorArchive || requiredMsg) && !isHideErrMessage ? (
        <Text color="#ff4d4f" style={{ marginLeft: 8, marginTop: 5 }}>
          {errorMsg || errorArchive || requiredMsg}
        </Text>
      ) : null}
    </>
  );

  if (!withWrapper) return content;

  return <div className="input__wrapper">{content}</div>;
});

OriginInput.defaultProps = {
  debounce: 400,
  isHideErrMessage: false,
  withWrapper: true,
};

export const Input = OriginInput as typeof StyledInput & {
  DefaultInput: typeof AntdInput;
  CustomSearch: typeof SearchInput;
};

const SearchInput = React.forwardRef<InputRef, InputProps>((props, ref) => (
  <OriginInput
    bordered={false}
    autoFocus
    suffix={<Icon type="icon-ants-search-2" size={24} color={globalToken?.bw8} />}
    {...props}
    ref={ref}
    className={`${props.className} antsomi-search-input`}
  />
));

const PasswordInput = React.forwardRef<InputRef, InputProps>((props, ref) => (
  <AntdInput.Password
    iconRender={visible =>
      visible ? (
        <StyledIconWrapper type="text">
          <PreviewVisibilityEyeIcon />
        </StyledIconWrapper>
      ) : (
        <StyledIconWrapper type="text">
          <VisibilityOffEyeIcon />
        </StyledIconWrapper>
      )
    }
    {...props}
    ref={ref}
  />
));

Input.CustomSearch = SearchInput;
Input.TextArea = StyledInput.TextArea;
Input.DefaultInput = AntdInput;
Input.Password = PasswordInput;

export const { TextArea } = StyledInput;
