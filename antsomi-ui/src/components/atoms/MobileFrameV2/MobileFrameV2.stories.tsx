// Libraries
import React from 'react';

// Components
import { StoryObj, Meta, StoryFn } from '@storybook/react';
import { MobileFrameV2 } from './MobileFrameV2';

export default {
  title: 'Atoms/MobileFrameV2',
  component: MobileFrameV2,
  argTypes: {},
  parameters: {
    docs: {
      description: {
        component: 'MobileFrameV2 Component',
      },
    },
  },
} as Meta<typeof MobileFrameV2>;

export const Default = {
  args: {},
};

export const BasicUsage: StoryObj<any> = {
  render: () => (
    <MobileFrameV2>
      <div style={{ width: '100%', height: '100%' }}>
        <iframe
          style={{ width: '100%', height: '100%', position: 'absolute', zIndex: 3000 }}
          title="preview"
          name=""
          src="https://antsomi-docs.vercel.app/"
        />
      </div>
    </MobileFrameV2>
  ),

  parameters: {
    docs: {
      description: {
        story: 'This is a basic example of MobileFrameV2',
      },
    },
  },
};
