// Libraries
import { render, screen } from '@testing-library/react';

// Components
import { TextWithoutHeadingStyle, Vertical } from '../Divider.stories';
import { Divider } from '../index';

describe('Divider Component', () => {
  it('should render the Divider Component', () => {
    const { container } = render(<Divider />);
    expect(container).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  it('should render the Default Divider Component', () => {
    const { container } = render(<Divider />);

    const divider = screen.getByRole('separator');
    expect(divider).toBeInTheDocument();

    expect(container).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  it('should render the Horizontal Divider Component', () => {
    const { container } = render(
      <Divider orientation="right" plain>
        Right Text
      </Divider>,
    );

    expect(container).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  it('should render the Vertical Divider Component', () => {
    const { container } = render(Vertical.render?.call(this));
    expect(container).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });

  it('should render the TextWithoutHeadingStyle Divider Component', () => {
    const { container } = render(TextWithoutHeadingStyle.render?.call(this));

    const firstParagraph = screen.getAllByText(/Lorem ipsum dolor sit a/i);
    expect(firstParagraph[0]).toBeInTheDocument();

    expect(container).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});
