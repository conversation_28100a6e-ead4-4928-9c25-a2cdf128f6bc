// Libraries
import React, { memo, useMemo } from 'react';

// Antd Components
import { Slider as AntdSlider } from 'antd';

// Types
import type { SliderRangeProps, SliderSingleProps } from 'antd/es/slider';

// Styled
import { SliderWrapper } from './styled';
import { omit } from 'lodash';

export interface SliderProps extends Omit<SliderSingleProps, 'tooltipVisible'> {
  tooltipVisible?: boolean | 'default';
}

export const Slider: React.FC<SliderProps | SliderRangeProps> = memo(props => {
  const { range, value, min = 0, max = 0, className, tooltipVisible = false } = props;

  const isNegative = useMemo(() => !!(!range && min < 0), [range, min]);

  const calculateWidth = useMemo(
    () => (((value as number) || 0) * 100) / (Math.abs(max) + Math.abs(min) || 1),
    [min, max, value],
  );

  const restProps = tooltipVisible === 'default' ? omit(props, ['tooltipVisible']) : props;

  if (isNegative) {
    return (
      <SliderWrapper isNegative width={calculateWidth} className={className}>
        <AntdSlider
          {...(omit(restProps, ['marks', 'className']) as any)}
          marks={{
            0: ' ',
          }}
        />
      </SliderWrapper>
    );
  }

  return (
    <SliderWrapper className={className}>
      <AntdSlider {...(restProps as any)} />
    </SliderWrapper>
  );
});
