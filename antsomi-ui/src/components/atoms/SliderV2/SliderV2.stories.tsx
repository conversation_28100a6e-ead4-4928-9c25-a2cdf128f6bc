/* eslint-disable no-console */
// Library
import { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';

// Components
import { Icon } from '../Icon';
import { Switch } from '../Switch';
import { SliderV2 } from './index';

const meta = {
  title: 'Atoms/SliderV2',
  component: SliderV2,
  // More on argTypes: https://storybook.js.org/docs/react/api/argtypes
} satisfies Meta<typeof SliderV2>;

export default meta;
type Story = StoryObj<typeof SliderV2>;

export const Default: Story = {
  args: {},

  parameters: {
    docs: {
      description: {
        component:
          'A Slider component for displaying current value and intervals in range.\n # When To Use\n To input a value in a range.',
      },
    },
  },
};

export const Basic: Story = {
  render: args => {
    const [disabled, setDisabled] = useState(false);

    const onChange = (checked: boolean) => {
      setDisabled(checked);
    };

    return (
      <>
        <SliderV2 defaultValue={30} disabled={disabled} />
        <SliderV2 range defaultValue={[20, 50]} disabled={disabled} />
        Disabled: <Switch size="small" checked={disabled} onChange={onChange} />
      </>
    );
  },

  parameters: {
    docs: {
      description: {
        story:
          'Basic slider. When range is true, display as dual thumb mode. When disable is true, the slider will not be interactable.\n ##### Example',
      },
    },
  },
};

export const SliderIcon: Story = {
  render: args => {
    const { max = 0, min = 0 } = args;
    const [value, setValue] = useState(0);

    const mid = Number(((max - min) / 2).toFixed(5));
    const preColorCls = value >= mid ? '' : 'icon-wrapper-active';
    const nextColorCls = value >= mid ? 'icon-wrapper-active' : '';

    return (
      <div className="icon-wrapper" style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
        <Icon type="icon-ants-minus-square-outlined" className={preColorCls} />
        <SliderV2 onChange={newValue => setValue(newValue)} value={30} min={min} max={max} />
        <Icon type="icon-ants-plus-square-outlined" className={nextColorCls} />
      </div>
    );
  },
  args: {
    min: 0,
    max: 100,
  },

  parameters: {
    docs: {
      description: {
        story: 'You can add an icon beside the slider to make it meaningful.\n ##### Example',
      },
    },
  },
};

export const Event: Story = {
  render: args => {
    const onChange = (value: number | [number, number]) => {
      console.log('onChange: ', value);
    };

    const onAfterChange = (value: number | [number, number]) => {
      console.log('onAfterChange: ', value);
    };
    return (
      <>
        <SliderV2 defaultValue={30} onChange={onChange} onAfterChange={onAfterChange} />
        <SliderV2
          range
          step={10}
          defaultValue={[20, 50]}
          onChange={onChange}
          onAfterChange={onAfterChange}
        />
      </>
    );
  },

  parameters: {
    docs: {
      description: {
        story:
          "The onChange callback function will fire when the user changes the slider's value. The onAfterChange callback function will fire when onmouseup fired.\n ##### Example",
      },
    },
  },
};

export const Reverse: Story = {
  render: args => {
    const [reverse, setReverse] = useState(true);
    return (
      <>
        <SliderV2 defaultValue={30} reverse={reverse} />
        <SliderV2 range defaultValue={[20, 50]} reverse={reverse} />
        Reversed: <Switch size="small" checked={reverse} onChange={setReverse} />
      </>
    );
  },

  parameters: {
    docs: {
      description: {
        story: 'Using reverse to render slider reversely.\n ##### Example',
      },
    },
  },
};
