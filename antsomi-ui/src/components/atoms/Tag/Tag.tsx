/* eslint-disable react/no-array-index-key */
// Libraries
import React, { useMemo, useState } from 'react';
import { Tag as AntdTag, TagProps } from 'antd';
import styled from 'styled-components';

// Icons
import { CloseIcon } from '../../icons';

// Components
import { Flex } from '../Flex';
import { Button } from '../Button';
import { Typography } from '../Typography';
import { SearchPopover } from '../../molecules';

// Constants
import { globalToken } from '@antscorp/antsomi-ui/es/constants';
import { TagType } from 'antd/lib';
import { translate, translations } from '@antscorp/antsomi-ui/es/locales';

// Utils
import { searchStringQuery } from '@antscorp/antsomi-ui/es/utils';
import { Scrollbars } from '../Scrollbars';

export interface TagListProps {
  tags: string[];
  tagProps?: TagProps;
  limit?: number;
}

const { Text } = Typography;

const CloseBtn = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background-color: #fff;
  border-radius: 100%;
`;

const AllTagsContainer = styled(Flex)`
  padding: 10px;
`;

const OriginTag: React.FC<TagProps> = props => {
  const {
    closable = false,
    closeIcon = (
      <CloseBtn>
        <CloseIcon size={14} color={globalToken?.colorPrimary} />
      </CloseBtn>
    ),
    ...restOfProps
  } = props;

  return <AntdTag {...restOfProps} closable={closable} closeIcon={closable ? closeIcon : null} />;
};

const TagList: React.FC<TagListProps> = props => {
  const { tags, tagProps, limit = 2 } = props;

  const [state, setState] = useState({
    open: false,
    searchValue: '',
    selected: [],
  });
  const { searchValue } = state;

  const filteredTags = useMemo(
    () => tags.filter(tag => searchStringQuery(tag, searchValue)),
    [tags, searchValue],
  );

  const renderTags = () =>
    tags.slice(0, limit).map((tag, index) => (
      <OriginTag key={index} {...tagProps}>
        <Text ellipsis={{ tooltip: true }}>{tag}</Text>
      </OriginTag>
    ));

  const renderAllTags = () => (
    <Scrollbars autoHeight autoHeightMax={300}>
      <AllTagsContainer gap="8px 0px" wrap="wrap">
        {filteredTags.map((tag, index) => (
          <OriginTag key={index} {...tagProps}>
            <Text ellipsis={{ tooltip: true }}>{tag}</Text>
          </OriginTag>
        ))}
      </AllTagsContainer>
    </Scrollbars>
  );

  const renderShowMoreTags = () => {
    if (tags.length > limit) {
      return (
        <SearchPopover
          content={renderAllTags()}
          overlayInnerStyle={{ width: 300 }}
          inputSearchProps={{
            onAfterChange(value) {
              setState(prev => ({ ...prev, searchValue: value }));
            },
          }}
        >
          <Button type="text">{translate(translations.viewAll)}</Button>
        </SearchPopover>
      );
    }

    return null;
  };

  return (
    <Flex>
      {renderTags()} {renderShowMoreTags()}
    </Flex>
  );
};

export const Tag = OriginTag as TagType & {
  List: React.FC<TagListProps>;
};

Tag.CheckableTag = AntdTag.CheckableTag;
Tag.List = TagList;
