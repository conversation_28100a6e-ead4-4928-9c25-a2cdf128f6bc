// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Spin > should match snapshot 1`] = `
.c1 {
  -webkit-animation: 1.5s linear 0s infinite normal none running spin;
  animation: 1.5s linear 0s infinite normal none running spin;
}

.c1 circle.circle {
  stroke: currentcolor;
  stroke-dasharray: 80px,200px;
  stroke-dashoffset: 0;
  -webkit-animation: 1.5s ease-in-out 0s infinite normal none running animation-loading;
  animation: 1.5s ease-in-out 0s infinite normal none running animation-loading;
}

.c0 {
  max-height: unset !important;
}

<div
  aria-busy="true"
  aria-live="polite"
  class="ant-spin ant-spin-spinning c0 css-dev-only-do-not-override-ht6oo7"
>
  <svg
    class="c1 ant-spin-dot"
    fill="none"
    height="24"
    style="font-size: 40px; color: rgb(0, 95, 184);"
    viewBox="0 0 41 41"
    width="24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M40.5 20.5C40.5 31.5457 31.5457 40.5 20.5 40.5C9.4543 40.5 0.5 31.5457 0.5 20.5C0.5 9.4543 9.4543 0.5 20.5 0.5C31.5457 0.5 40.5 9.4543 40.5 20.5ZM2.5 20.5C2.5 30.4411 10.5589 38.5 20.5 38.5C30.4411 38.5 38.5 30.4411 38.5 20.5C38.5 10.5589 30.4411 2.5 20.5 2.5C10.5589 2.5 2.5 10.5589 2.5 20.5Z"
      fill="transparent"
    />
    <circle
      class="circle"
      cx="20.5"
      cy="20.5"
      fill="none"
      r="19"
      stroke-width="2"
    />
  </svg>
</div>
`;
