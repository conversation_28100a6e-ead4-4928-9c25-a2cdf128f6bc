// Libraries
import React from 'react';

// Components
import { StoryObj, Meta } from '@storybook/react';
import { MobileFrame } from './MobileFrame';

export default {
  title: 'Atoms/MobileFrame',
  component: MobileFrame,
  argTypes: {},
  parameters: {
    docs: {
      description: {
        component: 'MobileFrame Component',
      },
    },
  },
} as Meta<typeof MobileFrame>;

export const Default = {
  args: {},
};

export const Basic: StoryObj<any> = {
  render: () => <MobileFrame />,

  parameters: {
    docs: {
      description: {
        story: 'This is a basic example of MobileFrame',
      },
    },
  },
};
