import React, { useEffect, useState } from 'react';

import StarImage from '@antscorp/antsomi-ui/es/assets/images/stars_slider.png';

interface RateContinuousProps {
  count?: number;
  className?: string;
  defaultValue?: number;
  /**
   * @description number of star
   */
  value?: number;
  type?: 'full' | 'half' | 'continuous';
  /**
   * @param value current number of stars are selected
   * @description number of stars = fillWidth / STAR_WIDTH
   */
  onChange?: (value: number) => void;
}

const STAR_WIDTH = 30;

const parseNumDecimals = (input: number, decimals: number) =>
  +Number.parseFloat(input?.toString()).toFixed(decimals);

const roundHalf = (num: number) => {
  const multiplier = 1 / 0.5;
  return Math.round(num * multiplier) / multiplier;
};

/**
 *
 * @param width width measure from the left edge of star container
 * @param type type of star - 'full' | 'half' | 'continuous'
 * @default type 'full'
 * @returns
 */
const getWidth = (width: number, type: 'full' | 'half' | 'continuous' = 'full') => {
  const numberOfStar = width / STAR_WIDTH;

  return type === 'full'
    ? Math.ceil(numberOfStar) * STAR_WIDTH
    : type === 'half'
    ? roundHalf(numberOfStar) * STAR_WIDTH
    : width;
};

export const RateV2: React.FC<RateContinuousProps> = ({
  count = 5,
  className,
  defaultValue = 0,
  value,
  type = 'full',
  onChange,
}) => {
  const fullWidth = count * STAR_WIDTH;

  // The width is calculated when move mouse => change the UI, not change the real value
  const [fillWidth, setFillWidth] = useState(() =>
    defaultValue ? getWidth(defaultValue * STAR_WIDTH, type) : 0,
  );
  // The real current value that will be return to parent component by onChange function
  // currWidth = value * STAR_WIDTH
  const [currWidth, setCurrWidth] = useState(0);

  const handleMouseMove = e => {
    const rect = e.currentTarget.getBoundingClientRect();

    const x = e.clientX - rect.left;
    const hoverWidth = Math.round(x);

    setFillWidth(getWidth(hoverWidth, type));
  };

  const handleClick = () => {
    // If have no value => auto set current value; if have value => must control value with onChange props
    if (!value) setCurrWidth(fillWidth);

    if (onChange) onChange(parseNumDecimals(fillWidth / STAR_WIDTH, 2));
  };

  useEffect(() => {
    if (typeof value === 'number') {
      const width = getWidth(value * STAR_WIDTH, type);
      setCurrWidth(width);
      setFillWidth(width);
    }
  }, [value]);

  return (
    <div
      className={`antsomi-rate-continuous${className ? ` ${className}` : ''}`}
      style={{
        position: 'relative',
        width: `${fullWidth}px`,
        backgroundPosition: '-27px',
        height: '28px',
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={() => setFillWidth(currWidth || 0)}
      onClick={handleClick}
    >
      <div
        className="custom-rate custom-rate--fill"
        style={{
          position: 'absolute',
          background: `url(${StarImage}) 0px 0px`,
          width: `${fillWidth}px`,
          height: '26px',
          zIndex: 1,
        }}
      />
      <div
        className="custom-rate custom-rate--empty"
        style={{
          position: 'absolute',
          top: '0px',
          left: '0px',
          backgroundImage: 'none',
          background: `transparent url(${StarImage}) 0px -26px`,
          width: `${fullWidth}px`,
          height: '26px',
        }}
      />
    </div>
  );
};
