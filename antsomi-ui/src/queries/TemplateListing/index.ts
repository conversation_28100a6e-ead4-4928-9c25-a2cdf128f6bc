// Libraries
import {
  UseInfiniteQueryOptions,
  UseMutationOptions,
  UseQueryOptions,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { get, omit } from 'lodash';

// Models
import { TemplateCategory } from '@antscorp/antsomi-ui/es/models/TemplateCategory';

// Services
import {
  TBulkUpdateObjectTemplateArgs,
  TGetCategoryListArgs,
  TGetObjectTemplateDetailArgs,
  TGetObjectTemplateListArgs,
  TGetSaveAsGalleryPermissionArgs,
  TUpdateObjectTemplateArgs,
  TValidateObjectTemplateNameArgs,
  templateListingServices,
} from '@antscorp/antsomi-ui/es/services/TemplateListing';

// Constants
import { QUERY_KEYS } from '../../constants/queries';
import { ResponseCheckNameExist, ResponseListing } from '../../types';
import { ObjectTemplate } from '../../models/ObjectTemplate';
import { TCreateObjectTemplateArgs } from '../../services/TemplateListing';
import { PUBLIC_LEVEL } from '../../constants';

const { category, objectTemplate } = templateListingServices;

export type TGetTemplateCategoryList = {
  args: TGetCategoryListArgs;
  options?: UseQueryOptions<any, any, TemplateCategory[], any[]>;
};

export type TGetSaveAsGalleryPermissionEmails = {
  args: TGetSaveAsGalleryPermissionArgs;
  options?: UseQueryOptions<any, any, string[], any[]>;
};

export type TGetObjectTEmplateList = {
  args: TGetObjectTemplateListArgs;
  options?: UseInfiniteQueryOptions<any, any, ResponseListing<ObjectTemplate>, (string | number)[]>;
};

export type TGetObjectTemplateDetail = {
  args: TGetObjectTemplateDetailArgs;
  options?: UseQueryOptions<ObjectTemplate, any, ObjectTemplate, any[]>;
};

export type TBulkUpdateTemplate = {
  options?: UseMutationOptions<any, any, TBulkUpdateObjectTemplateArgs, unknown>;
};

export type TUseCreateTemplate = {
  options?: UseMutationOptions<any, any, TCreateObjectTemplateArgs, unknown>;
};

export type TUseUpdateTemplate = {
  options?: UseMutationOptions<any, any, TUpdateObjectTemplateArgs, unknown>;
};

export type TUseValidateTemplateName = {
  options?: UseMutationOptions<
    ResponseCheckNameExist,
    any,
    TValidateObjectTemplateNameArgs,
    unknown
  >;
};

export type TUsePersistTemplate = {
  options?: UseMutationOptions<
    ObjectTemplate,
    any,
    { persistType: 'create' | 'update'; params: TUpdateObjectTemplateArgs },
    unknown
  >;
};

export const useGetTemplateCategoryList = (params: TGetTemplateCategoryList) => {
  const { options, args } = params;

  return useQuery({
    queryKey: [QUERY_KEYS.GET_TEMPLATE_CATEGORY_LIST, args.params],
    queryFn: () =>
      args?.params?.publicLevel === PUBLIC_LEVEL.PUBLIC
        ? category.getPublicList(params.args)
        : category.getList(params.args),
    onSuccess() {},
    ...options,
  });
};

export const useGetSaveAsGalleryPermissionEmails = (params: TGetSaveAsGalleryPermissionEmails) => {
  const { options } = params;

  return useQuery({
    queryKey: [QUERY_KEYS.GET_SAVE_AS_GALLERY_PERMISSION_EMAILS],
    queryFn: () => objectTemplate.getSaveAsGalleryPermission(params.args),
    onSuccess() {},
    ...options,
  });
};

export const useGetObjectTemplateList = (params: TGetObjectTEmplateList) => {
  const { options, args } = params;

  return useInfiniteQuery({
    queryKey: [QUERY_KEYS.GET_OBJECT_TEMPLATE_LIST, args.params],
    queryFn: async ({ pageParam = 1 }) => {
      const res =
        args?.params?.public_level === PUBLIC_LEVEL.PUBLIC
          ? await objectTemplate.getPublicList({
              ...args,
              params: { ...args.params, page: pageParam },
            })
          : await objectTemplate.getList({
              ...args,
              params: { ...args.params, page: pageParam },
            });

      return res;
    },
    getNextPageParam: lastPage => {
      const { meta } = lastPage;
      const { currentPage, totalPages } = meta;

      return +currentPage < +totalPages ? Number(lastPage.meta.currentPage) + 1 : undefined;
    },
    ...options,
  });
};

export const useGetObjectTemplateDetail = (params: TGetObjectTemplateDetail) => {
  const { args, options } = params;

  return useQuery({
    queryKey: [QUERY_KEYS.GET_OBJECT_TEMPLATE_DETAIL, +args.params.template_id],
    queryFn: () => objectTemplate.getDetail(args),
    enabled: !!args.params.template_id,
    ...options,
  });
};

export const useValidateTemplateName = (params: TUseValidateTemplateName) => {
  const { options } = params;

  return useMutation({
    mutationFn: objectTemplate.validateName,
    ...options,
  });
};

export const useBulkUpdateTemplate = (params: TBulkUpdateTemplate) => {
  const { options } = params;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: objectTemplate.bulkUpdate,
    onSettled() {
      queryClient.invalidateQueries([QUERY_KEYS.GET_OBJECT_TEMPLATE_LIST], {
        exact: false,
      });
      queryClient.invalidateQueries([QUERY_KEYS.GET_TEMPLATE_CATEGORY_LIST], {
        exact: false,
      });
    },
    ...options,
  });
};

export const useCreateTemplate = (params: TUseCreateTemplate) => {
  const { options } = params;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: objectTemplate.create,
    onSettled() {
      queryClient.invalidateQueries([QUERY_KEYS.GET_OBJECT_TEMPLATE_LIST], {
        exact: false,
      });
      queryClient.invalidateQueries([QUERY_KEYS.GET_TEMPLATE_CATEGORY_LIST], {
        exact: false,
      });
    },
    ...options,
  });
};

export const useUpdateTemplate = (params: TUseUpdateTemplate) => {
  const { options } = params;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: objectTemplate.update,
    onSettled() {
      queryClient.invalidateQueries([QUERY_KEYS.GET_OBJECT_TEMPLATE_LIST], {
        exact: false,
      });
      queryClient.invalidateQueries([QUERY_KEYS.GET_TEMPLATE_CATEGORY_LIST], {
        exact: false,
      });
    },
    ...options,
  });
};

export const usePersistTemplate = (params: TUsePersistTemplate) => {
  const { options } = params;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (args: { persistType: 'create' | 'update'; params: TUpdateObjectTemplateArgs }) => {
      const { persistType, params } = args;

      switch (persistType) {
        case 'update':
          return objectTemplate.update(params);

        case 'create':
        default:
          return objectTemplate.create({ ...params, data: omit(params.data, ['template_id']) });
      }
    },
    onSettled(data, error, variables) {
      queryClient.invalidateQueries(
        [QUERY_KEYS.GET_OBJECT_TEMPLATE_DETAIL, +get(variables, 'params.data.template_id', -1)],
        { exact: true },
      );
      queryClient.invalidateQueries([QUERY_KEYS.GET_OBJECT_TEMPLATE_LIST], {
        exact: false,
      });
      queryClient.invalidateQueries([QUERY_KEYS.GET_TEMPLATE_CATEGORY_LIST], {
        exact: false,
      });
    },
    ...options,
  });
};
