// Libraries
import {
  useQuery,
  UseQueryOptions,
  useMutation,
  UseMutationOptions,
  useQueryClient,
} from '@tanstack/react-query';

// Services
import { accountService, TAccountParams, TUpdateRecentAccount } from '../../services/Account';

// Constants
import { QUERY_KEYS } from '../../constants/queries';
import { AccountListing, AccountRecent, TAccountPermission } from '../../models/AccountListing';

type OptionHasDefault = 'queryKey' | 'queryFn';

type GetAccountListProps = {
  apiConfig: TAccountParams;
  options?: Omit<UseQueryOptions<AccountListing, any, AccountListing, any[]>, OptionHasDefault>;
};

type GetRecentAccountProps = {
  apiConfig: TAccountParams;
  options?: Omit<UseQueryOptions<any, any, AccountRecent, any[]>, OptionHasDefault>;
};

type UpdateRecentAccountProps = {
  apiConfig: TAccountParams;

  options?: UseMutationOptions<{ status: number }, any, TUpdateRecentAccount['data'], unknown>;
  // options?: Omit<UseQueryOptions<any, any, AccountRecent, any[]>, OptionHasDefault>;
};

type TCheckUserPermissionProps = {
  apiConfig: TAccountParams;
  options?: Omit<
    UseQueryOptions<any, any, TAccountPermission | undefined, any[]>,
    OptionHasDefault
  >;
};

const { getList, getRecentAccount, updateRecentAccount, checkPermission } = accountService;

export const useGetAccountList = (props: GetAccountListProps) => {
  const { apiConfig, options } = props;

  return useQuery({
    queryKey: [QUERY_KEYS.GET_ACCOUNT_LISTING, apiConfig],
    queryFn: () => getList(apiConfig),
    ...options,
  });
};

export const useGetRecentAccount = (props: GetRecentAccountProps) => {
  const { apiConfig, options } = props;

  return useQuery({
    queryKey: [QUERY_KEYS.GET_RECENT_ACCOUNT],
    queryFn: () => getRecentAccount(apiConfig),
    ...options,
  });
};

export const useUpdateRecentAccount = (props: UpdateRecentAccountProps) => {
  const { apiConfig, options } = props;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: variables => updateRecentAccount({ params: apiConfig, data: variables }),
    onSettled: () => {
      queryClient.invalidateQueries([QUERY_KEYS.UPDATE_RECENT_ACCOUNT], {
        exact: false,
      });
    },
    ...options,
  });
};

export const useCheckUserPermission = (props: TCheckUserPermissionProps) => {
  const { apiConfig, options } = props;
  const { languageCode, userId, portalId } = apiConfig;
  return useQuery({
    queryKey: [QUERY_KEYS.CHECK_ACCOUNT_PERMISSION, languageCode, userId, portalId],
    queryFn: () => checkPermission(apiConfig),
    ...options,
  });
};
