// Libraries
import { QueryClientProvider, QueryClient } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: 1,
    },
  },
});

export {
  QueryClientProvider as QueryClientProviderAntsomiUI,
  QueryClient as QueryClientAntsomiUI,
  ReactQueryDevtools as QueryDevtoolsAntsomiUI,
  queryClient as queryClientAntsomiUI,
};
