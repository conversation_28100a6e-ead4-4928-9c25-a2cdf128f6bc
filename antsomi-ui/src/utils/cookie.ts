// Libraries
import { Cookies } from 'react-cookie';

// Type
import { TEnv } from '../types/config';
import { ENV } from '../config';

const cookies = new Cookies(null, {
  path: '/',
  sameSite: 'none',
  secure: true,
});

interface RemoveCookieSubDomain {
  name: string;
  env?: TEnv;
  cookieOptions?: Record<string, any>;
}

interface SetCookieSession {
  name: string;
  value: any;
  env?: TEnv;
  cookieOptions?: Record<string, any>;
}

// Handle cookie
export const setCookieSession = ({ name, value, cookieOptions }: SetCookieSession) => {
  cookies.set(name, value, {
    ...cookieOptions,
  });

  if (typeof Storage !== undefined) {
    sessionStorage.setItem(name, name);
  }
};

// Handle cookie with subdomain
export const removeCookieSubDomain = ({
  name,
  env = 'development',
  cookieOptions,
}: RemoveCookieSubDomain) => {
  const arrTmp = location.hostname.split('.');

  cookies.remove(name, {
    ...(env !== ENV.DEV && {
      domain: `.${arrTmp.length < 4 ? arrTmp.slice(-2).join('.') : arrTmp.slice(-3).join('.')}`,
    }),
    path: '/',
    sameSite: 'none',
    secure: true,
    ...cookieOptions,
  });
};

export const removeAppCookieSessionSubdomain = (args: RemoveCookieSubDomain) => {
  removeCookieSubDomain(args);

  if (typeof Storage !== undefined) {
    sessionStorage.removeItem(args.name);
  }
};

export const removeAppCookieSession = (args: RemoveCookieSubDomain) => {
  cookies.remove(args?.name, args?.cookieOptions);

  if (typeof Storage !== undefined) {
    sessionStorage.removeItem(args.name);
  }
};

export const removeAppCookieSessionSubdomainPrefix = (args: RemoveCookieSubDomain) => {
  Object.keys(cookies.getAll()).forEach(cookieName => {
    if (cookieName.startsWith(args.name)) {
      removeAppCookieSessionSubdomain({
        ...args,
        name: cookieName,
      });
    }
  });
};
