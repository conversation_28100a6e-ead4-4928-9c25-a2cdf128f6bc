/* eslint-disable @typescript-eslint/no-unused-expressions */
// Libraries
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { has } from 'lodash';
import pako from 'pako';
import qs from 'qs';

// Types
import { PayloadInfo } from '../types';

// Utils
import { searchParamsToObject } from '../utils';

export const instance = axios.create();

const { CancelToken } = axios;

const isDebugApi =
  searchParamsToObject((window.location?.href || '').split('?')[1])['debug-api'] != null;

type UpdateParams = {
  id?: string | number;
  API_HOST: string;
  config?: AxiosRequestConfig;
  isGzip?: boolean;
};

export const services = {
  mediaTemplateDesign: {
    get: function get(params: any, payloadInfo: PayloadInfo) {
      if (typeof params.API_HOST !== 'undefined' && typeof params.id !== 'undefined') {
        const { API_HOST } = params;
        const { userId, accountId, token } = payloadInfo;
        const cancelToken = params.cancelToken ? params.cancelToken : new CancelToken(() => {});
        const { signal } = params;

        delete params.API_HOST;
        delete params.cancelToken;
        delete params.signal;

        if (has(params, 'url')) {
          delete params.url;
        }

        const snapParams = {
          ...params,
          _token: token,
          _user_id: userId,
          _account_id: accountId,
        };

        return instance.get(API_HOST + '/' + params.id, {
          params: snapParams,
          cancelToken: cancelToken,
          signal,
        });
      } else {
        return false;
      }
    },
    getList: function getList(params: any, infos: PayloadInfo): any {
      if (typeof params.API_HOST !== 'undefined') {
        const { API_HOST } = params;
        const { userId, accountId, token } = infos;
        const cancelToken = params.cancelToken ? params.cancelToken : new CancelToken(() => {});
        const { signal } = params;

        delete params.API_HOST;
        delete params.cancelToken;
        delete params.signal;

        if (has(params, 'url')) {
          delete params.url;
        }

        const snapParams = {
          ...params,
          _token: token,
          _user_id: userId,
          _account_id: accountId,
        };

        return instance.get(API_HOST, {
          params: snapParams,
          cancelToken,
          signal,
        });
      }
      return false;
    },
    create: function create(
      params: any,
      infos: PayloadInfo,
    ): Promise<AxiosResponse<any, any> | any> {
      if (params.API_HOST !== 'undefined') {
        const { API_HOST } = params;
        const { userId, accountId, token } = infos;
        const cancelToken = params.cancelToken ? params.cancelToken : new CancelToken(() => {});

        delete params.API_HOST;
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        params.cancelToken && delete params.cancelToken;

        if (has(params, 'url')) {
          delete params.url;
        }

        const paramsInfo = {
          _token: token,
          _user_id: userId,
          _account_id: accountId,
        };

        return instance.post(API_HOST, params, { cancelToken, params: paramsInfo });
      }
      return Promise.resolve(false);
    },
    update: function update(
      params: UpdateParams,
      infos: PayloadInfo,
    ): Promise<AxiosResponse<any, any> | any> {
      const { id, API_HOST, config = {}, isGzip, ...restOfParams } = params;
      const { userId, accountId, token } = infos;
      const data = isGzip && !isDebugApi ? pako.gzip(JSON.stringify(restOfParams)) : restOfParams;
      if (typeof API_HOST !== 'undefined' && typeof id !== 'undefined') {
        const paramsInfo = {
          _token: token,
          _user_id: userId,
          _account_id: accountId,
        };

        return instance.put(`${API_HOST}/${id}`, data, {
          ...config,
          ...(isGzip &&
            !isDebugApi && {
              headers: {
                ...config.headers,
                'Content-Type': 'application/json',
                'content-encoding': 'gzip',
              },
            }),
          params: paramsInfo,
        });
      }
      return Promise.resolve(false);
    },
    upload: function upload(params: any, infos: PayloadInfo) {
      if (typeof params.API_HOST !== 'undefined') {
        const { API_HOST } = params;
        const { userId, accountId, token } = infos;

        delete params.API_HOST;

        const paramsInfo = {
          _token: token,
          _user_id: userId,
          _account_id: accountId,
        };

        if (has(params, 'url')) {
          delete params.url;
        }

        const url = `${API_HOST}?${qs.stringify(params.params)}`;

        return instance.post(url, params.formData, {
          params: paramsInfo,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
      }
      return false;
    },
  },
};
