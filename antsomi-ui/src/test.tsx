/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState } from 'react';
import { createRoot } from 'react-dom/client';

import '@antscorp/icons/main.css';

import {
  CalendarSelection,
  ColorSetting,
  DatePicker,
  EdgeSetting,
  GradientSetting,
  FontSetting,
  // ChatBox
  Help,
  IconSelection,
  InputDynamic,
  InputNumberWithUnit,
  PositionSetting,
  RadioGroup,
  SettingWrapper,
  // Slider,
  SliderWithInputNumber,
  Space,
  UploadImage,
  Tabs,
  ResizeGrid,
  SlideBar,
  SlideBarProps,
  ActionType,
  useTemplateListing,
  TemplateListing,
  Button,
  Layout,
  HeaderV2,
  RequiredLabel,
  Login,
  ContentSources,
} from './components';

// Constants
import { GET_LIST_TYPE, THEME } from './constants';
// import { Slider } from 'antd';
import { Slider, SliderProps } from '@antscorp/antsomi-ui/es/components/atoms/Slider';
import { Rate } from './components/atoms/Rate';
import { TFontSettings } from './components/molecules/FontSetting/types';

// Queries configs
import { queryClientAntsomiUI, QueryClientProviderAntsomiUI } from './queries';
import { AppConfigProvider, AppConfigProviderProps, ConfigProvider } from './providers';
import { DataTable } from './components/organism/DataTable/DataTable';
import { DataTableTest, FilterTest, MatchesAnySelectTest } from './tests';
import { BrowserRouter } from 'react-router-dom';
import { AdvancedPicker } from './components/molecules/DatePicker/components/AdvancedPicker';

export const BACKGROUND_COLOR_STYLE = {
  SOLID: {
    value: 'color',
    label: 'Solid',
  },
  GRADIENT: {
    value: 'gradient',
    label: 'Gradient',
  },
};

const DOMAIN_MEDIA_SANDBOX = 'https://sandbox-media-template.antsomi.com/cdp';

export const customColors = [
  '#f7da64',
  '#8912dd',
  '#ed1515',
  '#230439',
  '#d11a66',
  '#ffbd64',
  '#f1ab96',
  '#824ccd',
  '#5858e9',
  '#57b8c2',
];

type MarginProps = number | 'auto';
type ValueProps = [MarginProps, MarginProps, MarginProps, MarginProps];

// Variables
const appConfig: AppConfigProviderProps = {
  env: 'sandbox',
  auth: {
    token: '5474r2x214z284d4w2b4y4h5a454p444m4h423u4z5t5',
    portalId: 33167,
    userId: '**********',
    accountId: '**********',
  },
  languageCode: 'en',
};

/* Test Data Table */

export const App = () => {
  // const [valueShortlink, setValueShortlink] = useState('#{shortlink(https://test.com.vn)}');
  const [valueShortlink, setValueShortlink] = useState('');
  const [state, setState] = useState<{
    date: string;
    option: any;
    format: string;
  }>({
    option: {
      dateType: 'today',
      calculationDate: 'years',
      calculationType: 'minus',
      value: 1,
    },
    date: '',
    format: 'YYYYMMDD',
  });

  const [colorSettingState, setColorSettingState] = useState<{
    color: string;
    customColors: Array<string>;
  }>({
    color: 'green',
    customColors,
  });

  const [stateSlideBar, setStateSlideBar] = useState<SlideBarProps>({
    errors: [],
    disabled: true,
    prefix: 'Item',
    isViewMode: false,
    isShowAdd: true,
    limit: {
      min: 1,
      max: 2,
    },
    activeId: '1',
    options: Array.from({ length: 5 }, (_, index) => ({
      label: `Item ${index + 1}`,
      value: `${index + 1}`,
    })),
    isShowLabelSequentially: false,
  });

  const [radioValue, setRadioValue] = useState('color');
  const [sliderValue, setSliderValue] = useState(10);
  const [edge, setEdge] = useState<ValueProps>(['auto', 'auto', 700, 700]);
  const [icon, setIcon] = useState('');

  // const {
  //   categoryItems,
  //   templateItems,
  //   checkedCategories,
  //   openCategoryKeys,
  //   isLoadingCategoryList,
  //   isLoadingTemplateList,
  //   onLoadMore: onLoadMoreTemplates,
  //   onChangeOpenCategoryKeys,
  //   onChangeCheckedCategories,
  //   onRemoveObjectTemplate,
  // } = useTemplateListing({
  //   serviceAuth: {
  //     url: 'https://sandbox-media-template.antsomi.com/cdp/api/v1',
  //     token: '5474r2x214z254a4u2a4y444m4j5p27444c4h4h4n5t5',
  //     userId: '**********',
  //     accountId: '**********',
  //   },
  //   config: {
  //     getListType: GET_LIST_TYPE.OWNER,
  //     objectType: 1,
  //     publicLevel: 0,
  //     limitListPerPage: 10,
  //   },
  //   checkedCategoriesDefault: {
  //     device_type: [1, 2],
  //     template_type: [1, 2, 3, 4, 5],
  //   },
  // });

  const callbackColorSetting = (key: 'ON_ADD_NEW_PRESET' | 'ON_EDIT_CLICKED', dataIn: any) => {
    switch (key) {
      case 'ON_ADD_NEW_PRESET':
        // eslint-disable-next-line no-case-declarations
        const { customColors = [] } = dataIn;

        setColorSettingState(prev => ({
          ...prev,
          customColors,
        }));
        break;
      case 'ON_EDIT_CLICKED': {
        // eslint-disable-next-line no-console
        console.log('insideee.dev - clicked edit preset :>');
        break;
      }

      default:
        break;
    }
  };

  const handleChangeColorSetting = (colorOut: string) => {
    setColorSettingState(prev => ({
      ...prev,
      color: colorOut,
    }));
  };

  const handleChangeRadio = e => {
    const { value = '' } = e.target;
    setRadioValue(value);
  };

  const handleChangeSlider = value => {
    setSliderValue(value);
  };

  const handleSetIcon = value => {
    setIcon(value);
  };

  const callbackSlideBar = (type: ActionType, data: any) => {
    console.log({ type, data });
  };

  const d = {
    loginDomain: 'https://iam.ants.tech/',
  };

  // const d = {
  //   loginDomain: 'https://sandbox-aacm.ants.vn',
  //   networkId: '76753',
  //   theme: 'white',
  //   u_ogs: 'uogs_sand_48956',
  //   apiDomain: '//sandbox-permission.ants.vn',
  // };

  /* Login */
  return (
    <AdvancedPicker
      timezone="Australia/Brisbane"
      disableBeforeDate="2024-09-05"
      format="YYYY-MM-DD HH:mm:ss"
      valueType="YEAR_MONTH_DAY_SECOND"
    />
  );

  // return <Login {...d} />;

  /* MatchesAnySelect */
  return <MatchesAnySelectTest />;

  /* Data Table */

  return (
    <div>
      <RequiredLabel>this is Required</RequiredLabel>
      THIS IS TEST VITE BUILD
      <Button>Click me</Button>
    </div>
  );
  // return <DataTableTest />;

  // --------------------------- Test Layout 2.0 -------------------------------------------

  // return (
  //   <HeaderV2
  //     accountSharingConfig={{ u_ogs: 'uogs', appCode: 'APP_CUSTOMER_360' }}
  //     // useURLParam
  //     helpConfig={{
  //       configs: {
  //         appCode: 'SANDBOX_MARKETING',
  //         avatar: '//c0-platform.ants.tech/avatar/2021/09/17/0xgbkurioo.png',
  //         config: {
  //           p_timezone: 'Asia/Singapore',
  //           api_pid: 33167,
  //           p_f_longdatetime: "dd MMMM 'at' HH:mm:ss",
  //           embeddedData: {},
  //           INSIGHT_U_OGS: 'uogs',
  //         },
  //       },
  //     }}
  //   />
  // );
  // return <Layout leftMenuProps={{ objectId: 1, objectType: 'OVERVIEW', isGrouped: true }} />;

  // --------------------------- Test SlideBar start -------------------------------------------
  // return (
  //   <div
  //     style={{
  //       width: 500,
  //       height: 500,
  //       border: '1px solid black',
  //       margin: '0 auto',
  //       padding: 12,
  //       borderRadius: '10px',
  //     }}
  //   >
  //     <SlideBar {...stateSlideBar} callback={callbackSlideBar} />
  //   </div>
  // );
  // --------------------------- Test SlideBar end -------------------------------------------

  // ----------------------------  Test Input Dynamic start ------------------------------
  // return (
  //   <div
  //     style={{
  //       width: 500,
  //       height: 500,
  //       border: '1px solid black',
  //       margin: '0 auto',
  //       padding: 12,
  //       borderRadius: '10px',
  //     }}
  //   >
  //     <SlideBar {...stateSlideBar} callback={callbackSlideBar} />
  //   </div>
  // );
  // --------------------------- Test SlideBar end -------------------------------------------

  // ----------------------------  Test Input Dynamic start ------------------------------
  // return (
  //   <div
  //     style={{
  //       width: 500,
  //       height: 500,
  //       border: '1px solid black',
  //       margin: '0 auto',
  //       padding: 12,
  //       borderRadius: '10px',
  //     }}
  //   >
  //     <InputDynamic
  //       errors={[]}
  //       onChange={dataOut => setValueShortlink(dataOut)}
  //       onError={() => {}}
  //       isRealTime
  //       isViewMode={false}
  //       canMultipleLine={false}
  //       value={valueShortlink}
  //     />
  //   </div>
  // );
  // ----------------------------  Test Input Dynamic End ------------------------------
  // ----------------------------  Test Helps Start ------------------------------
  // return (
  //   <div style={{ display: 'flex', justifyContent: 'center', width: '100vw', height: '100vh' }}>
  //     <Help />
  //   </div>
  // );
  // ----------------------------  Test Helps End ------------------------------

  // ------------------------ Media template components test start -------------------------
  // return (
  //   <ConfigProvider>
  //     <div
  //       style={{
  //         width: 500,
  //         height: 1000,
  //         border: '1px solid black',
  //         margin: '0 auto',
  //         padding: 12,
  //         borderRadius: '10px',
  //         marginTop: 12,
  //       }}
  //     >
  //       {/* <h2 style={{ textAlign: 'center' }}>Test component of media template</h2> */}
  //       {/* <Space size={20} direction="vertical"> */}
  //       {/*   {/* <SettingWrapper vertical label="Color Setting"> */}
  //       {/*      <ColorSetting */}
  //       {/*        label="Background Color" */}
  //       {/*        labelStyling={{ color: THEME.token?.colorIcon }} */}
  //       {/*        color={colorSettingState.color} */}
  //       {/*        customColors={colorSettingState.customColors} */}
  //       {/*        callback={callbackColorSetting} */}
  //       {/*        onChange={handleChangeColorSetting} */}
  //       {/*      /> */}
  //       {/*    </SettingWrapper> */}
  //       {/**/}
  //       {/*    <SettingWrapper vertical label="Input number with unit"> */}
  //       {/*      <InputNumberWithUnit */}
  //       {/*        unit="px" */}
  //       {/*        min={0} */}
  //       {/*        max={2000} */}
  //       {/*        value={700} */}
  //       {/*        onChange={value => { */}
  //       {/*          console.log('onChange:: ', value); */}
  //       {/*        }} */}
  //       {/*        onChangeUnit={unit => { */}
  //       {/*          console.log('onChangeUnit:: ', unit); */}
  //       {/*        }} */}
  //       {/*      /> */}
  //       {/*    </SettingWrapper> */}
  //       {/*    <SettingWrapper label="Radio Group"> */}
  //       {/*      <RadioGroup */}
  //       {/*        options={Object.values(BACKGROUND_COLOR_STYLE)} */}
  //       {/*        value={radioValue} */}
  //       {/*        onChange={e => handleChangeRadio(e)} */}
  //       {/*      /> */}
  //       {/*    </SettingWrapper> */}
  //       {/*   <SettingWrapper vertical label="Color Setting"> */}
  //       {/*     <GradientSetting /> */}
  //       {/*   </SettingWrapper> */}
  //       {/*   <SliderWithInputNumber */}
  //       {/*     label="Size (px)" */}
  //       {/*     labelStyling={{ marginTop: 10 }} */}
  //       {/*     min={0} */}
  //       {/*     max={100} */}
  //       {/*     value={sliderValue} */}
  //       {/*     onAfterChange={handleChangeSlider} */}
  //       {/*   /> */}
  //       {/*   <EdgeSetting */}
  //       {/*     label="Notification Spacing" */}
  //       {/*     unit="px" */}
  //       {/*     linked={false} */}
  //       {/*     values={edge} */}
  //       {/*     // edgeLabels={[t(translations.column.title), t(translations.row.title)]} */}
  //       {/*     onChange={({ values, linked, unit }) => { */}
  //       {/*       setEdge(values); */}
  //       {/*       // onUpdateSettings({ */}
  //       {/*       //   gapX: values[0] + unit, */}
  //       {/*       //   gapY: values[1] + unit, */}
  //       {/*       //   gapSuffix: unit, */}
  //       {/*       //   linkedGapInput: linked, */}
  //       {/*       // }); */}
  //       {/*     }} */}
  //       {/*   /> */}
  //       {/*   <PositionSetting */}
  //       {/*     settings={{ linkedPositionInput: false, positionSuffix: 'px' }} */}
  //       {/*     styles={{ top: '10px', right: '20px', bottom: '30', left: '40' }} */}
  //       {/*     onChange={() => {}} */}
  //       {/*   /> */}
  //       {/* </Space> */}
  //       <Space size={10} direction="vertical">
  //         <SettingWrapper vertical label="Background Image" containerStyle={{ gap: '20px' }}>
  //           <UploadImage
  //             // isInputMode={false}
  //             domainMedia={DOMAIN_MEDIA_SANDBOX}
  //             // extensions={['video/mp4']}
  //             slug="api/v1"
  //             // maxSize={200}
  //             labelButtonSelect="Select video from computer"
  //             labelHeadingModal="Video Selection"
  //             labelModalDelete="Delete Video"
  //             searchPlaceholder="Search video..."
  //             paramConfigs={{
  //               token: '5474r2x214z2a4b403e4y4a4q436l4s5s2q4x2r474v5',
  //               userId: '**********',
  //               accountId: '**********',
  //             }}
  //             // mode="video"
  //             iconName="video"
  //             // title="Background Image"
  //             selectedImage={{
  //               url: 'https://th.bing.com/th?id=OSK.********************************&w=148&h=148&c=7&o=6&dpr=2&pid=SANGAM',
  //             }}
  //             onChangeImage={image =>
  //               // onChangeSettings('backgroundImageObj', {
  //               //   name: image.name,
  //               //   previewUrl: image.url,
  //               // })
  //               {}
  //             }
  //             onRemoveImage={() => {
  //               // onChangeSettings('backgroundImageObj', {
  //               //   name: '',
  //               //   previewUrl: '',
  //               // });
  //             }}
  //           />
  //         </SettingWrapper>
  //         {/* <SettingWrapper vertical label="Icon"> */}
  //         {/*   <IconSelection */}
  //         {/*     isOpen={false} */}
  //         {/*     labelHeadingModal="Toggle Selection" */}
  //         {/*     searchPlaceholder="Search for toggle..." */}
  //         {/*     icon={icon} */}
  //         {/*     onChange={handleSetIcon} */}
  //         {/*     iconTypes={['cus', 'font-awesome']} */}
  //         {/*     onChangeSvg={value => { */}
  //         {/*       console.log('onChangeSvg', value); */}
  //         {/*     }} */}
  //         {/*   /> */}
  //         {/* </SettingWrapper> */}
  //       </Space>
  //     </div>
  //   </ConfigProvider>
  // );
  // ------------------------ Media template components test end -------------------------

  // return (
  //   <div style={{ width: '500px', margin: '32px' }}>
  //     <GradientSetting onChange={e => console.log('gradient change')} />
  //     <br />
  //     <br />
  //     <br />
  //     <br />
  //     <Slider
  //       className="test-classname"
  //       min={-10}
  //       max={10}
  //       onChange={e => console.log('slider change', e)}
  //     />
  //     <br />
  //     <br />
  //     <Rate />
  //     <br />
  //     <br />
  //     <Slider className="test-classname" />
  //   </div>
  // );

  // ------------------------ Template Listing test -------------------------
  // return (
  //   <div
  //     style={{
  //       height: '50vh',
  //     }}
  //   >
  //     <TemplateListing
  //       templatesProps={{
  //         items: templateItems,
  //         loading: isLoadingTemplateList,
  //         onLoadMoreTemplates,
  //       }}
  //       categoryListingProps={{
  //         loading: isLoadingCategoryList,
  //         items: categoryItems,
  //         checkedCategories,
  //         openKeys: openCategoryKeys,
  //         onOpenChange: onChangeOpenCategoryKeys,
  //         onMenuChange: onChangeCheckedCategories,
  //       }}
  //       templateItemProps={{
  //         removeModalProps: {
  //           // onOk
  //           onOk: onRemoveObjectTemplate,
  //         },
  //       }}
  //     />
  //   </div>
  // );
};

const container = document.getElementById('root');
const root = createRoot(container!);

root.render(
  <QueryClientProviderAntsomiUI client={queryClientAntsomiUI}>
    <ConfigProvider locale="en" appConfig={appConfig}>
      <App />
    </ConfigProvider>
  </QueryClientProviderAntsomiUI>,
);
