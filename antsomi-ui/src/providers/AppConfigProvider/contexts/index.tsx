// Libraries
import React, { useState } from 'react';
import { createContext } from 'use-context-selector';

// Types
import { PayloadInfo, useGetLoggedUser } from '../../..';
import { TEnv } from '@antscorp/antsomi-ui/es/types/config';

// Components
import { Spin } from '@antscorp/antsomi-ui/es/components';

// Hooks
import { useDeepCompareEffect, useDeepCompareMemo } from '@antscorp/antsomi-ui/es/hooks';
import { useGetUserInfo } from '@antscorp/antsomi-ui/es/queries/User/userGetUserInfo';

export interface AppConfigProps {
  env?: TEnv;
  auth?: Omit<PayloadInfo, 'url'>;
  appCode?: string;
  menuCode?: string;
  languageCode?: string;
  urlLogout?: string;

  /** Domain */
  permissionDomain?: string;
  iamDomain?: string;

  setAppConfig: React.Dispatch<React.SetStateAction<AppConfigProviderProps>>;
}

export interface AppConfigProviderProps extends Omit<AppConfigProps, 'setAppConfig'> {}

const initialContext: AppConfigProps = { setAppConfig: () => {} };

const AppConfigContext = createContext<AppConfigProps>(initialContext);

const AppConfigProvider: React.FC<
  React.PropsWithChildren<{
    value?: AppConfigProviderProps;
  }>
> = props => {
  const { value = initialContext, children } = props;
  const [appConfig, setAppConfig] = useState<AppConfigProviderProps>(value);

  // Effects
  useDeepCompareEffect(() => {
    setAppConfig(value);
  }, [value, appConfig]);

  const mergedValue: AppConfigProps = useDeepCompareMemo(
    () => ({
      ...appConfig,
      setAppConfig,
    }),
    [appConfig],
  );

  const { isLoading } = useGetUserInfo(mergedValue);
  useGetLoggedUser({
    args: {
      auth: mergedValue.auth,
    },
  });

  return (
    <AppConfigContext.Provider value={mergedValue}>
      {isLoading ? <Spin indicatorSize={24} fullscreen /> : children}
    </AppConfigContext.Provider>
  );
};

export { AppConfigContext, AppConfigProvider };
