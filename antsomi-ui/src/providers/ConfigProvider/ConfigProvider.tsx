// Libraries
import { StyleProvider } from '@ant-design/cssinjs';
import { ConfigProvider as AntdConfigProvider, App } from 'antd';
import { ConfigProviderProps as AntdConfigProviderProps } from 'antd/es/config-provider';
import dayjs from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import relativeTime from 'dayjs/plugin/relativeTime';
import weekday from 'dayjs/plugin/weekday';
import React, { ReactNode, useEffect, useRef } from 'react';
/** dayjs plugins for timezone */
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

// Constants
import { ANTSOMI_COMPONENT_PREFIX_CLS, THEME } from '@antscorp/antsomi-ui/es/constants';

// Style
import '@antscorp/antsomi-ui/es/assets/css/main.scss';
import { GlobalStyle } from './GlobalStyle';

// Types
import { TLocale } from '@antscorp/antsomi-ui/es/types';

// Initialize languages
// import '@antscorp/antsomi-ui/es/locales/i18n';
import i18next from '@antscorp/antsomi-ui/es/locales/i18n';
import 'animate.css';
import { AppConfigProvider, AppConfigProviderProps } from '../AppConfigProvider';

// Hooks
import { useMutationObserver } from '../../hooks';
import { QueryDevtoolsAntsomiUI } from '../../queries';

// Providers
// import {
//   QueryClientProviderAntsomiUI,
//   QueryDevtoolsAntsomiUI,
//   queryClientAntsomiUI,
// } from '@antscorp/antsomi-ui/es/queries';

// NOTE: HOT fix DatePicker Advanced
dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(relativeTime);

interface ConfigProviderProps extends Omit<AntdConfigProviderProps, 'locale'> {
  children?: ReactNode;
  locale?: TLocale;
  appConfig?: AppConfigProviderProps;
}

const ConfigProvider: React.FC<ConfigProviderProps> = props => {
  // Props
  const {
    children,
    locale,
    appConfig,
    theme = THEME,
    prefixCls = ANTSOMI_COMPONENT_PREFIX_CLS,
    ...restOfProps
  } = props;

  // Refs
  const ref = useRef<HTMLDivElement>(null);

  // Handlers
  const handleScrollAction = (elements: Element[]) => {
    let timer: any = null;
    elements?.forEach(element => {
      element.addEventListener(
        'scroll',
        () => {
          if (timer !== null) {
            if (!element.classList.contains('on-scroll')) {
              element.classList.add('on-scroll');
            }
            clearTimeout(timer);
          }

          timer = setTimeout(() => {
            if (element.classList.contains('on-scroll')) {
              element.classList.remove('on-scroll');
            }
          }, 500);
        },
        true,
      );
    });
  };

  // Hooks
  useMutationObserver(
    mutationsList => {
      handleScrollAction(
        mutationsList
          ?.filter(item => (item.target as Element)?.classList?.contains('antsomi-scroll-box'))
          ?.map(item => item.target as Element),
      );
    },
    ref,
    {
      attributes: true,
      childList: true,
      subtree: true,
      characterData: true,
      attributeOldValue: true,
      characterDataOldValue: true,
    },
  );

  // Effects
  // useEffect(() => {
  //   if (locale) {
  //     i18next.changeLanguage(locale);
  //   }
  // }, [locale]);

  useEffect(() => {
    const containers = document.getElementsByClassName('antsomi-scroll-box');
    handleScrollAction(Array.from(containers));
  }, []);

  return (
    <AntdConfigProvider theme={theme} prefixCls={prefixCls} {...restOfProps}>
      <AppConfigProvider value={appConfig}>
        <div ref={ref}>
          <App>
            {/* <QueryClientProviderAntsomiUI client={queryClientAntsomiUI}> */}
            <GlobalStyle />
            <StyleProvider hashPriority="high">{children}</StyleProvider>
            <QueryDevtoolsAntsomiUI position="bottom-right" />
            {/* </QueryClientProviderAntsomiUI> */}
          </App>
        </div>
      </AppConfigProvider>
    </AntdConfigProvider>
  );
};

export default ConfigProvider;
