// Libraries
import React from 'react';
import { Global, css } from '@emotion/react';

// Constants
import { THEME, globalToken } from '@antscorp/antsomi-ui/es/constants';

// FontAwesome
import { library } from '@fortawesome/fontawesome-svg-core';
import { fas } from '@fortawesome/free-solid-svg-icons';
import { far } from '@fortawesome/free-regular-svg-icons';
import { fab } from '@fortawesome/free-brands-svg-icons';

library.add(fas, far, fab);

interface GlobalStyleProps {}

const { accent7, gray5, scrollBarSize, bw3, bw4, bw5 } = THEME.token || {};

export const GlobalStyle: React.FC<GlobalStyleProps> = () => (
  <Global
    styles={css`
      @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&display=swap');

      :root {
        --divider-1: ${bw3};
        --divider-2: ${bw4};
        --divider-3: ${bw5};
      }

      *,
      *:before,
      *:after {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
      }

      hr {
        border-color: var(--divider-1);
      }

      .rc-virtual-list-scrollbar {
        width: 4px !important;
        height: 4px !important;
      }

      /* Custom */
      .antsomi-scrollbar-hidden {
        -ms-overflow-style: none; /* for Internet Explorer, Edge */
        scrollbar-width: none; /* for Firefox */

        &::-webkit-scrollbar {
          display: none;
        }
      }

      /* width */
      ::-webkit-scrollbar {
        background: transparent !important;
        width: ${scrollBarSize} !important;
        height: ${scrollBarSize} !important;
      }

      /* Track */
      ::-webkit-scrollbar-track {
        background: transparent !important;
      }

      /* Handle */
      ::-webkit-scrollbar-thumb {
        background-color: transparent;
        border-radius: 4px;
      }

      *:hover {
        &::-webkit-scrollbar-thumb {
          background-color: ${gray5} !important;
        }
      }

      .rc-virtual-list {
        .rc-virtual-list-scrollbar-vertical {
          width: ${scrollBarSize} !important;
        }

        .rc-virtual-list-scrollbar-thumb {
          background: ${accent7} !important;
        }
      }

      .custom-scrollbars {
        &:hover {
          .scrollbar-track {
            opacity: 1 !important;
          }
        }
      }

      .antsomi-scroll-box {
        overflow-y: auto;
        scrollbar-gutter: stable !important;
        margin-right: -${scrollBarSize} !important;

        &.on-scroll {
          ::-webkit-scrollbar-thumb {
            display: block !important;
          }
        }

        .scroll-content {
          margin-right: -${scrollBarSize} !important;
        }

        ::-webkit-scrollbar {
          width: ${scrollBarSize} !important;
          height: ${scrollBarSize} !important;
          background-color: transparent !important;
        }

        ::-webkit-scrollbar-track {
          background-color: transparent !important;
        }

        ::-webkit-scrollbar-thumb {
          background-color: ${gray5} !important;
          border-radius: 15px !important;
          display: none !important;

          &:hover {
            display: block !important;
          }
        }
      }

      /* Tooltip */
      .antsomi-tooltip {
        .antsomi-tooltip-content {
          .antsomi-tooltip-inner {
            a {
              color: inherit;
              font-weight: bold;
            }
          }
        }
      }

      /* Button */
      .antsomi-btn {
        font-weight: 700 !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 8px;

        i {
          font-size: 20px;
        }
      }

      .antsomi-btn-default {
        &:not(:disabled):hover {
          border-color: ${THEME.token?.blue3} !important;
          background-color: ${THEME.token?.blue} !important;
        }
        &:not(:disabled).antsomi-btn-default-active {
          border-color: ${THEME.token?.colorPrimary} !important;
          background-color: ${THEME.token?.blue1_1} !important;
        }

        &.antsomi-btn-dangerous {
          border-color: ${THEME.token?.red2} !important;

          &:not(:disabled):hover {
            border-color: ${THEME.token?.red3} !important;
            background-color: ${THEME.token?.red} !important;
          }
        }

        &.antsomi-btn-icon-only {
          width: 36px !important;
          height: 36px !important;
        }

        &.antsomi-btn-icon-only.antsomi-btn-sm {
          /* Important to override the above code before */
          width: 24px !important;
          height: 24px !important;
        }
      }

      .antsomi-btn-text:disabled,
      .antsomi-btn-link:disabled {
        color: ${THEME.token?.bw6} !important;
      }

      /* .antsomi-btn-primary:disabled, */
      .antsomi-btn-default:disabled,
      .antsomi-btn-dashed:disabled,
      .antsomi-btn-disabled,
      .antsomi-btn-default.antsomi-btn-dangerous:disabled,
      .antsomi-btn-primary.antsomi-btn-dangerous:disabled {
        border-color: ${THEME.token?.bw4} !important;
      }

      .antsomi-btn-default:disabled,
      .antsomi-btn-dashed:disabled,
      .antsomi-btn-disabled,
      .antsomi-btn-default.antsomi-btn-dangerous:disabled {
        color: ${THEME.token?.bw6} !important;
        background-color: ${THEME.token?.bw2} !important;
      }

      // NOTE: Just hot fix for easy looking
      .antsomi-btn-primary.antsomi-btn-compact-item.antsomi-btn-compact-first-item {
        padding-right: 6px !important;
      }
      .antsomi-btn-compact-item.antsomi-btn-primary.antsomi-btn-compact-last-item {
        width: fit-content;
        //padding: 0px !important;
        padding: 0px 4px !important;
        &::before {
          content: '';
          position: absolute;
          left: 0px;
          background-color: ${globalToken?.blue5} !important;
          top: auto !important;
          height: 80% !important;
          width: 1px !important;
        }

        &:disabled {
          &::before {
            background-color: ${globalToken?.bw4} !important;
          }
        }

        &:not(:disabled) {
          &:hover {
            &::before {
              opacity: 0;
            }
          }
        }
      }

      /* Input */
      .antsomi-input,
      .antsomi-input-affix-wrapper {
        border-width: 0 0 1px 0 !important;
        box-shadow: none !important;

        &:hover {
          background-color: ${THEME.token?.blue};
        }
      }

      .antsomi-input-disabled {
        border-color: ${THEME.token?.bw3} !important;
        background-color: ${THEME.token?.bw0} !important;
      }

      .antsomi-input {
        &:focus {
          background-color: ${THEME.token?.bw0};
        }
      }

      .antsomi-input-affix-wrapper {
        &:hover {
          > input.antsomi-input {
            background-color: ${THEME.token?.blue};
          }
        }

        &.antsomi-input-affix-wrapper-focused {
          background-color: ${THEME.token?.bw0};

          > input.antsomi-input {
            background-color: ${THEME.token?.bw0};
          }
        }
      }

      .antsomi-input-affix-wrapper-disabled {
        background-color: ${THEME.token?.bw0} !important;
        color: ${THEME.token?.bw8} !important;
        border-color: ${THEME.token?.bw3} !important;
      }

      .antsomi-input-status-error:not(.antsomi-input-disabled):not(
          .antsomi-input-borderless
        ).antsomi-input {
        &:hover {
          background-color: ${THEME.token?.red};
        }
      }

      .antsomi-input-status-warning:not(.antsomi-input-disabled):not(
          .antsomi-input-borderless
        ).antsomi-input {
        &:hover {
          background-color: ${THEME.token?.gold1};
        }
      }

      .antsomi-input-affix-wrapper-status-error:not(.antsomi-input-affix-wrapper-disabled):not(
          .antsomi-input-affix-wrapper-borderless
        ).antsomi-input-affix-wrapper {
        &:hover,
        &:hover > input.antsomi-input {
          background-color: ${THEME.token?.red};
        }
      }

      .antsomi-input-affix-wrapper-status-warning:not(.antsomi-input-affix-wrapper-disabled):not(
          .antsomi-input-affix-wrapper-borderless
        ).antsomi-input-affix-wrapper {
        &:hover,
        &:hover > input.antsomi-input {
          background-color: ${THEME.token?.gold1};
        }
      }

      /* For custom search input */
      .antsomi-input-affix-wrapper.antsomi-search-input:hover > input.antsomi-input {
        background-color: transparent;
      }

      // Select
      .antsomi-select .antsomi-select-arrow {
        right: 5px !important;
      }

      .antsomi-select:not(.antsomi-select-disabled):not(.antsomi-select-customize-input):not(
          .antsomi-pagination-size-changer
        ):hover
        .antsomi-select-selector {
        border-color: ${THEME.token?.blue1} !important;
        background-color: ${THEME.token?.blue} !important;
      }

      .antsomi-select-disabled.antsomi-select:not(.antsomi-select-customize-input)
        .antsomi-select-selector {
        border-color: ${THEME.token?.bw4};
      }

      .antsomi-select-disabled.antsomi-select:not(.antsomi-select-customize-input) .antsomi-tag {
        background-color: ${THEME.token?.bw4} !important;
      }

      .antsomi-select-selector {
        border-width: 0 0 1px 0 !important;
        box-shadow: none !important;
      }

      .antsomi-select-auto-complete {
        .antsomi-select-selector {
          border: none !important;
        }
      }

      .antsomi-select-dropdown {
        padding: 8px 0 !important;
        z-index: 3002 !important;
      }

      .antsomi-select-selection-overflow {
        gap: 5px;
      }

      // Dropdown
      .antsomi-dropdown-menu {
        padding: 8px 0 !important;
      }

      // Input Number
      .antsomi-input-number,
      .antsomi-input-number-affix-wrapper {
        border-width: 0 0 1px 0 !important;
        box-shadow: none !important;

        &.--show-handler {
          .antsomi-input-number-handler-wrap {
            opacity: 1;
          }
        }
      }

      .antsomi-input-number-group-addon {
        border-width: 0 0 1px 0 !important;
        background-color: ${THEME.token?.bw0} !important;
        box-shadow: none !important;
      }

      .antsomi-input-number:hover {
        background-color: ${THEME.token?.blue};

        .antsomi-input-number-handler {
          background-color: ${THEME.token?.blue};
        }
      }

      .antsomi-input-number:focus,
      .antsomi-input-number-focused {
        border-color: ${THEME.token?.colorPrimary} !important;
      }

      .antsomi-input-number-handler {
        border: none !important;

        &:hover i {
          color: ${THEME.token?.colorPrimary};
        }
      }

      // DatePicker
      .antsomi-picker {
        border-width: 0 0 1px 0 !important;
        box-shadow: none !important;
      }

      .antsomi-picker:hover,
      .antsomi-picker-focused {
        background-color: ${THEME.token?.blue};
      }

      .antsomi-picker:not(.antsomi-picker-status-error) .antsomi-picker-suffix {
        color: ${THEME.token?.bw10};
      }

      .antsomi-picker:not(.antsomi-picker-disabled).antsomi-picker-status-error:not(
          [disabled]
        ):hover {
        background-color: ${THEME.token?.red} !important;
      }

      // Custom
      .date-time-picker__popup-content {
        width: 230px !important;
      }

      .antsomi-picker-dropdown__advanced {
        &.antsomi-picker-dropdown > .antsomi-picker-panel-container {
          border-radius: 10px;
        }

        > .antsomi-picker-panel-container > .antsomi-picker-panel-layout > .antsomi-picker-panel {
          flex-direction: column-reverse;
          padding-bottom: 60px;

          .antsomi-picker-date-panel,
          .antsomi-picker-quarter-panel {
            width: 280px !important;

            .antsomi-picker-cell {
              pointer-events: none !important;
            }
          }

          /* .antsomi-picker-date-panel {
            position: relative;
          } */

          /* .antsomi-picker-date-panel::before {
            content: 'Hours';
            position: absolute;
            top: 0;
            right: 0;
            transform: translateX(100%);
            width: 61px;
            font-weight: bold;
            border-right: 1px solid rgba(5, 5, 5, 0.06);
            height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
          } */

          .antsomi-picker-time-panel {
            .antsomi-picker-time-panel-column:after {
              height: 8px;
            }

            .antsomi-picker-time-panel-cell-selected {
              background-color: ${THEME.token?.colorPrimary};
              color: #fff;
            }
            .antsomi-picker-time-panel-cell-selected:hover {
              background-color: ${THEME.token?.colorPrimary};
            }

            .antsomi-picker-cell-inner {
              text-align: center;
              cursor: pointer;
            }
            .antsomi-picker-cell-inner:hover {
              background: rgba(34, 34, 34, 0.04);
            }

            .antsomi-picker-header {
              display: none;
            }

            .antsomi-picker-time-panel-column {
              width: 60px;
              margin-top: 38px;
              /* margin-top: 0; */
              scrollbar-width: unset;
              scrollbar-color: unset;

              &::-webkit-scrollbar {
                width: 6px;
                background: transparent;
              }
              &::-webkit-scrollbar-track {
                /* margin-top: 38px; */
                border-radius: 10px;
              }
            }

            /* ul.antsomi-picker-time-panel-column::before {
              content: '';
              position: absolute;
              background: red;
              top: 0;
              right: 0;
              width: 1px;
              bottom: 0;
            } */

            /* .antsomi-picker-time-panel-column::before {
              content: 'Hours';
              position: sticky;
              display: block;
              top: 0;
              height: 38px;
              line-height: 38px;
              text-align: center;
              font-weight: bold;
              background-color: #fff;
            }

            .antsomi-picker-time-panel-column:nth-child(1)::before {
              content: 'Hours';
            }
            .antsomi-picker-time-panel-column:nth-child(2)::before {
              content: 'Minutes';
            }
            .antsomi-picker-time-panel-column:nth-child(3)::before {
              content: 'Seconds';
            } */

            /* .antsomi-picker-content {
              position: relative;

              .antsomi-picker-time-panel-column {
                margin-top: 38px;
                width: 60px;
              }
            } */

            .antsomi-picker-content {
              position: relative;
            }

            /* .antsomi-picker-content::after,
            .antsomi-picker-content::before {
              content: 'Minutes';
              position: absolute;
              top: 0;
              left: 61px;
              width: 60px;
              height: 38px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: bold;
            } */

            /* .antsomi-picker-content::before {
              border-right: 1px solid rgba(5, 5, 5, 0.06);
            } */
            /* .antsomi-picker-content::after {
              content: 'Seconds';
              left: 120px;
            } */
          }

          /* Custom css scrollbar */
          ul {
            padding-left: 6px;
            scrollbar-gutter: stable;
          }
        }

        &.hide-picker-header {
          .antsomi-picker-month-panel,
          .antsomi-picker-quarter-panel {
            .antsomi-picker-header {
              display: none;
            }
          }
        }

        &.only-show-time-picker {
          .antsomi-picker-date-panel {
            display: none;
          }
          .antsomi-picker-time-panel {
            border: none;
            /* NOTE: production prod show unexpected border */
            border-inline-start: none !important;
          }
          > .antsomi-picker-panel-container > .antsomi-picker-panel-layout > .antsomi-picker-panel {
            width: 250px;
            .antsomi-picker-datetime-panel {
              justify-content: center;
            }

            ul {
              ::-webkit-scrollbar {
                width: 0;
              }
              padding: 0;
            }

            .antsomi-picker-content::after {
              display: none;
            }
            .antsomi-picker-content::before {
              content: 'Hours';
              border: none;
              left: 0;

              position: absolute;
              top: 0;
              /* left: 61px; */
              width: 60px;
              height: 38px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: bold;
            }
          }
          &.is-minutes-picker {
            > .antsomi-picker-panel-container
              > .antsomi-picker-panel-layout
              > .antsomi-picker-panel {
              .antsomi-picker-content::before {
                content: 'Minutes';
              }
            }
          }
        }

        &.--error
          > .antsomi-picker-panel-container
          > .antsomi-picker-panel-layout
          > .antsomi-picker-panel {
          padding-bottom: 80px;
        }

        &.antsomi-picker-dropdown {
          .antsomi-picker-footer-extra {
            padding: 0 !important;
          }

          /*
          * NOTE: UI broken when add showTime props to <DatePicker />
          * Unexpected header control (comment code below to see bug)
          * Temporary solution: hide the verbose element
          */
          .antsomi-picker-ranges {
            display: none;
          }
        }
      }

      .antsomi-advanced-picker-container .input__wrapper {
        display: inline-block;
      }

      // Slider
      .antsomi-slider {
        margin: 0 !important;

        &.antsomi-slider-horizontal {
          .antsomi-slider-rail,
          .antsomi-slider-step {
            height: 4px;
            width: 100%;
          }

          .antsomi-slider-rail {
            height: 4px;
          }
        }

        .antsomi-slider-rail,
        .antsomi-slider-step {
          position: absolute !important;
        }

        .antsomi-slider-rail {
          border-radius: 2px !important;
          transition: background-color 0.3s !important;
          background-color: ${THEME.token?.accent1} !important;
        }

        .antsomi-slider-track {
          position: absolute !important;
          border-radius: 2px !important;
          transition: background-color 0.3s !important;
          background-color: ${THEME.token?.colorPrimary} !important;
        }

        .antsomi-slider-step {
          background: 0 0 !important;
          pointer-events: none !important;
        }

        .antsomi-slider-handle {
          position: absolute !important;
          width: 14px !important;
          height: 14px !important;
          margin-top: -2px;
          background-color: #fff;
          border-radius: 50%;
          box-shadow: rgba(0, 0, 0, 0.3) 0px 2px 2px 0px;
          cursor: pointer;
          transition:
            border-color 0.3s,
            box-shadow 0.6s,
            transform 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28) !important;
        }

        .antsomi-slider-handle::before,
        .antsomi-slider-handle::after,
        .antsomi-slider-handle:hover::after,
        .antsomi-slider-handle:focus::before,
        .antsomi-slider-handle:focus::after {
          width: 14px !important;
          height: 14px !important;
          box-shadow: none !important;
          inset-block-start: 0 !important;
          inset-inline-start: 0 !important;
        }

        .antsomi-slider-handle::after {
          background-color: ${THEME.token?.colorPrimary} !important;
        }

        &:hover {
          .antsomi-slider-track {
            background-color: ${THEME.token?.colorPrimary} !important;
          }

          .antsomi-slider-rail {
            background-color: ${THEME.token?.accent1} !important;
          }

          .antsomi-slider-handle::after {
            box-shadow: none !important;
          }

          .antsomi-slider-handle {
            border-color: ${THEME.token?.colorPrimary} !important;
          }
        }

        &.antsomi-slider-with-marks {
          margin-bottom: 0 !important;
        }

        &.antsomi-slider-disabled {
          .antsomi-slider-handle {
            background-color: ${THEME.token?.accent2} !important;
            border-color: ${THEME.token?.accent2} !important;
          }
        }
      }

      /* Radio */
      .antsomi-radio-group-outline {
        .antsomi-radio-button-wrapper {
          display: inline-flex;
          align-items: center;
          height: 30px;
          padding-left: 20px;
          padding-right: 20px;
          font-family: 'Roboto';
          color: ${THEME.token?.colorTextBase};
          font-size: ${THEME.token?.fontSize}px;

          &:focus-within {
            --tw-shadow: 0 0 #0000;
            --tw-shadow-colored: 0 0 #0000;
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000),
              var(--tw-shadow);
          }

          &:first-child {
            border-top-left-radius: ${THEME.token?.borderRadius}px;
            border-bottom-left-radius: ${THEME.token?.borderRadius}px;
          }

          &:last-child {
            border-top-right-radius: ${THEME.token?.borderRadius}px;
            border-bottom-right-radius: ${THEME.token?.borderRadius}px;
          }

          &.antsomi-radio-button-wrapper-checked {
            border-color: ${THEME.token?.accent1};
            background-color: ${THEME.token?.colorTextActive};
            color: ${THEME.token?.colorPrimary};
            font-weight: 700;

            &::before {
              background-color: ${THEME.token?.accent1};
            }
          }
        }

        .antsomi-radio-wrapper {
          font-family: 'Roboto';
          font-size: ${THEME.token?.fontSize}px;
          margin-right: 0;

          .antsomi-radio-disabled {
            &:hover {
              ::after {
                border-color: ${THEME.token?.gray5} !important;
              }
            }

            .antsomi-radio-inner {
              border-color: ${THEME.token?.gray5} !important;

              &::after {
                background-color: ${THEME.token?.colorTextDisabled} !important;
              }
            }
          }

          .antsomi-radio-inner {
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .antsomi-radio-checked .antsomi-radio-inner::after {
            transform: scale(0.6);
          }

          .antsomi-radio-inner::after {
            flex-shrink: 0;
            position: unset !important;
            transition: none !important;
            transform: scale(0.6);
            margin-block-start: 0px;
            margin-inline-start: 0px;
          }

          .antsomi-radio {
            .antsomi-radio-inner {
              width: 16px;
              height: 16px;
              border-color: ${THEME.token?.colorPrimary};
              border-width: 2px;
              background-color: ${THEME.token?.bw0};

              &::after {
                background-color: ${THEME.token?.colorPrimary};
              }
            }
          }

          &:not(:last-child) {
            margin-right: 30px;
          }
        }
      }

      /* Checkbox */
      .antsomi-checkbox-wrapper {
        .antsomi-checkbox-indeterminate {
          .antsomi-checkbox-inner::after {
            width: 10px !important;
            height: 2px !important;
          }
        }

        .antsomi-checkbox-indeterminate:not(.antsomi-checkbox-disabled) {
          .antsomi-checkbox-inner {
            background-color: ${THEME.token?.colorPrimary} !important;
            &::after {
              background-color: ${THEME.token?.bw0} !important;
            }
          }
        }
      }

      /* Modal Close */
      .antsomi-modal-root .antsomi-modal-wrap {
        z-index: 3001;
      }

      .antsomi-modal .antsomi-modal-close {
        width: unset !important;
        height: unset !important;
        top: 0px !important;
        right: 0 !important;

        .antsomi-modal-close-x {
          padding: 20px;
        }
      }
      .antsomi-modal-content {
        .antsomi-modal-header {
          .antsomi-modal-title {
            font-weight: 500;
            font-size: 16px;
            line-height: 22px;
            word-wrap: break-word;
          }
        }

        .antsomi-modal-body {
          p {
            margin: 0;
          }
          .antsomi-modal-confirm-body-wrapper {
            padding: 20px;

            .antsomi-modal-confirm-body {
              display: flex;

              /* Style for icon */
              > i {
                color: ${THEME.token?.colorPrimary};
                margin-right: 13px;
              }
            }

            .antsomi-modal-confirm-body:has(i) ~ .antsomi-modal-confirm-btns {
              padding-left: 30px;
            }

            .antsomi-modal-confirm-btns {
              display: flex;
              flex-direction: row-reverse;
              justify-content: start;
              gap: 8px;

              .antsomi-btn {
                margin: 0px;
              }
            }
          }
        }
        .antsomi-modal-footer {
          margin-top: 0px;
          padding: 15px !important;
          text-align: right;
          background: transparent;
          border-top: 1px solid #f0f0f0 !important;
          border-radius: 0 0 2px 2px;
        }
      }
      /* Override style for Modal confirm in Editor Tab */
      .editor-tab__modal-confirm .antsomi-modal-content .antsomi-modal-body {
        .antsomi-modal-confirm-body-wrapper {
          padding: 0;

          .antsomi-modal-confirm-body {
            padding: 20px 20px 0 20px;
          }
          .antsomi-modal-confirm-btns {
            padding: 12px 20px 20px;
            border-top: 1px solid ${THEME.token?.bw4};
          }
        }
      }

      /* Message Notification */
      .ant-message {
        z-index: 999999 !important;
      }

      /* Collapse */
      .antsomi-collapse-header {
        font-size: 14px;
      }

      .antsomi-collapse {
        &.antsomi-collapse-borderless {
          border-bottom: 1px solid ${THEME.token?.gray5};
        }

        > .antsomi-collapse-item > .antsomi-collapse-header .antsomi-collapse-arrow {
          transition: all 200ms;
        }
      }

      /* Tabs */
      .antsomi-tabs {
        .antsomi-tabs-tab {
          font-weight: bold;

          &:not(.antsomi-tabs-tab-active):hover {
            background-color: ${THEME.token?.blue};
          }
        }
        .antsomi-tabs-tab-active {
          z-index: 2;
        }
        .antsomi-tabs-nav::before {
          z-index: 1;
        }
      }

      /* Form */
      .antsomi-form {
        .antsomi-form-item {
          .antsomi-form-item-label {
            > label.antsomi-form-item-required:not(.antsomi-form-item-required-mark-optional) {
              &::before {
                position: absolute;
                right: 0px;
                font-family: Roboto;
              }
            }
          }
        }
      }

      /* Popover */
      .antsomi-popover {
        &.no-padding-content {
          .antsomi-popover-inner {
            padding: 0 !important;
          }
        }
        .antsomi-popover-inner {
          overflow: hidden;
        }

        &.antsomi-emoji-popover .antsomi-popover-inner {
          padding: 15px;
          width: 530px;
          height: 275px;
        }
      }

      /* Menu */
      .antsomi-menu {
        .antsomi-menu-submenu.antsomi-menu-submenu-selected {
          .antsomi-menu-item.antsomi-menu-item-selected {
            background-color: ${THEME.token?.blue};
          }
        }
      }

      .antsomi-typography {
        &.antsomi-typography-danger,
        &.antsomi-typography-warning {
          font-size: 11px;
        }
      }

      /* Tree */
      .antsomi-tree-list {
        .antsomi-tree-treenode {
          align-items: center !important;

          .antsomi-tree-checkbox {
            margin: 3px 0px;
          }
        }

        > .antsomi-tree-list-scrollbar-vertical {
          width: ${scrollBarSize} !important;

          > .antsomi-tree-list-scrollbar-thumb {
            background: ${accent7} !important;
          }
        }
      }

      /* Checkbox */
      .antsomi-checkbox,
      .antsomi-tree-checkbox {
        &.antsomi-checkbox-disabled,
        &.antsomi-tree-checkbox-disabled {
          .antsomi-checkbox-inner,
          .antsomi-tree-checkbox-inner {
            background-color: ${globalToken?.bw0};
            border-color: ${globalToken?.bw5} !important;
          }

          &.antsomi-checkbox-checked,
          &.antsomi-tree-checkbox-checked {
            .antsomi-checkbox-inner,
            .antsomi-tree-checkbox-inner {
              background-color: ${globalToken?.bw5};

              &::after {
                border-color: ${globalToken?.bw0};
              }
            }
          }
        }
      }

      /* Tags */
      .antsomi-tag {
        padding-inline: 10px !important;
        align-items: center;
        display: flex !important;
        line-height: 1;
        min-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;

        .antsomi-tag-close-icon {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          right: 4px;
          opacity: 0;
        }

        &:hover {
          .antsomi-tag-close-icon {
            opacity: 1;
          }
        }
      }
    `}
  />
);
