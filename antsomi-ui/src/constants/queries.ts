export const QUERY_KEYS = {
  // Event attr
  GET_LIST_EVENT_ATTR: 'GET_LIST_EVENT_ATTR',
  GET_LIST_EVENT_BY_SOURCE: 'GET_LIST_EVENT_BY_SOURCE',
  GET_LIST_SOURCE_BY_EVENT: 'GET_SOURCE_BY_EVENT',
  GET_LIST_ALL_EVENT: 'GET_LIST_ALL_EVENT',

  // BUSINESS OBJECT
  GET_LIST_BO: 'GET_LIST_BO',
  GET_EVENT_ATTRIBUTE_BO: 'GET_EVENT_ATTRIBUTE_BO',
  GET_BO_DETAIL: 'GET_BO_DETAIL',
  GET_DATA_TABLE_BO: 'GET_DATA_TABLE_BO',

  // Promotion Code Attr
  GET_LIST_PROMOTION_CODE_ATTR: 'GET_LIST_PROMOTION_CODE_ATTR',

  // Event tracking attributes
  GET_EVENT_TRACKING_ATTRIBUTES: 'GET_EVENT_TRACKING_ATTRIBUTES',

  // Dynamic content Attr
  GET_LIST_DYNAMIC_CONTENT_ATTR: 'GET_LIST_DYNAMIC_CONTENT_ATTR',

  // Event Pool
  GET_LIST_PROMOTION_POOL: 'GET_LIST_PROMOTION_POOL',

  // Custom Function
  GET_LIST_CUSTOM: 'GET_LIST_CUSTOM',
  GET_SAVE_CUSTOM_FUNCTION: 'GET_SAVE_CUSTOM_FUNCTION',

  // Template Listing
  GET_TEMPLATE_CATEGORY_LIST: 'GET_TEMPLATE_CATEGORY_LIST',
  GET_OBJECT_TEMPLATE_LIST: 'GET_OBJECT_TEMPLATE_LIST',
  GET_OBJECT_TEMPLATE_DETAIL: 'GET_OBJECT_TEMPLATE_DETAIL',
  GET_SAVE_AS_GALLERY_PERMISSION_EMAILS: 'GET_SAVE_AS_GALLERY_PERMISSION_EMAILS',

  // THIRD PARTY
  GET_LIST_FALLBACK_BO: 'GET_LIST_FALLBACK_BO',
  GET_LIST_SAVED_MEDIA: 'GET_LIST_SAVED_MEDIA',

  // Left menu
  GET_DASHBOARD: 'GET_DASHBOARD',
  GET_LIST_MENU: 'GET_LIST_MENU',
  GET_LIST_MENU_PERMISSION: 'GET_LIST_MENU_PERMISSION',
  GET_DESTINATION_CHANNEL: 'GET_DESTINATION_CHANNEL',

  // Account Listing
  GET_ACCOUNT_LISTING: 'GET_ACCOUNT_LISTING',
  GET_PERMISSION_ACCOUNT_LISTING: 'GET_PERMISSION_ACCOUNT_LISTING',
  GET_RECENT_ACCOUNT: 'GET_RECENT_ACCOUNT',
  UPDATE_RECENT_ACCOUNT: 'UPDATE_RECENT_ACCOUNT',
  CHECK_ACCOUNT_PERMISSION: 'CHECK_ACCOUNT_PERMISSION',

  // ABSTRACT
  GET_ABSTRACT_USERS: 'GET_ABSTRACT_USERS',

  // DATA TABLE
  GET_COLUMN_METRICS: 'GET_COLUMN_METRICS',
  GET_DATA_TABLE_LISTING: 'GET_DATA_TABLE_LISTING',
  GET_MODIFY_COLUMN_LIST: 'GET_MODIFY_COLUMN_LIST',
  GET_SAVED_FILTER_LIST: 'GET_SAVED_FILTER_LIST',
  GET_FILTER_METRIC_LIST: 'GET_FILTER_METRIC_LIST',
  GET_SEARCH_LIST: 'GET_SEARCH_LIST',
  GET_MATCHES_ANY_LIST: 'GET_MATCHES_ANY_LIST',
  GET_INFINITE_MATCHES_ANY_LIST: 'GET_INFINITE_MATCHES_ANY_LIST',

  // USER
  GET_LOGGED_USER: 'GET_LOGGED_USER',
};
