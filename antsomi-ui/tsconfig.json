{
  "compilerOptions": {
    "target": "ES2020" /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */,
    // "typeRoots": [
    //   "node_modules",
    //   "node_modules/@types",
    //   "src/types"
    // ] /* Specify multiple folders that act like `./node_modules/@types`. */,
    "esModuleInterop": true /* Emit additional JavaScript to ease support for importing CommonJS modules. This enables `allowSyntheticDefaultImports` for type compatibility. */,
    "forceConsistentCasingInFileNames": true /* Ensure that casing is correct in imports. */,
    /* Type Checking */
    "strict": true /* Enable all strict type-checking options. */,
    "skipLibCheck": true /* Skip type checking all .d.ts files. */,
    "types": ["vite/client", "vitest/globals"],
    "jsx": "react-jsx",
    "module": "ESNext",
    // "declaration": true,
    // "declarationDir": "dist/types",
    "isolatedModules": true,
    "sourceMap": true,
    "outDir": "dist",
    "moduleResolution": "bundler",
    "allowJs": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "noImplicitAny": false,
    "baseUrl": "./",
    "paths": {
      "@antscorp/antsomi-ui/es": ["./src"],
      "@antscorp/antsomi-ui/es/*": ["./src/*"]
    }
  },
  "exclude": [
    // "src/**/*.test.tsx",
    // "src/**/*.test.ts",
    // "src/**/*.test.jsx",
    // "src/**/*.test.js",
    // "src/**/*.stories.tsx",
    // "src/**/*.stories.ts",
    "src/stories",
    "scripts",
    "dist",
    "node_modules",
    "lib",
    "es"
  ],
  "include": ["src", "types", "vite.config.mts", "setupTests.ts"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
