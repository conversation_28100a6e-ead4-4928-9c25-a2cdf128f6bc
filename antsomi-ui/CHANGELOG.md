### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

#### [v2.0.63](https://bitbucket.org/git-vn/at.packages.frontend/compare/v2.0.63..v2.0.43)

- chore(package): bump version to 1.0.31 [`b509f0e`](https://bitbucket.org/git-vn/at.packages.frontend/commits/b509f0e34922527ae6d3c200c968799e0ef4f943)
- feat(DisplayFormat): create DisplayFormatSetting and DisplayFormatSelect components [`ffcd777`](https://bitbucket.org/git-vn/at.packages.frontend/commits/ffcd77745a9b929500535a1a9b0b658c1823baf7)
- feat(antsomi-ui): TagifyInput add tag short links v2 [`8a84711`](https://bitbucket.org/git-vn/at.packages.frontend/commits/8a84711a8dde4037d107ecda7e87d2529849bd8a)

#### [v2.0.43](https://bitbucket.org/git-vn/at.packages.frontend/compare/v2.0.43..v2.0.22)

> 26 December 2024

- fix(SelectAccount): add translate [`c2b94d5`](https://bitbucket.org/git-vn/at.packages.frontend/commits/c2b94d577b6027a322a0e309dcc6d8b91427a109)
- feat(antsomi-utils): add new package [`b872333`](https://bitbucket.org/git-vn/at.packages.frontend/commits/b8723338dc41f8302399e72e8d1843fa91dd2446)
- feat(antsomi-hooks): crafting new lib [`292f327`](https://bitbucket.org/git-vn/at.packages.frontend/commits/292f3271c05b82ce5315bd75726a621f33055fb9)

### [v2.0.22](https://bitbucket.org/git-vn/at.packages.frontend/compare/v2.0.22..v1.0.5)

> 19 January 2023

- update ticket packge [`13ab54f`](https://bitbucket.org/git-vn/at.packages.frontend/commits/13ab54fe43fb85000e9d6369cab438576da66bea)
- change-params-api [`b7dc0bc`](https://bitbucket.org/git-vn/at.packages.frontend/commits/b7dc0bc86bb903e66b9458089ece308a06f1d17d)
- fix-bug-packace-editor [`ac50d64`](https://bitbucket.org/git-vn/at.packages.frontend/commits/ac50d6461e18214281693d02847d3a61cc4cdb47)

#### [v1.0.5](https://bitbucket.org/git-vn/at.packages.frontend/compare/v1.0.5..v1.0.3)

> 17 January 2023

#### [v1.0.3](https://bitbucket.org/git-vn/at.packages.frontend/compare/v1.0.3..v1.0.2)

> 17 January 2023

#### v1.0.2

> 17 January 2023

- Sprint10/vinlt/feature/date advanced [`#24`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/24)
- Sprint10/sangndd/feature/extend value matches any [`#23`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/23)
- Sprint10/sangndd/feature/extend value matches any [`#22`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/22)
- [Scatter chart] Max X-axis giá trị bị lặp [`#20`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/20)
- Sprint8/vinlt/feature/show data label short [`#18`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/18)
- Fix duplicate axis max value [`#17`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/17)
- [Antalyser] Một số bug khi nhập axis giá trị thập phân [`#16`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/16)
- Sprint6/vinlt/fixbug/chart [`#15`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/15)
- - Axis allows input of decimal values [`#14`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/14)
- Sprint4/sangndd/feature/icons [`#12`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/12)
- Sprint4/sangndd/feature/icons [`#11`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/11)
- Sprint4/thanghn/fixbug/pivot alias border [`#10`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/10)
- fix scroll dimension and total column [`#9`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/9)
- [ANTALYSER][Filter][Khi filter filed có type date và semantic là "Month day" thì không load được danh sách các ngày trong tháng] [`#7`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/7)
- [ANTALYSER][Pivot chart][Tính năng Add border shadow chưa hoạt động] [`#8`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/8)
- Fix bug [ANTALYSER][Scatter chart][Label ở cột X và Y chưa đổi theo alias name đã change ở metric] [`#6`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/6)
- Fix bug [Pivot chart] Row color không hiển thị được khi chọn hiển thị data của metric là Bar [`#5`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/5)
- Fix bug package antscorp/charts [`#1`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/1)
- Fix bug package antscorp/chart [`#2`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/2)
- table component [`#1`](https://bitbucket.org/git-vn/at.packages.frontend/pull-requests/1)
- Update map indonesia [`fc94208`](https://bitbucket.org/git-vn/at.packages.frontend/commits/fc9420838b3f4087a5763017e9bf7e72ee8c5e31)
- Update geomap [`43f3098`](https://bitbucket.org/git-vn/at.packages.frontend/commits/43f3098506f30a215dc2b30734590e5c55f8b490)
- sync package master [`7476118`](https://bitbucket.org/git-vn/at.packages.frontend/commits/7476118de2fcfa1bd6b0a9e3c0f887b29fd149b6)
