// Libraries
import { Preview, StoryContext } from '@storybook/react';
import { themes } from '@storybook/theming';
import React from 'react';
import { BrowserRouter } from 'react-router-dom';

import { ConfigProvider, QueryClientProviderAntsomiUI, queryClientAntsomiUI } from '../src';

// Css
import '@antscorp/icons/main.css';
import './main.scss';
import { ENV } from '../src/config';

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    viewMode: 'docs',
    options: {
      storySort: {
        order: ['Home', ['Overview', 'Installation', 'Usage', 'Change Log'], 'Components'],
      },
    },
    docs: {
      theme: themes.light,
      source: {
        format: 'dedent',
        language: 'tsx',
        transform: (_code: string, storyContext: StoryContext) => {
          const originalSource = storyContext.parameters?.docs?.source?.originalSource;

          const regexDocs =
            /(?<=\/\* storybook-docs wrapper \*\/)[\s\S]*(?=\/\* storybook-docs wrapper \*\/.*)/m;

          const matchRegex = originalSource.match(regexDocs);
          if (!matchRegex) return null;

          const formattedRegex = matchRegex[0].trim();

          return `const App: React.FC = () => {\n${formattedRegex.includes('return') ? '' : '  return'}\n    ${formattedRegex}\n}`;
        },
      },
    },
    actions: { argTypesRegex: '^on(Click).*' },
  },

  decorators: [
    Story => (
      <QueryClientProviderAntsomiUI client={queryClientAntsomiUI}>
        <ConfigProvider
          locale="vi"
          appConfig={{
            env: ENV.SANDBOX_CDP,
            languageCode: 'EN',
            auth: {
              portalId: 33167,
              token: '5474r2x214z2a4a4u254y4u5s234p244s4v2x2b434t5',
              userId: '**********',
              accountId: '**********',
            },
          }}
        >
          <BrowserRouter>
            <Story />
          </BrowserRouter>
        </ConfigProvider>
      </QueryClientProviderAntsomiUI>
    ),
  ],

  tags: ['autodocs'],
};

export default preview;
